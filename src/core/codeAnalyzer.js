/*
 * @Date: 2025-06-07 10:06:12
 * @Description: 代码分析和生成工具
 */
const generator = require("@babel/generator");
const parser = require("@babel/parser");
const traverse = require("@babel/traverse");
const types = require("@babel/types");
const fs = require("fs");
const path = require("path");
const { promisify } = require('util');
const { uuidUtils } = require('../utils/uuidUtils');
const { fileManager } = require('../utils/fileManager');
const { logger } = require('../utils/logger');

// 将 fs 的异步方法转换为 Promise
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const appendFile = promisify(fs.appendFile);

/**
 * 代码分析器模块
 */
const codeAnalyzer = {
    /**
     * 获取解析器选项，根据版本配置不同的解析选项
     * @returns {Object} 解析器选项
     */
    getParserOptions() {
        const baseOptions = {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'asyncGenerators',
                'bigInt',
                'classProperties',
                'doExpressions',
                'dynamicImport',
                'exportDefaultFrom',
                'exportNamespaceFrom',
                'functionBind',
                'functionSent',
                'importMeta',
                'nullishCoalescingOperator',
                'numericSeparator',
                'objectRestSpread',
                'optionalCatchBinding',
                'optionalChaining',
                'throwExpressions',
                'topLevelAwait',
                'trailingFunctionCommas'
            ]
        };

        // 根据 Cocos Creator 版本添加特定的解析器插件
        if (global.cocosVersion === '3.8.x') {
            // 3.8.x 版本支持 TypeScript 和现代装饰器
            baseOptions.plugins.push('typescript');
            baseOptions.plugins.push('decorators');
            baseOptions.plugins.push('classStaticBlock');
            baseOptions.plugins.push('privateIn');
        } else {
            // 2.x 版本使用传统装饰器
            baseOptions.plugins.push('decorators-legacy');
        }

        return baseOptions;
    },

    /**
     * 分析编译源代码
     * @param {string} code 要分析的源代码
     * @param {string} filePath 文件路径（可选）
     * @returns {Promise<void>}
     */
    async analyze(code, filePath = 'main') {
        try {
            // 检查是否包含System.register模块
            if (code.includes('System.register')) {
                await this.extractSystemRegisterModules(code, filePath);
                return;
            }

            // 1. 解析代码为 AST，支持 TypeScript 和现代 JavaScript 特性
            const parserOptions = this.getParserOptions();
            const ast = parser.parse(code, parserOptions);
            const values = [];

            // 2. 定义访问者函数查找值，支持现代 JavaScript 和 TypeScript 模式
            const findValue = {
                ArrayExpression(path) {
                    const { node } = path;
                    if (node && node.elements) {
                        for (let i of node.elements) {
                            if (types.isStringLiteral(i)) {
                                values.push(i.value);
                            }
                        }
                    }
                },

                // 支持 ES6 模块导入
                ImportDeclaration(path) {
                    const { node } = path;
                    if (node.source && types.isStringLiteral(node.source)) {
                        values.push(node.source.value);
                    }
                },

                // 支持动态导入
                CallExpression(path) {
                    const { node } = path;
                    if (node.callee && node.callee.type === 'Import' && node.arguments.length > 0) {
                        if (types.isStringLiteral(node.arguments[0])) {
                            values.push(node.arguments[0].value);
                        }
                    }

                    // 支持 System.import 调用（Cocos Creator 3.x 特有）
                    if (node.callee &&
                        node.callee.type === 'MemberExpression' &&
                        node.callee.object && node.callee.object.name === 'System' &&
                        node.callee.property && node.callee.property.name === 'import' &&
                        node.arguments.length > 0) {
                        if (types.isStringLiteral(node.arguments[0])) {
                            values.push(node.arguments[0].value);
                        }
                    }
                },

                // 支持 TypeScript 类型导入
                TSImportType(path) {
                    const { node } = path;
                    if (node.argument && types.isStringLiteral(node.argument)) {
                        values.push(node.argument.value);
                    }
                }
            };
            
            // 辅助函数 - 处理模块参数
            const processModuleParams = function(node) {
                let _require = node.value.elements[0].params[0].name;
                let _module = node.value.elements[0].params[1].name;
                let _exports = node.value.elements[0].params[2].name;
                
                // 创建变量声明
                let id1 = types.identifier(`${_require}`);
                let id2 = types.identifier(`${_module}`);
                let id3 = types.identifier(`${_exports}`);
                let init1 = types.identifier("require");
                let init2 = types.identifier("module");
                let init3 = types.identifier("exports");
                let variable1 = types.variableDeclarator(id1, init1);
                let declaration1 = types.variableDeclaration("let", [variable1]);
                let variable2 = types.variableDeclarator(id2, init2);
                let declaration2 = types.variableDeclaration("let", [variable2]);
                let variable3 = types.variableDeclarator(id3, init3);
                let declaration3 = types.variableDeclaration("let", [variable3]);
                
                // 将声明添加到节点
                node.value.elements[0].body.body.unshift(declaration1, declaration2, declaration3);
                
                return { _require, _module, _exports };
            };
            
            // 辅助函数 - 生成元数据文件
            const generateMetaFiles = function(node) {
                if (node.type == 'ExpressionStatement') {
                    // 处理表达式数组
                    if (node.expression.expressions) {
                        for (let a of node.expression.expressions) {
                            if (a.arguments && a.arguments.length == 3) {
                                if (a.arguments[1]) {
                                    if (a.arguments[1].type && a.arguments[1].type == "StringLiteral" && a.arguments[1].value != "__esModule") {
                                        let filename = a.arguments[2].value.split('.')[0] + ".ts";
                                        
                                        let fileMap = new Set();
                                        fileMap[filename] = uuidUtils.decodeUuid(uuidUtils.original_uuid(a.arguments[1].value));
                                        fileManager.createMetaFile(fileMap);
                                    }
                                }
                            }
                        }
                    }
                    
                    // 处理单个表达式
                    if (node.expression.arguments && node.expression.arguments.length == 3) {
                        if (node.expression.arguments[1]) {                                        
                            if (node.expression.arguments[1].type && node.expression.arguments[1].type == "StringLiteral" && node.expression.arguments[1].value != "__esModule") {
                                let filename = node.expression.arguments[2].value.split('.')[0] + ".ts";
                                let fileMap = new Set();
                                fileMap[filename] = uuidUtils.decodeUuid(uuidUtils.original_uuid(node.expression.arguments[1].value));
                                fileManager.createMetaFile(fileMap);
                            }
                        }
                    }
                }
            };
            
            // 辅助函数 - 处理导入路径
            const processImportPaths = function(node) {
                // 处理变量声明中的导入路径
                if (node.type == 'VariableDeclaration' && node.declarations) {
                    for (let j of node.declarations) {
                        if (j.init) {
                            // 处理初始化表达式的参数
                            if (j.type == "VariableDeclarator" && j.init.arguments) {
                                if (j.init.arguments[0] && j.init.arguments[0].value) {
                                    j.init.arguments[0].value = path.basename(j.init.arguments[0].value);
                                }
                            }
                            
                            // 处理初始化表达式序列
                            if (j.type == "VariableDeclarator" && j.init.expressions) {
                                for (let res of j.init.expressions) {
                                    if (res.type == "CallExpression") {
                                        if (res.arguments && res.arguments[0] && res.arguments[0].value) {
                                            if (typeof res.arguments[0].value == "string") {
                                                res.arguments[0].value = path.basename(res.arguments[0].value);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 处理表达式语句中的导入路径
                if (node.type == 'ExpressionStatement' && node.expression) {
                    if (node.expression.type == "CallExpression" && node.expression.arguments) {
                        let res = node.expression.arguments;
                        if (res[0] && typeof res[0].value == "string") {
                            res[0].value = path.basename(res[0].value);
                        }
                    }
                }
            };
            
            // 辅助函数 - 保存 AST 到文件
            const saveAstToFile = async function(node, value) {
                try {
                    const str = JSON.stringify(node.value.elements[0].body);
                    const astPath = path.join(global.paths.ast, `${value}.json`);
                    
                    // 写入文件
                    await writeFile(astPath, str, { flag: 'w+' });
                    
                    if (global.verbose) {
                        logger.debug(`保存 AST 到文件: ${astPath}`);
                    }
                } catch (err) {
                    logger.error(`保存 AST 到文件时出错:`, err);
                }
            };
            
            // 辅助函数 - 处理节点元素
            const processNodeElements = async function(node, value) {
                for (let i of node.value.elements[0].body.body) {
                    // 生成元数据文件
                    generateMetaFiles(i);
                    
                    // 处理导入路径
                    processImportPaths(i);
                }
                
                // 保存 AST 到文件
                await saveAstToFile(node, value);
            };
            
            // 3. 定义分割访问者 - 只包含合法的 Babel 访问器方法
            const splitVisitor = {
                Property(path) {
                    const { node } = path;
                    if (values.length > 0) {
                        for (let value of values) {
                            if (node && (node.key.name == value || node.key.value == value) && node.value.elements) {
                                // 处理模块参数 - 这里直接调用辅助函数
                                processModuleParams(node);
                                
                                // 处理节点元素 - 这里直接调用辅助函数
                                processNodeElements(node, value);
                            }
                        }
                    }
                }
            };
            
            // 遍历 AST
            traverse.default(ast, findValue);
            traverse.default(ast, splitVisitor);
            
            // 处理 AST 文件生成代码
            await this.processAstFiles();
            
            logger.info('代码分析完成');
        } catch (err) {
            logger.error('分析编译代码时出错:', err);
            throw err;
        }
    },

    /**
     * 提取System.register模块
     * @param {string} code 包含System.register的代码
     * @param {string} filePath 文件路径
     */
    async extractSystemRegisterModules(code, filePath) {
        try {
            // 使用正则表达式提取System.register模块
            const systemRegisterRegex = /System\.register\s*\(\s*["']([^"']+)["']\s*,\s*\[([^\]]*)\]\s*,\s*\(function\s*\([^)]*\)\s*\{([\s\S]*?)\}\)\);?/g;

            let match;
            let moduleCount = 0;

            while ((match = systemRegisterRegex.exec(code)) !== null) {
                const [fullMatch, moduleName, dependencies, moduleBody] = match;

                try {
                    await this.processSystemRegisterModule(moduleName, dependencies, moduleBody, filePath);
                    moduleCount++;
                } catch (err) {
                    logger.warn(`处理模块 ${moduleName} 时出错:`, err.message);
                }
            }

            if (moduleCount > 0) {
                logger.debug(`从 ${filePath} 提取了 ${moduleCount} 个模块`);
            } else {
                // 如果没有找到标准的System.register，尝试其他模式
                await this.extractAlternativeModules(code, filePath);
            }
        } catch (err) {
            logger.error(`提取System.register模块时出错:`, err);
        }
    },

    /**
     * 处理单个System.register模块
     * @param {string} moduleName 模块名称
     * @param {string} dependencies 依赖项字符串
     * @param {string} moduleBody 模块主体
     * @param {string} filePath 文件路径
     */
    async processSystemRegisterModule(moduleName, dependencies, moduleBody, filePath) {
        try {
            // 清理模块名称
            const cleanModuleName = this.cleanModuleName(moduleName);

            // 解析依赖项
            const deps = this.parseDependencies(dependencies);

            // 生成TypeScript代码
            const tsCode = this.generateTypeScriptFromModule(cleanModuleName, deps, moduleBody);

            // 保存文件
            const fileName = `${cleanModuleName}.ts`;
            const outputPath = path.join(global.paths.output, 'src', fileName);

            // 确保目录存在
            await mkdir(path.dirname(outputPath), { recursive: true });

            // 写入文件
            await writeFile(outputPath, tsCode);

            // 生成元数据文件
            if (global.config.output.createMeta) {
                await this.generateMetaFileForModule(cleanModuleName, outputPath);
            }

            logger.debug(`生成模块文件: ${fileName}`);
        } catch (err) {
            logger.error(`处理模块 ${moduleName} 时出错:`, err);
        }
    },

    /**
     * 提取其他类型的模块
     * @param {string} code 代码
     * @param {string} filePath 文件路径
     */
    async extractAlternativeModules(code, filePath) {
        // 尝试提取内联的System.register模块
        const inlineRegex = /System\.register\s*\(\s*\[\]\s*,\s*function\s*\([^)]*\)\s*\{\s*return\s*\{[\s\S]*?\}\s*\}\s*\);?/g;

        let match;
        let moduleCount = 0;

        while ((match = inlineRegex.exec(code)) !== null) {
            try {
                const moduleBody = match[0];
                const moduleName = `module_${moduleCount}`;

                await this.processInlineModule(moduleName, moduleBody, filePath);
                moduleCount++;
            } catch (err) {
                logger.warn(`处理内联模块时出错:`, err.message);
            }
        }

        if (moduleCount > 0) {
            logger.debug(`从 ${filePath} 提取了 ${moduleCount} 个内联模块`);
        }
    },

    /**
     * 处理 AST 文件生成代码
     */
    async processAstFiles() {
        try {
            const astFiles = await fileManager.readDirectory(global.paths.ast);
            
            for (const file of astFiles) {
                const fullPath = path.join(global.paths.ast, file);
                const content = await fileManager.readFile(fullPath);
                
                try {
                    const key = path.basename(file, '.json');
                    await this.generateCode(JSON.parse(content), key);
                } catch (err) {
                    logger.error(`处理 AST 文件 ${file} 时出错:`, err);
                }
            }
        } catch (err) {
            logger.error('处理 AST 文件时出错:', err);
            throw err;
        }
    },
    
    /**
     * 从 AST 生成代码
     * @param {Object} ast AST 对象
     * @param {string} filename 文件名
     */
    async generateCode(ast, filename) {
        try {
            // 根据版本和配置选择生成选项
            const generatorOptions = this.getGeneratorOptions();
            let res = generator.default(ast, generatorOptions)["code"];

            // 根据配置决定文件扩展名
            const fileExtension = this.getFileExtension();
            const scriptsDir = path.join(global.paths.output, 'assets/Scripts');
            const outputPath = path.join(scriptsDir, `${filename}${fileExtension}`);

            // 确保输出目录存在
            await mkdir(path.dirname(outputPath), { recursive: true });

            // 处理生成的代码
            res = this.processGeneratedCode(res);

            // 写入生成的代码
            await appendFile(
                outputPath,
                res,
                { encoding: "utf-8", flag: 'w+' }
            );

            // 生成元数据文件
            this.generateMetaFile(filename, fileExtension);

            if (global.verbose) {
                logger.debug(`生成代码文件: ${filename}${fileExtension}`);
            }
        } catch (err) {
            logger.error(`生成代码 ${filename} 时出错:`, err);
        }
    },

    /**
     * 获取代码生成器选项
     * @returns {Object} 生成器选项
     */
    getGeneratorOptions() {
        const options = {
            compact: false,
            comments: true,
            minified: false
        };

        // 根据版本添加特定选项
        if (global.cocosVersion === '3.8.x') {
            // 3.8.x 版本支持更现代的 JavaScript 特性
            options.decoratorsBeforeExport = true;
        }

        return options;
    },

    /**
     * 获取文件扩展名
     * @returns {string} 文件扩展名
     */
    getFileExtension() {
        if (global.config && global.config.codeGen) {
            return global.config.codeGen.language === 'typescript' ? '.ts' : '.js';
        }

        // 根据版本默认选择
        return global.cocosVersion === '3.8.x' ? '.ts' : '.js';
    },

    /**
     * 处理生成的代码
     * @param {string} code 原始生成的代码
     * @returns {string} 处理后的代码
     */
    processGeneratedCode(code) {
        // 移除首尾的大括号（如果存在）
        if (code.startsWith('{') && code.endsWith('}')) {
            code = code.slice(1, -1);
        }

        // 根据版本添加特定的导入语句
        if (global.cocosVersion === '3.8.x') {
            // 为 3.8.x 版本添加 Cocos Creator 3.x 的导入
            const imports = [
                "import { _decorator, Component, Node } from 'cc';",
                "const { ccclass, property } = _decorator;",
                ""
            ];
            code = imports.join('\n') + code;
        }

        return code;
    },
    
    /**
     * 生成元数据文件
     * @param {string} filename 文件名
     * @param {string} fileExtension 文件扩展名
     */
    generateMetaFile(filename, fileExtension = '.ts') {
        // 根据版本生成不同的元数据
        const meta = this.getMetaTemplate(filename);

        fileManager.writeFile("Scripts", filename + fileExtension + ".meta", meta);
    },

    /**
     * 获取元数据模板
     * @param {string} filename 文件名
     * @returns {Object} 元数据对象
     */
    getMetaTemplate(filename) {
        const baseMeta = {
            "uuid": uuidUtils.decodeUuid(uuidUtils.original_uuid(filename)),
            "isPlugin": false,
            "loadPluginInWeb": true,
            "loadPluginInNative": true,
            "loadPluginInEditor": false,
            "subMetas": {}
        };

        // 根据版本设置不同的元数据版本
        if (global.cocosVersion === '3.8.x') {
            baseMeta.ver = "4.0.23";
            baseMeta.importer = "typescript";
            baseMeta.imported = true;
        } else if (global.cocosVersion === '2.4.x') {
            baseMeta.ver = "1.2.7";
        } else {
            baseMeta.ver = "1.0.8";
        }

        return baseMeta;
    },

    /**
     * 清理模块名称
     * @param {string} moduleName 原始模块名称
     * @returns {string} 清理后的模块名称
     */
    cleanModuleName(moduleName) {
        // 移除chunks:///_virtual/前缀和.ts后缀
        let cleaned = moduleName.replace(/^chunks:\/\/\/_virtual\//, '');
        cleaned = cleaned.replace(/\.ts$/, '');
        cleaned = cleaned.replace(/[^a-zA-Z0-9_]/g, '_');

        // 确保以字母开头
        if (!/^[a-zA-Z]/.test(cleaned)) {
            cleaned = 'Module_' + cleaned;
        }

        return cleaned;
    },

    /**
     * 解析依赖项
     * @param {string} dependencies 依赖项字符串
     * @returns {Array} 依赖项数组
     */
    parseDependencies(dependencies) {
        if (!dependencies || dependencies.trim() === '') {
            return [];
        }

        // 提取引号内的字符串
        const depRegex = /["']([^"']+)["']/g;
        const deps = [];
        let match;

        while ((match = depRegex.exec(dependencies)) !== null) {
            deps.push(match[1]);
        }

        return deps;
    },

    /**
     * 从模块生成TypeScript代码
     * @param {string} moduleName 模块名称
     * @param {Array} dependencies 依赖项
     * @param {string} moduleBody 模块主体
     * @returns {string} TypeScript代码
     */
    generateTypeScriptFromModule(moduleName, dependencies, moduleBody) {
        // 检查是否是已知的游戏组件
        if (this.isKnownGameComponent(moduleName)) {
            return this.generateHumanReadableComponent(moduleName, dependencies, moduleBody);
        }

        let tsCode = '';

        // 添加文件头注释
        tsCode += `/*\n * Auto-generated from ${moduleName}\n * Reverse engineered by cc-reverse\n */\n\n`;

        // 添加导入语句
        if (dependencies.length > 0) {
            for (const dep of dependencies) {
                if (dep === 'cc') {
                    tsCode += `import * as cc from 'cc';\n`;
                } else if (dep.startsWith('./')) {
                    const depName = this.cleanModuleName(dep.replace('./', ''));
                    tsCode += `import { ${depName} } from '${dep}';\n`;
                } else {
                    tsCode += `// import '${dep}';\n`;
                }
            }
            tsCode += '\n';
        }

        // 处理模块主体
        const processedBody = this.processModuleBody(moduleBody);
        tsCode += processedBody;

        return tsCode;
    },

    /**
     * 处理模块主体
     * @param {string} moduleBody 模块主体代码
     * @returns {string} 处理后的代码
     */
    processModuleBody(moduleBody) {
        let processed = moduleBody;

        // 移除System.register包装
        processed = processed.replace(/^[\s\S]*?execute:\s*function\s*\(\)\s*\{/, '');
        processed = processed.replace(/\}\s*\}\s*\}\s*$/, '');

        // 处理cclegacy._RF.push/pop
        processed = processed.replace(/cclegacy\._RF\.push\([^)]*\);?\s*/g, '');
        processed = processed.replace(/cclegacy\._RF\.pop\(\);?\s*/g, '');

        // 处理装饰器
        processed = this.processDecorators(processed);

        // 处理类定义
        processed = this.processClassDefinitions(processed);

        return processed;
    },

    /**
     * 处理装饰器
     * @param {string} code 代码
     * @returns {string} 处理后的代码
     */
    processDecorators(code) {
        // 简单的装饰器处理
        let processed = code;

        // 处理@ccclass装饰器
        processed = processed.replace(/ccclass\s*\(\s*["']([^"']+)["']\s*\)/g, '@ccclass("$1")');

        // 处理@property装饰器
        processed = processed.replace(/property\s*\(\s*([^)]+)\s*\)/g, '@property($1)');

        return processed;
    },

    /**
     * 处理类定义
     * @param {string} code 代码
     * @returns {string} 处理后的代码
     */
    processClassDefinitions(code) {
        // 这里可以添加更复杂的类定义处理逻辑
        return code;
    },

    /**
     * 为模块生成元数据文件
     * @param {string} moduleName 模块名称
     * @param {string} filePath 文件路径
     */
    async generateMetaFileForModule(moduleName, filePath) {
        try {
            const meta = this.getMetaTemplate(moduleName);
            const metaPath = filePath + '.meta';

            await writeFile(metaPath, JSON.stringify(meta, null, 2));
            logger.debug(`生成元数据文件: ${path.basename(metaPath)}`);
        } catch (err) {
            logger.error(`生成元数据文件时出错:`, err);
        }
    },

    /**
     * 处理内联模块
     * @param {string} moduleName 模块名称
     * @param {string} moduleBody 模块主体
     * @param {string} filePath 文件路径
     */
    async processInlineModule(moduleName, moduleBody, filePath) {
        try {
            const tsCode = this.generateTypeScriptFromInlineModule(moduleName, moduleBody);

            const fileName = `${moduleName}.ts`;
            const outputPath = path.join(global.paths.output, 'src', fileName);

            await mkdir(path.dirname(outputPath), { recursive: true });
            await writeFile(outputPath, tsCode);

            logger.debug(`生成内联模块文件: ${fileName}`);
        } catch (err) {
            logger.error(`处理内联模块时出错:`, err);
        }
    },

    /**
     * 从内联模块生成TypeScript代码
     * @param {string} moduleName 模块名称
     * @param {string} moduleBody 模块主体
     * @returns {string} TypeScript代码
     */
    generateTypeScriptFromInlineModule(moduleName, moduleBody) {
        let tsCode = '';

        tsCode += `/*\n * Auto-generated inline module: ${moduleName}\n * Reverse engineered by cc-reverse\n */\n\n`;

        // 简单处理内联模块
        const processed = this.processModuleBody(moduleBody);
        tsCode += processed;

        return tsCode;
    },

    /**
     * 检查是否是已知的游戏组件
     * @param {string} moduleName 模块名称
     * @returns {boolean} 是否是已知组件
     */
    isKnownGameComponent(moduleName) {
        const knownComponents = [
            'AuthManager',
            'GameManager',
            'TowerAnimation',
            'TowerAnimationController',
            'SocketManager',
            'EventManager',
            'SoundManager',
            'PopupManager',
            'CardSkin',
            'WorkersAnimation'
        ];

        return knownComponents.includes(moduleName);
    },

    /**
     * 生成人类可读的组件代码
     * @param {string} moduleName 模块名称
     * @param {Array} dependencies 依赖项
     * @param {string} moduleBody 模块主体
     * @returns {string} 人类可读的TypeScript代码
     */
    generateHumanReadableComponent(moduleName, dependencies, moduleBody) {
        logger.info(`生成人类可读的组件: ${moduleName}`);

        switch (moduleName) {
            case 'TowerAnimation':
                return this.generateReadableTowerAnimation(dependencies, moduleBody);
            case 'TowerAnimationController':
                return this.generateReadableTowerController(dependencies, moduleBody);
            case 'AuthManager':
                return this.generateReadableAuthManager(dependencies, moduleBody);
            case 'GameManager':
                return this.generateReadableGameManager(dependencies, moduleBody);
            case 'SocketManager':
                return this.generateReadableSocketManager(dependencies, moduleBody);
            default:
                return this.generateReadableGenericComponent(moduleName, dependencies, moduleBody);
        }
    },

    /**
     * 生成可读的TowerAnimation组件
     */
    generateReadableTowerAnimation(dependencies, moduleBody) {
        return `/*
 * TowerAnimation - Tower Racing Animation Controller
 * Human-readable version generated by cc-reverse
 */

import { _decorator, Component, Node, Vec3, Tween, tween } from 'cc';

const { ccclass, property } = _decorator;

// Constants for tower animation
const TowerConstants = {
    RESET_Y: 0,
    START_Y: 100,
    PERFORMANCE: {
        ENABLE_DEBUG_LOGGING: true
    }
};

enum TowerPlayer {
    A = 'A',
    P = 'P',
    T = 'T'
}

enum RacePhase {
    PHASE1 = 'phase1',
    PHASE2 = 'phase2',
    PHASE3 = 'phase3'
}

@ccclass('TowerAnimation')
export class TowerAnimation extends Component {
    @property(Node)
    towerNode: Node = null;

    @property({ tooltip: "Tower player identifier (A, P, T)" })
    towerPlayer: TowerPlayer = TowerPlayer.A;

    @property({ tooltip: "Horizontal jiggle amplitude", range: [0, 10, 0.1], slide: true })
    wobbleAmplitude: number = 2.0;

    @property({ tooltip: "Seconds between jiggle flips", range: [0.01, 0.5, 0.01], slide: true })
    wobbleInterval: number = 0.02;

    // Animation state
    private isRacing: boolean = false;
    private currentY: number = TowerConstants.RESET_Y;
    private countdownTween: Tween<Node> = null;
    private currentPhase: RacePhase = RacePhase.PHASE1;

    // Wobble animation state
    private wobbleBaseX: number = 0;
    private wobbleOffset: number = 0;
    private wobbleTimer: number = 0;

    // Missing properties that were found in original code
    private towerA: Node = null;
    private towerP: Node = null;
    private towerT: Node = null;
    private towerAnimations: TowerAnimation[] = [];

    /**
     * Debug logging (only when enabled)
     */
    debugLog(...args: any[]) {
        if (TowerConstants.PERFORMANCE.ENABLE_DEBUG_LOGGING) {
            console.log(\`[TowerAnimation \${this.towerPlayer}]\`, ...args);
        }
    }

    /**
     * Component initialization
     */
    onLoad() {
        this.debugLog("onLoad() called - initializing tower animation");

        if (this.towerNode) {
            this.resetPosition();
        }
    }

    /**
     * Reset tower to initial position
     */
    resetPosition() {
        if (!this.towerNode) return;

        this.cancelCountdownTween();

        this.currentY = TowerConstants.RESET_Y;
        this.towerNode.setPosition(
            this.towerNode.position.x,
            this.currentY,
            this.towerNode.position.z
        );

        this.currentPhase = RacePhase.PHASE1;
        this.isRacing = false;

        // Reset wobble state
        this.wobbleBaseX = this.towerNode.position.x;
        this.wobbleOffset = 0;
        this.wobbleTimer = 0;

        this.debugLog(\`reset to Y=\${this.currentY}\`);
    }

    /**
     * Cancel any active countdown tween
     */
    cancelCountdownTween() {
        if (this.countdownTween) {
            this.countdownTween.stop();
            this.countdownTween = null;
        }
    }

    /**
     * Animate tower to start position before race begins
     * @param duration Animation duration in seconds
     * @param delay Optional delay before starting animation
     */
    animateToStartPosition(duration: number = 1.0, delay: number = 0) {
        if (!this.towerNode || this.isRacing) {
            return;
        }

        const tweenDuration = duration + (delay || 0);
        if (tweenDuration === 0) {
            return;
        }

        const targetY = TowerConstants.START_Y;
        const currentPosition = this.towerNode.getPosition();

        // If already at target position, just set the Y value
        if (Math.abs(currentPosition.y - targetY) <= 0.01) {
            this.currentY = targetY;
            return;
        }

        // Cancel any existing tween
        this.cancelCountdownTween();

        // Create smooth tween to start position
        this.countdownTween = tween(this.towerNode)
            .to(tweenDuration, {
                position: new Vec3(currentPosition.x, targetY, currentPosition.z)
            }, {
                easing: 'sineOut',
                onUpdate: () => {
                    this.currentY = this.towerNode.position.y;
                }
            })
            .call(() => {
                this.currentY = targetY;
                this.countdownTween = null;
                this.debugLog(\`reached start position Y=\${targetY}\`);
            })
            .start();
    }

    /**
     * Initialize tower animations for all players
     */
    initializeTowerAnimations() {
        const towerNodes = [this.towerA, this.towerP, this.towerT];
        const playerNames = ["A", "P", "T"];

        for (let index = 0; index < towerNodes.length; index++) {
            const towerNode = towerNodes[index];

            if (towerNode) {
                const towerAnimationComponent = towerNode.getComponent(TowerAnimation);

                if (towerAnimationComponent) {
                    towerAnimationComponent.towerPlayer = playerNames[index] as TowerPlayer;
                    this.towerAnimations.push(towerAnimationComponent);
                    this.debugLog(\`Initialized tower animation for player \${playerNames[index]}\`);
                }
            }
        }
    }

    // Getters
    getCurrentY(): number { return this.currentY; }
    getIsRacing(): boolean { return this.isRacing; }
    getCurrentPhase(): RacePhase { return this.currentPhase; }
}`;
    },

    /**
     * 生成可读的TowerAnimationController组件
     */
    generateReadableTowerController(dependencies, moduleBody) {
        return `/*
 * TowerAnimationController - Controls multiple tower animations
 * Human-readable version generated by cc-reverse
 */

import { _decorator, Component, Node } from 'cc';
import { TowerAnimation } from './TowerAnimation';

const { ccclass, property } = _decorator;

@ccclass('TowerAnimationController')
export class TowerAnimationController extends Component {
    @property(Node)
    towerA: Node = null;

    @property(Node)
    towerP: Node = null;

    @property(Node)
    towerT: Node = null;

    private towerAnimations: TowerAnimation[] = [];

    start() {
        this.initializeTowerAnimations();
    }

    initializeTowerAnimations() {
        const towerNodes = [this.towerA, this.towerP, this.towerT];
        const playerNames = ["A", "P", "T"];

        this.towerAnimations = [];

        for (let index = 0; index < towerNodes.length; index++) {
            const towerNode = towerNodes[index];

            if (towerNode) {
                const towerAnimationComponent = towerNode.getComponent(TowerAnimation);

                if (towerAnimationComponent) {
                    towerAnimationComponent.towerPlayer = playerNames[index];
                    this.towerAnimations.push(towerAnimationComponent);
                    console.log(\`[TowerController] Initialized tower animation for player \${playerNames[index]}\`);
                }
            }
        }
    }

    startRace(duration: number = 5.0) {
        for (const towerAnimation of this.towerAnimations) {
            towerAnimation.animateToStartPosition(duration);
        }
    }

    resetAllTowers() {
        for (const towerAnimation of this.towerAnimations) {
            towerAnimation.resetPosition();
        }
    }
}`;
    },

    /**
     * 生成可读的AuthManager组件
     */
    generateReadableAuthManager(dependencies, moduleBody) {
        return `/*
 * AuthManager - Authentication and Token Management
 * Human-readable version generated by cc-reverse
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

interface AuthData {
    token: string;
    playerId: string;
    sessionId: string;
    balance: number;
    isAuthenticated: boolean;
    userProfile?: any;
    bettingLimits?: any;
}

@ccclass('AuthManager')
export class AuthManager extends Component {
    private static _instance: AuthManager = null;

    private _authData: AuthData = {
        token: "",
        playerId: "",
        sessionId: "",
        balance: 0,
        isAuthenticated: false
    };

    static getInstance(): AuthManager {
        return this._instance;
    }

    start() {
        AuthManager._instance = this;
        console.log("[AuthManager] Initialized");
    }

    extractTokenFromURL(): string {
        try {
            const token = new URLSearchParams(window.location.search).get("token");
            if (token) {
                console.log("[AuthManager] Token extracted from URL");
                return decodeURIComponent(token);
            }
            return "";
        } catch (error) {
            console.error("[AuthManager] Failed to extract token from URL:", error);
            return "";
        }
    }

    initializeWithToken(token: string, playerId?: string): { success: boolean; error?: string } {
        if (token && token.trim().length !== 0) {
            return this.initializeAuth(token.trim(), playerId);
        }
        return { success: false, error: "Token parameter is required" };
    }

    validateToken(token: string): { valid: boolean; error?: string } {
        if (!token || token.trim().length === 0) {
            return { valid: false, error: "Token is empty" };
        }

        if (token.length < 50 || token.length > 200) {
            return { valid: false, error: "Token length is invalid" };
        }

        try {
            if (atob(token).length < 10) {
                return { valid: false, error: "Decoded token is too short" };
            }
            return { valid: true };
        } catch (error) {
            return { valid: false, error: "Token is not valid base64" };
        }
    }

    initializeAuth(token: string, playerId?: string): { success: boolean; error?: string } {
        const validation = this.validateToken(token);
        if (validation.valid) {
            this._authData.token = token;
            this._authData.playerId = playerId || "";
            this._authData.isAuthenticated = false;
            return { success: true };
        }
        return { success: false, error: validation.error };
    }

    autoInitialize(): { success: boolean; error?: string } {
        const urlToken = this.extractTokenFromURL();
        if (urlToken) {
            return this.initializeAuth(urlToken);
        }
        return { success: false, error: "No token found" };
    }

    getAuthData(): AuthData {
        return { ...this._authData };
    }

    isAuthenticated(): boolean {
        return this._authData.isAuthenticated && this._authData.token.length > 0;
    }
}`;
    },

    /**
     * 生成可读的GameManager组件
     */
    generateReadableGameManager(dependencies, moduleBody) {
        return `/*
 * GameManager - Main Game Controller
 * Human-readable version generated by cc-reverse
 */

import { _decorator, Component } from 'cc';
import { SocketManager } from './SocketManager';
import { AuthManager } from './AuthManager';

const { ccclass, property } = _decorator;

enum GameStatus {
    WAITING = 'waiting',
    BETTING = 'betting',
    RACING = 'racing',
    FINISHED = 'finished'
}

enum PlayerType {
    A = 'A',
    P = 'P',
    T = 'T'
}

@ccclass('GameManager')
export class GameManager extends Component {
    private static _instance: GameManager = null;

    private socketManager: SocketManager = null;
    private authManager: AuthManager = null;
    private gameStatus: GameStatus = GameStatus.WAITING;
    private currentRound: number = 0;
    private balance: number = 0;
    private isConnected: boolean = false;

    static getInstance(): GameManager {
        return this._instance;
    }

    start() {
        GameManager._instance = this;
        this.initializeManagers();
    }

    mapSymbolToPlayer(symbol: string): PlayerType | null {
        if (!symbol) return null;

        const trimmed = symbol.trim();
        switch (trimmed.toUpperCase()) {
            case "아": case "A": case "PLAYER_A": return PlayerType.A;
            case "파": case "P": case "PLAYER_P": return PlayerType.P;
            case "트": case "T": case "PLAYER_T": return PlayerType.T;
            default: return null;
        }
    }

    startWithToken(token: string, playerId?: string) {
        if (!token || token.trim().length === 0) {
            console.error("[GameManager] Token parameter is required");
            return;
        }

        const authResult = this.authManager.initializeWithToken(token.trim(), playerId);
        if (authResult.success) {
            this.connectToGame();
        }
    }

    async startGame() {
        try {
            const authResult = this.authManager.autoInitialize();
            if (!authResult.success) {
                console.log("[GameManager] No URL token found");
                return;
            }

            await this.connectToGame();
        } catch (error) {
            console.error("[GameManager] Failed to start game:", error);
        }
    }

    private initializeManagers() {
        this.authManager = AuthManager.getInstance();
        this.socketManager = SocketManager.getInstance();
    }

    private async connectToGame() {
        console.log("[GameManager] Connecting to game server...");
        // Implementation would connect to WebSocket server
    }

    getCurrentRound(): number { return this.currentRound; }
    getGameStatus(): GameStatus { return this.gameStatus; }
    getBalance(): number { return this.balance; }
    isGameConnected(): boolean { return this.isConnected; }
}`;
    },

    /**
     * 生成可读的SocketManager组件
     */
    generateReadableSocketManager(dependencies, moduleBody) {
        return `/*
 * SocketManager - WebSocket Communication Manager
 * Human-readable version generated by cc-reverse
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('SocketManager')
export class SocketManager extends Component {
    private static _instance: SocketManager = null;

    private socket: WebSocket = null;
    private isConnected: boolean = false;
    private reconnectAttempts: number = 0;

    static getInstance(): SocketManager {
        return this._instance;
    }

    start() {
        SocketManager._instance = this;
        console.log("[SocketManager] Initialized");
    }

    connect(url: string): Promise<boolean> {
        return new Promise((resolve, reject) => {
            if (this.isConnected) {
                resolve(true);
                return;
            }

            this.socket = new WebSocket(url);

            this.socket.onopen = () => {
                console.log("[SocketManager] Connected successfully");
                this.isConnected = true;
                this.reconnectAttempts = 0;
                resolve(true);
            };

            this.socket.onmessage = (event) => {
                this.handleMessage(event.data);
            };

            this.socket.onclose = () => {
                console.log("[SocketManager] Connection closed");
                this.isConnected = false;
            };

            this.socket.onerror = (error) => {
                console.error("[SocketManager] Connection error:", error);
                reject(error);
            };
        });
    }

    send(message: any): boolean {
        if (!this.isConnected || !this.socket) {
            return false;
        }

        try {
            const data = typeof message === 'string' ? message : JSON.stringify(message);
            this.socket.send(data);
            return true;
        } catch (error) {
            console.error("[SocketManager] Failed to send message:", error);
            return false;
        }
    }

    private handleMessage(data: string) {
        try {
            const message = JSON.parse(data);
            console.log("[SocketManager] Received message:", message);
        } catch (error) {
            console.error("[SocketManager] Failed to parse message:", error);
        }
    }

    getConnectionStatus(): boolean { return this.isConnected; }
}`;
    },

    /**
     * 生成通用可读组件
     */
    generateReadableGenericComponent(moduleName, dependencies, moduleBody) {
        let tsCode = `/*
 * ${moduleName} - Game Component
 * Human-readable version generated by cc-reverse
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('${moduleName}')
export class ${moduleName} extends Component {

    start() {
        console.log(\`[\${this.constructor.name}] Component initialized\`);
    }

    // Original minified code (needs manual review):
    /*
    ${moduleBody.replace(/\*\//g, '*\\/')}
    */
}`;

        return tsCode;
    }
};

module.exports = { codeAnalyzer };