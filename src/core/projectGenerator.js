/*
 * @Date: 2025-06-07 10:06:12
 * @Description: 项目配置文件生成工具
 */
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const { uuidUtils } = require('../utils/uuidUtils');
const { logger } = require('../utils/logger');

// 将 fs 的异步方法转换为 Promise
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);

/**
 * 项目生成器模块
 */
const projectGenerator = {
    /**
     * 生成项目文件
     * @returns {Promise<void>}
     */
    async generateProject() {
        try {
            // 创建配置对象
            const project = this.createProjectConfig();
            const jsconfig = this.createJSConfig();
            const tsconfig = this.createTSConfig();
            const settings = this.createProjectSettings();

            // 确保目录存在
            const settingsDir = path.join(global.paths.output, 'settings');
            await mkdir(settingsDir, { recursive: true });

            // 写入配置文件
            await writeFile(
                path.join(settingsDir, 'project.json'),
                JSON.stringify(settings, null, 2)
            );

            await writeFile(
                path.join(global.paths.output, 'project.json'),
                JSON.stringify(project, null, 2)
            );

            await writeFile(
                path.join(global.paths.output, 'jsconfig.json'),
                JSON.stringify(jsconfig, null, 2)
            );

            await writeFile(
                path.join(global.paths.output, 'tsconfig.json'),
                JSON.stringify(tsconfig, null, 2)
            );

            // 为 3.8.x 版本生成额外的配置文件
            if (global.cocosVersion === '3.8.x') {
                await this.generate38xSpecificFiles();
            }

            logger.info('项目配置文件生成完成');
        } catch (err) {
            logger.error('生成项目配置文件时出错:', err);
            throw err;
        }
    },

    /**
     * 生成 Cocos Creator 3.8.x 特有的配置文件
     * @returns {Promise<void>}
     */
    async generate38xSpecificFiles() {
        try {
            // 生成 package.json
            const packageJson = this.createPackageJson();
            await writeFile(
                path.join(global.paths.output, 'package.json'),
                JSON.stringify(packageJson, null, 2)
            );

            // 生成 .gitignore
            const gitignore = this.createGitignore();
            await writeFile(
                path.join(global.paths.output, '.gitignore'),
                gitignore
            );

            // 生成 extensions 目录配置
            const extensionsDir = path.join(global.paths.output, 'extensions');
            await mkdir(extensionsDir, { recursive: true });

            logger.debug('3.8.x 特有配置文件生成完成');
        } catch (err) {
            logger.error('生成 3.8.x 特有配置文件时出错:', err);
            throw err;
        }
    },

    /**
     * 创建 package.json 文件 (3.8.x)
     * @returns {Object} package.json 配置对象
     */
    createPackageJson() {
        return {
            "name": "cocos-creator-project",
            "version": "1.0.0",
            "description": "Cocos Creator 3.8.x Project",
            "main": "index.js",
            "scripts": {
                "build": "cocos build",
                "preview": "cocos preview"
            },
            "dependencies": {},
            "devDependencies": {},
            "engines": {
                "node": ">=14.0.0"
            }
        };
    },

    /**
     * 创建 .gitignore 文件内容 (3.8.x)
     * @returns {string} .gitignore 文件内容
     */
    createGitignore() {
        return `# Cocos Creator 3.x
/library/
/local/
/temp/
/build/
/profiles/
/settings/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
`;
    },

    /**
     * 生成项目配置
     * @param {string} name 项目名称
     * @returns {Object} 项目配置对象
     */
    createProjectConfig(name = "project") {
        // 根据检测到的版本设置相应的版本号
        const version = this.getProjectVersion();

        const baseConfig = {
            "name": name,
            "id": uuidUtils.decodeUuid(uuidUtils.generateUuid()),
            "version": version,
            "isNew": false
        };

        // 根据版本设置不同的配置
        if (global.cocosVersion === '3.8.x') {
            return {
                ...baseConfig,
                "engine": "cocos-creator",
                "packages": "packages",
                "type": "3d", // 3.8.x 支持 3D
                "renderPipeline": "builtin-forward"
            };
        } else {
            return {
                ...baseConfig,
                "engine": "cocos-creator-js",
                "packages": "packages"
            };
        }
    },

    /**
     * 获取项目版本号
     * @returns {string} 版本号
     */
    getProjectVersion() {
        // 根据全局检测到的版本返回相应的版本号
        if (global.cocosVersion === '3.8.x') {
            return "3.8.8"; // 3.8.x系列的最新稳定版本
        } else if (global.cocosVersion === '2.4.x') {
            return "2.4.13"; // 2.4.x系列的最新稳定版本
        } else {
            return "2.3.4"; // 默认2.3.x版本
        }
    },

    /**
     * 生成 JSConfig 配置
     * @returns {Object} JSConfig 配置对象
     */
    createJSConfig() {
        return {
            "compilerOptions": {
                "target": "es6",
                "module": "commonjs",
                "experimentalDecorators": true
            },
            "exclude": [
                "node_modules",
                ".vscode",
                "library",
                "local",
                "settings",
                "temp"
            ]
        };
    },

    /**
     * 生成 TSConfig 配置
     * @returns {Object} TSConfig 配置对象
     */
    createTSConfig() {
        const baseConfig = {
            "compilerOptions": {
                "module": "commonjs",
                "lib": ["es2015", "es2017", "dom"],
                "target": "es5",
                "experimentalDecorators": true,
                "skipLibCheck": true,
                "outDir": "temp/vscode-dist",
                "forceConsistentCasingInFileNames": true
            },
            "exclude": [
                "node_modules",
                "library",
                "local",
                "temp",
                "build",
                "settings"
            ]
        };

        // 为 3.8.x 版本添加特定的 TypeScript 配置
        if (global.cocosVersion === '3.8.x') {
            baseConfig.compilerOptions = {
                ...baseConfig.compilerOptions,
                "target": "es6",
                "lib": ["es2015", "es2017", "es2020", "dom"],
                "moduleResolution": "node",
                "strict": true,
                "noImplicitAny": false,
                "downlevelIteration": true,
                "esModuleInterop": true,
                "isolatedModules": true,
                "baseUrl": "./",
                "paths": {
                    "cc": ["./temp/declarations/cc"],
                    "cc/*": ["./temp/declarations/cc/*"]
                },
                "types": [
                    "cc"
                ]
            };
        }

        return baseConfig;
    },

    /**
     * 生成项目设置
     * @returns {Object} 项目设置对象
     */
    createProjectSettings() {
        let settings;

        if (global.cocosVersion === '3.8.x') {
            // Cocos Creator 3.8.x 项目设置
            settings = {
                "group-list": [],
                "collision-matrix": [],
                "physics": {
                    "gravity": {
                        "x": 0,
                        "y": -10
                    },
                    "allowSleep": true,
                    "sleepThreshold": 0.1,
                    "autoSimulation": true,
                    "fixedTimeStep": 1/60,
                    "maxSubSteps": 1
                },
                "rendering": {
                    "renderPipeline": "builtin-forward",
                    "shadowType": 1,
                    "shadowDistance": 50,
                    "shadowNormalBias": 0,
                    "shadowBias": 0.0001
                },
                "animation": {
                    "defaultClip": {
                        "duration": 1,
                        "sample": 60,
                        "speed": 1,
                        "wrapMode": 1
                    }
                },
                "assets": {
                    "preloadBundles": [],
                    "remoteBundles": [],
                    "server": ""
                },
                "scripting": {
                    "strictMode": false
                },
                "migrate-history": [],
                "start-scene": "current"
            };
        } else {
            // Cocos Creator 2.x 项目设置
            settings = {
                "group-list": "",
                "collision-matrix": "",
                "excluded-modules": [
                    "3D Physics/Builtin"
                ],
                "last-module-event-record-time": Date.now(),
                "design-resolution-width": 960,
                "design-resolution-height": 640,
                "fit-width": false,
                "fit-height": true,
                "use-project-simulator-setting": false,
                "simulator-orientation": false,
                "use-customize-simulator": true,
                "simulator-resolution": {
                    "height": 640,
                    "width": 960
                },
                "assets-sort-type": "name",
                "facebook": {
                    "appID": "",
                    "audience": {
                        "enable": false
                    },
                    "enable": false,
                    "live": {
                        "enable": false
                    }
                },
                "migrate-history": [],
                "start-scene": "current"
            };
        }

        // 添加全局设置
        if (global.settings) {
            if (global.settings["groupList"]) {
                settings["group-list"] = global.settings["groupList"];
            }
            
            if (global.settings["collisionMatrix"]) {
                settings["collision-matrix"] = global.settings["collisionMatrix"];
            }
            
            if (global.settings["launchScene"]) {
                settings["start-scene"] = path.basename(global.settings["launchScene"]).split(".")[0];
            }
        }

        return settings;
    }
};

module.exports = { projectGenerator }; 