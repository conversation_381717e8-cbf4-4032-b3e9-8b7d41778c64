/*
 * @Date: 2025-10-05
 * @Description: Test script for Cocos Creator 3.8.x support
 */
const assert = require('assert');
const path = require('path');
const fs = require('fs');
const { reverseProject } = require('../src/core/reverseEngine');
const { codeAnalyzer } = require('../src/core/codeAnalyzer');
const { resourceProcessor } = require('../src/core/resourceProcessor');
const { projectGenerator } = require('../src/core/projectGenerator');

/**
 * Test suite for Cocos Creator 3.8.x support
 */
describe('Cocos Creator 3.8.x Support Tests', function() {
    this.timeout(10000); // 10 second timeout for tests

    beforeEach(function() {
        // Reset global state before each test
        global.cocosVersion = '3.8.x';
        global.config = {
            output: { createMeta: true, prettify: true },
            codeGen: { language: "typescript", moduleType: "commonjs" },
            assets: { extractTextures: true, extractAudio: true }
        };
        global.paths = {
            output: './test-output',
            ast: './test-output/ast',
            res: './test-resources'
        };
    });

    describe('Version Detection', function() {
        it('should detect 3.8.x version from file patterns', function() {
            // Mock file system for testing
            const originalExistsSync = fs.existsSync;
            const originalReadFileSync = fs.readFileSync;
            
            fs.existsSync = function(filePath) {
                if (filePath.includes('cc.js') || filePath.includes('application.js')) {
                    return true;
                }
                return false;
            };
            
            fs.readFileSync = function(filePath) {
                if (filePath.includes('cc.js')) {
                    return 'System.import("cc").then((cc) => { /* 3.8.x code */ });';
                }
                return '';
            };

            // Test version detection logic would go here
            // This is a simplified test structure
            
            // Restore original functions
            fs.existsSync = originalExistsSync;
            fs.readFileSync = originalReadFileSync;
        });
    });

    describe('Code Analyzer', function() {
        it('should configure parser for TypeScript and modern JS features', function() {
            const options = codeAnalyzer.getParserOptions();
            
            assert(options.plugins.includes('typescript'), 'Should include TypeScript plugin');
            assert(options.plugins.includes('decorators'), 'Should include decorators plugin');
            assert(options.plugins.includes('classStaticBlock'), 'Should include class static block plugin');
            assert(options.sourceType === 'module', 'Should use module source type');
        });

        it('should generate correct file extension for 3.8.x', function() {
            const extension = codeAnalyzer.getFileExtension();
            assert.strictEqual(extension, '.ts', 'Should generate .ts files for 3.8.x');
        });

        it('should generate correct metadata for 3.8.x', function() {
            const meta = codeAnalyzer.getMetaTemplate('test-file');
            
            assert.strictEqual(meta.ver, '4.0.23', 'Should use 3.8.x metadata version');
            assert.strictEqual(meta.importer, 'typescript', 'Should use TypeScript importer');
            assert.strictEqual(meta.imported, true, 'Should mark as imported');
        });
    });

    describe('Resource Processor', function() {
        it('should generate correct metadata for different asset types', function() {
            const audioMeta = resourceProcessor.generateMetaData('test-uuid', 'AudioClip');
            assert.strictEqual(audioMeta.ver, '4.0.23', 'Should use 3.8.x version for audio');
            assert.strictEqual(audioMeta.importer, 'audio-clip', 'Should use correct importer');

            const textureMeta = resourceProcessor.generateMetaData('test-uuid', 'Texture2D');
            assert.strictEqual(textureMeta.importer, 'texture', 'Should use texture importer');
        });

        it('should get correct importer types', function() {
            assert.strictEqual(resourceProcessor.getImporterType('Material'), 'material');
            assert.strictEqual(resourceProcessor.getImporterType('EffectAsset'), 'effect');
            assert.strictEqual(resourceProcessor.getImporterType('Mesh'), 'mesh');
            assert.strictEqual(resourceProcessor.getImporterType('Prefab'), 'prefab');
        });
    });

    describe('Project Generator', function() {
        it('should generate correct project version for 3.8.x', function() {
            const version = projectGenerator.getProjectVersion();
            assert.strictEqual(version, '3.8.8', 'Should return 3.8.8 for 3.8.x projects');
        });

        it('should create 3.8.x specific project config', function() {
            const config = projectGenerator.createProjectConfig('TestProject');
            
            assert.strictEqual(config.engine, 'cocos-creator', 'Should use cocos-creator engine');
            assert.strictEqual(config.type, '3d', 'Should support 3D');
            assert.strictEqual(config.renderPipeline, 'builtin-forward', 'Should use forward pipeline');
            assert.strictEqual(config.name, 'TestProject', 'Should use provided name');
        });

        it('should create enhanced TypeScript config for 3.8.x', function() {
            const tsconfig = projectGenerator.createTSConfig();
            
            assert.strictEqual(tsconfig.compilerOptions.target, 'es6', 'Should target ES6');
            assert.strictEqual(tsconfig.compilerOptions.strict, true, 'Should enable strict mode');
            assert(tsconfig.compilerOptions.lib.includes('es2020'), 'Should include ES2020 lib');
            assert(tsconfig.compilerOptions.paths.cc, 'Should include cc path mapping');
        });

        it('should create 3.8.x specific project settings', function() {
            const settings = projectGenerator.createProjectSettings();
            
            assert(Array.isArray(settings['group-list']), 'Group list should be array in 3.8.x');
            assert(settings.physics, 'Should include physics settings');
            assert(settings.rendering, 'Should include rendering settings');
            assert(settings.animation, 'Should include animation settings');
            assert.strictEqual(settings.rendering.renderPipeline, 'builtin-forward', 'Should use forward pipeline');
        });

        it('should create package.json for 3.8.x', function() {
            const packageJson = projectGenerator.createPackageJson();
            
            assert.strictEqual(packageJson.name, 'cocos-creator-project', 'Should have correct name');
            assert(packageJson.scripts.build, 'Should include build script');
            assert(packageJson.scripts.preview, 'Should include preview script');
            assert(packageJson.engines.node, 'Should specify Node.js version requirement');
        });

        it('should create .gitignore content for 3.8.x', function() {
            const gitignore = projectGenerator.createGitignore();
            
            assert(gitignore.includes('/library/'), 'Should ignore library directory');
            assert(gitignore.includes('/temp/'), 'Should ignore temp directory');
            assert(gitignore.includes('/build/'), 'Should ignore build directory');
            assert(gitignore.includes('node_modules/'), 'Should ignore node_modules');
        });
    });

    describe('Integration Tests', function() {
        it('should handle complete 3.8.x project structure', function() {
            // This would be a more comprehensive integration test
            // Testing the complete flow from detection to generation
            
            // Mock a 3.8.x project structure
            const mockProjectStructure = {
                'application.js': 'System.import("cc").then(() => {});',
                'cc.js': 'export * from "./cocos/core";',
                'assets/': {}
            };

            // Test would verify complete processing pipeline
            assert(true, 'Integration test placeholder');
        });
    });

    afterEach(function() {
        // Clean up test artifacts
        if (fs.existsSync('./test-output')) {
            // Clean up test output directory
        }
    });
});

// Export for use in other test files
module.exports = {
    // Test utilities can be exported here
};
