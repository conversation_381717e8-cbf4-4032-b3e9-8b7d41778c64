System.register(["./_virtual_cc-D1oNkISf.js"],(function(e){"use strict";return{setters:[function(a){e({Acceleration:a.ed,AffineTransform:a.a7,AlphaKey:a.cf,AmbientInfo:a.df,AnimCurve:a.ad,Animation:a.ab,AnimationClip:a.a9,AnimationComponent:a.ab,AnimationManager:a.ah,AnimationState:a.aa,Asset:a.dG,AssetLibrary:a.d_,AssetManager:a.dW,AsyncDelegate:a.bP,Atlas:a.A,AudioClip:a.al,AudioPCMDataView:a.am,AudioSource:a.ak,AudioSourceComponent:a.ak,BASELINE_RATIO:a.z,BITMASK_TAG:a.cI,BaseNode:a.d3,BaseRenderData:a.r,BitMask:a.bh,BitmapFont:a.B,BlockInputEvents:a.eP,BlockInputEventsComponent:a.eP,BufferAsset:a.dH,BuiltinResMgr:a.e0,Button:a.eA,ButtonComponent:a.eA,CCBoolean:a.bF,CCClass:a.bz,CCFloat:a.bE,CCInteger:a.bD,CCLoader:a.dY,CCObject:a.bB,CCObjectFlags:a.bC,CCString:a.bG,CacheMode:a.n,CachedArray:a.bg,CallbacksInvoker:a.cM,Camera:a.dt,CameraComponent:a.dt,Canvas:a.C,CanvasComponent:a.C,Color:a.aL,ColorKey:a.ce,CompactValueTypeArray:a.bH,Component:a.d8,DEFAULT_OCTREE_DEPTH:a.dl,DEFAULT_WORLD_MAX_POS:a.dk,DEFAULT_WORLD_MIN_POS:a.dj,DebugMode:a.c1,DebugView:a.d0,Details:a.dE,Director:a.dz,DirectorEvent:a.dy,DynamicAtlasManager:a.a3,ENUM_TAG:a.cH,EPSILON:a.aQ,EasingMethod:a.cL,EditBox:a.eB,EditBoxComponent:a.eB,EditorExtendable:a.bM,EffectAsset:a.dP,Enum:a.bi,Event:a.e4,EventAcceleration:a.e5,EventGamepad:a.e9,EventHMD:a.eb,EventHandheld:a.ec,EventHandle:a.ea,EventHandler:a.d7,EventInfo:a.ae,EventKeyboard:a.e6,EventMouse:a.e7,EventTarget:a.bN,EventTouch:a.e8,Eventify:a.bO,ExtrapolationMode:a.c9,FogInfo:a.dh,Font:a.F,GCObject:a.cE,Game:a.dB,Gradient:a.cg,Graphics:a.G,GraphicsComponent:a.G,HALF_PI:a.aO,HorizontalTextAlignment:a.H,HtmlTextParser:a.y,ImageAsset:a.dM,Input:a.eh,InstanceMaterialType:a.I,InstancedBuffer:a.cY,JavaScript:a.dT,JsonAsset:a.dL,KeyCode:a.ee,LRUCache:a.J,Label:a.o,LabelAtlas:a.L,LabelComponent:a.o,LabelOutline:a.i,LabelOutlineComponent:a.i,LabelShadow:a.k,Layers:a.d5,Layout:a.eC,LayoutComponent:a.eC,LightProbeInfo:a.dq,MATH_FLOAT_ARRAY:a.bc,MIDDLE_RATIO:a.D,MaskComponent:a.p,Mat3:a.aE,Mat4:a.aF,Material:a.dQ,MathBase:a.bd,MeshBuffer:a.M,MeshRenderData:a.u,MissingScript:a.dw,MobilityMode:a.dd,ModelRenderer:a.du,Node:a.d3,NodeActivator:a.d9,NodeEventType:a.de,NodePool:a.e2,NodeSpace:a.db,ObjectCurve:a.cd,OctreeInfo:a.dm,Overflow:a.O,PageView:a.eL,PageViewComponent:a.eL,PageViewIndicator:a.eM,PageViewIndicatorComponent:a.eM,PipelineEventProcessor:a.c_,PipelineEventType:a.c$,PipelineInputAssemblerData:a.d2,PipelineSceneData:a.cX,PipelineStateManager:a.cZ,Pool:a.be,PostSettingsInfo:a.dp,Prefab:a.da,PrefabLink:a.dx,PrivateNode:a.ds,ProgressBar:a.eD,ProgressBarComponent:a.eD,QuadRenderData:a.Q,Quat:a.aC,QuatCurve:a.cb,QuatInterpolationMode:a.cc,RatioSampler:a.ac,RealCurve:a.c7,RealInterpolationMode:a.c8,Rect:a.aJ,RecyclePool:a.bf,RenderComponent:a.d,RenderData:a.t,RenderRoot2D:a.R,RenderTexture:a.dR,Renderable2D:a.d,RenderableComponent:a.du,Renderer:a.dv,RenderingSubMesh:a.dI,ResolutionPolicy:a.eT,RichText:a.q,RichTextComponent:a.q,Root:a.ap,SafeArea:a.eN,SafeAreaComponent:a.eN,Scene:a.d4,SceneAsset:a.dJ,SceneGlobals:a.dr,Scheduler:a.c6,Script:a.dS,ScrollBar:a.eE,ScrollBarComponent:a.eE,ScrollView:a.eF,ScrollViewComponent:a.eF,Settings:a.ci,SettingsCategory:a.ch,ShadowsInfo:a.di,Size:a.aH,SkelAnimDataHub:a.eo,SkeletalAnimation:a.er,SkeletalAnimationComponent:a.er,SkeletalAnimationState:a.ep,SkinInfo:a.dn,SkyboxInfo:a.dg,Slider:a.eG,SliderComponent:a.eG,Socket:a.eq,Sorting:a.a6,SortingLayers:a.a5,Sprite:a.g,SpriteAtlas:a.a,SpriteComponent:a.g,SpriteFrame:a.c,SpriteFrameEvent:a.b,SpriteRenderer:a.f,StencilManager:a.S,SubContextView:a.eQ,System:a.cl,SystemEvent:a.ej,SystemEventType:a.e3,SystemPriority:a.ck,TTFFont:a.T,TWO_PI:a.aP,TangentWeightMode:a.ca,TextAsset:a.dK,Texture2D:a.dN,TextureCube:a.dO,Toggle:a.eH,ToggleComponent:a.eH,ToggleContainer:a.eI,ToggleContainerComponent:a.eI,Touch:a.ef,TransformBit:a.dc,Tween:a.ex,TweenAction:a.ew,TweenSystem:a.ev,TypeScript:a.dU,UI:a.w,UIComponent:a.U,UICoordinateTracker:a.eO,UICoordinateTrackerComponent:a.eO,UIDrawBatch:a.x,UIMeshRenderer:a.h,UIModelComponent:a.h,UIOpacity:a.m,UIOpacityComponent:a.m,UIRenderable:a.d,UIRenderer:a.d,UIReorderComponent:a.eV,UISkew:a.eW,UIStaticBatch:a.j,UIStaticBatchComponent:a.j,UITransform:a.e,UITransformComponent:a.e,UIVertexFormat:a.v,VERSION:a.at,ValueType:a.bk,Vec2:a.aw,Vec3:a.ay,Vec4:a.aA,VerticalTextAlignment:a.V,VideoClip:a.eX,VideoPlayer:a.eY,View:a.eS,ViewGroup:a.eJ,WebGL2Device:a.e_,WebGLDevice:a.em,WebView:a.eZ,Widget:a.eK,WidgetComponent:a.eK,WorldNode3DToLocalNodeUI:a.bv,WorldNode3DToWorldNodeUI:a.bw,__checkObsoleteInNamespace__:a.bu,__checkObsolete__:a.bt,_decorator:a.by,_resetDebugSetting:a.cO,absMax:a.b8,absMaxComponent:a.b7,animation:a.a8,applyMixins:a.cN,approx:a.aS,assert:a.bU,assertID:a.bZ,assertIsNonNullable:a.cS,assertIsTrue:a.cT,assertsArrayIndex:a.cU,assetManager:a.dV,bezier:a.co,bezierByTime:a.cp,binarySearch:a.cP,binarySearchBy:a.cR,binarySearchEpsilon:a.cQ,bits:a.av,builtinResMgr:a.e1,ccenum:a.bj,cclegacy:a.au,clamp:a.aT,clamp01:a.aU,color:a.aM,computeRatioByType:a.ag,convertUtils:a.bx,debug:a.bQ,debugID:a.bV,deprecateModuleExportedName:a.bs,deserialize:a.dD,deserializeTag:a.bJ,director:a.dA,disallowAnimation:a.cy,displayName:a.ct,displayOrder:a.cu,dynamicAtlasManager:a.a4,easing:a.cm,editable:a.cq,editorExtrasTag:a.bI,enumerableProps:a.b9,equals:a.aR,error:a.bS,errorID:a.bX,find:a.d6,flattenCodeArray:a.cV,floatToHalf:a.ba,formerlySerializedAs:a.cA,fragmentText:a.a2,game:a.dC,garbageCollectionManager:a.cD,geometry:a.as,getBaselineOffset:a.E,getEnglishWordPartAtFirst:a.a0,getEnglishWordPartAtLast:a.a1,getError:a.c0,getPathFromRoot:a.ai,getPhaseID:a.d1,getSerializationMetadata:a.bL,getSymbolAt:a.X,getSymbolCodeAt:a.Y,getSymbolLength:a.W,getWorldTransformUntilRoot:a.aj,gfx:a.an,graphicsAssembler:a.en,halfToFloat:a.bb,input:a.eg,instantiate:a.dF,inverseLerp:a.b6,isCCClassOrFastDefined:a.bA,isCCObject:a.cJ,isDisplayStats:a.b_,isEnglishWordPartAtFirst:a.Z,isEnglishWordPartAtLast:a.$,isUnicodeCJK:a.K,isUnicodeSpace:a.N,isValid:a.cK,js:a.bl,labelAssembler:a.l,lerp:a.aV,loadWasmModuleSpine:a.et,loader:a.dZ,log:a.bR,logID:a.bW,macro:a.c4,markAsWarning:a.br,mat4:a.aG,math:a.aq,memop:a.ar,misc:a.bm,murmurhash2_32_gc:a.cn,native:a.ek,nextPow2:a.b3,override:a.cz,path:a.bn,pingPong:a.b5,pipeline:a.cW,preTransforms:a.aN,pseudoRandom:a.b0,pseudoRandomRange:a.b1,pseudoRandomRangeInt:a.b2,quat:a.aD,random:a.aY,randomRange:a.a_,randomRangeInt:a.a$,range:a.cv,rangeStep:a.cw,rect:a.aK,removeProperty:a.bq,renderer:a.ao,rendering:a.el,repeat:a.b4,replaceProperty:a.bp,resources:a.dX,safeMeasureText:a.P,sampleAnimationCurve:a.af,screen:a.c2,serializable:a.cB,serializeTag:a.bK,setDefaultLogTimes:a.bo,setDisplayStats:a.b$,setPropertyEnumType:a.cF,setPropertyEnumTypeOnAttrs:a.cG,setRandGenerator:a.aZ,settings:a.cj,shift:a.cC,size:a.aI,slide:a.cx,sp:a.es,spriteAssembler:a.s,sys:a.c3,systemEvent:a.ei,toDegree:a.aX,toRadian:a.aW,tooltip:a.cr,tween:a.ey,tweenProgress:a.eu,tweenUtil:a.ez,url:a.d$,v2:a.ax,v3:a.az,v4:a.aB,view:a.eU,visible:a.cs,visibleRect:a.c5,warn:a.bT,warnID:a.bY,widgetManager:a.eR})}],execute:function(){}}}));
