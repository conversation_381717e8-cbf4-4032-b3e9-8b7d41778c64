System.register([],(function(r){"use strict";return{execute:function(){var n;r("default",(n="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(r={}){var f,i,e;f||(f=void 0!==r?r:{}),f.ready=new Promise(((r,n)=>{i=r,e=n}));var t,u=Object.assign({},f),o="";function a(r){return f.locateFile?f.locateFile(r,o):o+r}"undefined"!=typeof document&&document.currentScript&&(o=document.currentScript.src),n&&(o=n),o=0!==o.indexOf("blob:")?o.substr(0,o.replace(/[?#].*/,"").lastIndexOf("/")+1):"",t=(r,n)=>{var f=new XMLHttpRequest;f.open("GET",J,!0),f.responseType="arraybuffer",f.onload=()=>{200==f.status||0==f.status&&f.response?r(f.response):n()},f.onerror=n,f.send(null)};var c,b=f.printErr||console.error.bind(console);function k(r){this.exports=function(r){var n=new ArrayBuffer(16),f=new Int32Array(n),i=new Float32Array(n),e=new Float64Array(n);function t(r){return f[r]}function u(r,n){f[r]=n}function o(){return e[0]}function a(r){e[0]=r}function c(){throw new Error("abort")}function b(r){i[2]=r}function k(){return i[2]}return function(r){var n=r.a,f=n.a,i=f.buffer;f.grow=function(r){r|=0;var n=0|Ma(),t=n+r|0;if(n<t&&t<65536){var u=new ArrayBuffer(m(t,65536));new Int8Array(u).set(e),e=new Int8Array(u),s=new Int16Array(u),v=new Int32Array(u),l=new Uint8Array(u),h=new Uint16Array(u),d=new Uint32Array(u),p=new Float32Array(u),y=new Float64Array(u),i=u,f.buffer=i}return n};var e=new Int8Array(i),s=new Int16Array(i),v=new Int32Array(i),l=new Uint8Array(i),h=new Uint16Array(i),d=new Uint32Array(i),p=new Float32Array(i),y=new Float64Array(i),m=Math.imul,w=Math.fround,g=Math.abs,F=Math.clz32,A=Math.floor,T=Math.sqrt,$=n.b,I=n.c,C=n.d,P=n.e,E=n.f,O=n.g,R=n.h,S=n.i,W=n.j,G=n.k,U=n.l,j=n.m,H=n.n,L=n.o,M=n.p,_=n.q,z=n.r,x=n.s,J=n.t,K=n.u,B=n.v,N=n.w,q=n.x,D=n.y,V=88016,Z=0;function Y(){var r=0,n=0;D(21664,4855),O(21665,5283,4,0,-1),O(21666,3978,4,0,-1),O(21667,6123,4,0,-1),O(21668,5231,4,0,-1),O(21669,6066,4,0,-1),O(21670,5962,4,0,-1),O(21671,5928,4,0,-1),O(21672,6008,4,0,-1),O(21673,6038,4,0,-1),O(21674,3574,4,0,-1),O(21675,3790,4,0,-1),O(21676,5248,4,0,-1),C(21677,21678,21679,0,11288,387,11291,0,11291,0,5303,11293,388),P(21677,1,11296,11288,389,390),r=Jt(8),v[r+4>>2]=0,v[r>>2]=391,$(21677,4929,4,11328,11344,392,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=393,$(21677,4931,2,11352,11360,394,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=395,$(21677,2249,3,11364,11376,396,0|r,0,0),C(21683,21684,21685,0,11288,397,11291,0,11291,0,2377,11293,398),P(21683,1,11384,11288,399,400),r=Jt(8),v[r+4>>2]=0,v[r>>2]=401,$(21683,4929,4,11392,11408,402,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=403,$(21683,4931,2,11416,11360,404,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=405,$(21683,2249,3,11424,11436,406,0|r,0,0),r=Jt(4),v[r>>2]=407,$(21683,2242,4,11456,11408,1598,0|r,0,0),C(21688,21689,21690,0,11288,408,11291,0,11291,0,2357,11293,409),P(21688,1,11472,11288,410,411),r=Jt(8),v[r+4>>2]=0,v[r>>2]=412,$(21688,4929,4,11488,11344,413,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=414,$(21688,4931,2,11504,11360,415,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=416,$(21688,2249,3,11512,11376,417,0|r,0,0),r=Jt(4),v[r>>2]=418,$(21688,2242,4,11536,11344,1599,0|r,0,0),C(21691,21692,21693,0,11288,419,11291,0,11291,0,2143,11293,420),P(21691,1,11552,11288,421,422),r=Jt(8),v[r+4>>2]=0,v[r>>2]=423,$(21691,4929,4,11568,11344,424,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=425,$(21691,4931,2,11584,11360,426,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=427,$(21691,2249,3,11592,11376,428,0|r,0,0),r=Jt(4),v[r>>2]=429,$(21691,2242,4,11616,11344,1600,0|r,0,0),C(21694,21695,21696,0,11288,430,11291,0,11291,0,1760,11293,431),P(21694,1,11632,11288,432,433),r=Jt(8),v[r+4>>2]=0,v[r>>2]=434,$(21694,4929,4,11664,11344,435,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=436,$(21694,4931,2,11680,11360,437,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=438,$(21694,2249,3,11688,11376,439,0|r,0,0),r=Jt(4),v[r>>2]=440,$(21694,2242,4,11712,11344,1601,0|r,0,0),C(21698,21699,21700,0,11288,441,11291,0,11291,0,2125,11293,442),P(21698,1,11728,11288,443,444),r=Jt(8),v[r+4>>2]=0,v[r>>2]=445,$(21698,4929,4,11744,11344,446,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=447,$(21698,4931,2,11760,11360,448,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=449,$(21698,2249,3,11768,11376,450,0|r,0,0),r=Jt(4),v[r>>2]=451,$(21698,2242,4,11792,11344,1602,0|r,0,0),C(21701,21702,21703,0,11288,452,11291,0,11291,0,2391,11293,453),P(21701,1,11808,11288,454,455),r=Jt(8),v[r+4>>2]=0,v[r>>2]=456,$(21701,4929,4,11824,11344,457,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=458,$(21701,4931,2,11840,11360,459,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=460,$(21701,2249,3,11848,11376,461,0|r,0,0),r=Jt(4),v[r>>2]=462,$(21701,2242,4,11872,11344,1603,0|r,0,0),C(21704,21705,21706,0,11288,463,11291,0,11291,0,1498,11293,464),P(21704,1,11888,11288,465,466),r=Jt(8),v[r+4>>2]=0,v[r>>2]=467,$(21704,4929,4,11904,11344,468,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=469,$(21704,4931,2,11920,11360,470,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=471,$(21704,2249,3,11928,11376,472,0|r,0,0),r=Jt(4),v[r>>2]=473,$(21704,2242,4,11952,11344,1604,0|r,0,0),C(21708,21709,21710,0,11288,474,11291,0,11291,0,4867,11293,475),P(21708,1,11968,11288,476,477),r=Jt(8),v[r+4>>2]=0,v[r>>2]=478,$(21708,4929,4,11984,11344,479,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=480,$(21708,4931,2,12e3,11360,481,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=482,$(21708,2249,3,12008,11376,483,0|r,0,0),r=Jt(4),v[r>>2]=484,$(21708,2242,4,12032,11344,1605,0|r,0,0),C(21711,21712,21713,0,11288,485,11291,0,11291,0,3283,11293,486),P(21711,1,12048,11288,487,488),r=Jt(8),v[r+4>>2]=0,v[r>>2]=489,$(21711,4929,4,12064,11344,490,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=491,$(21711,4931,2,12080,11360,492,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=493,$(21711,2249,3,12088,11376,494,0|r,0,0),C(21714,21715,21716,0,11288,495,11291,0,11291,0,3500,11293,496),P(21714,1,12100,11288,497,498),r=Jt(8),v[r+4>>2]=0,v[r>>2]=499,$(21714,4929,4,12112,11344,500,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=501,$(21714,4931,2,12128,11360,502,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=503,$(21714,2249,3,12136,11376,504,0|r,0,0),C(21718,21719,21720,0,11288,505,11291,0,11291,0,3340,11293,506),P(21718,1,12148,11288,507,508),r=Jt(8),v[r+4>>2]=0,v[r>>2]=509,$(21718,4929,4,12160,11344,510,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=511,$(21718,4931,2,12176,11360,512,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=513,$(21718,2249,3,12184,11376,514,0|r,0,0),C(21722,21723,21724,0,11288,515,11291,0,11291,0,3386,11293,516),P(21722,1,12196,11288,517,518),r=Jt(8),v[r+4>>2]=0,v[r>>2]=519,$(21722,4929,4,12208,11344,520,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=521,$(21722,4931,2,12224,11360,522,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=523,$(21722,2249,3,12232,11376,524,0|r,0,0),C(21725,21726,21727,0,11288,525,11291,0,11291,0,3449,11293,526),P(21725,1,12244,11288,527,528),r=Jt(8),v[r+4>>2]=0,v[r>>2]=529,$(21725,4929,4,12256,11344,530,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=531,$(21725,4931,2,12272,11360,532,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=533,$(21725,2249,3,12280,11376,534,0|r,0,0),C(21728,21729,21730,0,11288,535,11291,0,11291,0,3360,11293,536),P(21728,1,12292,11288,537,538),r=Jt(8),v[r+4>>2]=0,v[r>>2]=539,$(21728,4929,4,12304,11344,540,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=541,$(21728,4931,2,12320,11360,542,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=543,$(21728,2249,3,12328,11376,544,0|r,0,0),C(21731,21732,21733,0,11288,545,11291,0,11291,0,3127,11293,546),P(21731,1,12340,11288,547,548),r=Jt(8),v[r+4>>2]=0,v[r>>2]=549,$(21731,4929,4,12352,11344,550,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=551,$(21731,4931,2,12368,11360,552,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=553,$(21731,2249,3,12376,11376,554,0|r,0,0),C(21735,21736,21737,0,11288,555,11291,0,11291,0,3262,11293,556),P(21735,1,12388,11288,557,558),r=Jt(8),v[r+4>>2]=0,v[r>>2]=559,$(21735,4929,4,12400,11344,560,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=561,$(21735,4931,2,12416,11360,562,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=563,$(21735,2249,3,12424,11376,564,0|r,0,0),C(21739,21740,21741,0,11288,565,11291,0,11291,0,3479,11293,566),P(21739,1,12436,11288,567,568),r=Jt(8),v[r+4>>2]=0,v[r>>2]=569,$(21739,4929,4,12448,11344,570,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=571,$(21739,4931,2,12464,11360,572,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=573,$(21739,2249,3,12472,11376,574,0|r,0,0),C(21743,21744,21745,0,11288,575,11291,0,11291,0,3224,11293,576),P(21743,1,12484,11288,577,578),r=Jt(8),v[r+4>>2]=0,v[r>>2]=579,$(21743,4929,4,12496,11344,580,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=581,$(21743,4931,2,12512,11360,582,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=583,$(21743,2249,3,12520,11376,584,0|r,0,0),C(21747,21748,21749,0,11288,585,11291,0,11291,0,3241,11293,586),P(21747,1,12532,11288,587,588),r=Jt(8),v[r+4>>2]=0,v[r>>2]=589,$(21747,4929,4,12544,11344,590,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=591,$(21747,4931,2,12560,11360,592,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=593,$(21747,2249,3,12568,11376,594,0|r,0,0),C(21751,21752,21753,0,11288,595,11291,0,11291,0,3174,11293,596),P(21751,1,12580,11288,597,598),r=Jt(8),v[r+4>>2]=0,v[r>>2]=599,$(21751,4929,4,12592,11344,600,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=601,$(21751,4931,2,12608,11360,602,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=603,$(21751,2249,3,12616,11376,604,0|r,0,0),C(21754,21755,21756,0,11288,605,11291,0,11291,0,3421,11293,606),P(21754,1,12628,11288,607,608),r=Jt(8),v[r+4>>2]=0,v[r>>2]=609,$(21754,4929,4,12640,11344,610,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=611,$(21754,4931,2,12656,11360,612,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=613,$(21754,2249,3,12664,11376,614,0|r,0,0),C(21757,21758,21759,0,11288,615,11291,0,11291,0,3143,11293,616),P(21757,1,12676,11288,617,618),r=Jt(8),v[r+4>>2]=0,v[r>>2]=619,$(21757,4929,4,12688,11344,620,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=621,$(21757,4931,2,12704,11360,622,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=623,$(21757,2249,3,12712,11376,624,0|r,0,0),C(21760,21761,21762,0,11288,625,11291,0,11291,0,3198,11293,626),P(21760,1,12724,11288,627,628),r=Jt(8),v[r+4>>2]=0,v[r>>2]=629,$(21760,4929,4,12736,11344,630,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=631,$(21760,4931,2,12752,11360,632,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=633,$(21760,2249,3,12760,11376,634,0|r,0,0),C(21763,21764,21765,0,11288,635,11291,0,11291,0,3299,11293,636),P(21763,1,12772,11288,637,638),r=Jt(8),v[r+4>>2]=0,v[r>>2]=639,$(21763,4929,4,12784,11344,640,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=641,$(21763,4931,2,12800,11360,642,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=643,$(21763,2249,3,12808,11376,644,0|r,0,0),r=Jt(4),v[r>>2]=645,$(21763,2242,4,12832,11344,1606,0|r,0,0),C(21766,21767,21768,0,11288,646,11291,0,11291,0,3100,11293,647),P(21766,1,12848,11288,648,649),r=Jt(8),v[r+4>>2]=0,v[r>>2]=650,$(21766,4929,4,12864,11344,651,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=652,$(21766,4931,2,12880,11360,653,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=654,$(21766,2249,3,12888,11376,655,0|r,0,0),C(21770,21771,21772,0,11288,656,11291,0,11291,0,3319,11293,657),P(21770,1,12900,11288,658,659),r=Jt(8),v[r+4>>2]=0,v[r>>2]=660,$(21770,4929,4,12912,11344,661,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=662,$(21770,4931,2,12928,11360,663,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=664,$(21770,2249,3,12936,11376,665,0|r,0,0),C(21773,21774,21775,0,11288,666,11291,0,11291,0,3079,11293,667),P(21773,1,12948,11288,668,669),r=Jt(8),v[r+4>>2]=0,v[r>>2]=670,$(21773,4929,4,12976,11344,671,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=672,$(21773,4931,2,12992,11360,673,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=674,$(21773,2249,3,13e3,11376,675,0|r,0,0),C(21777,21778,21779,0,11288,676,11291,0,11291,0,3052,11293,677),P(21777,1,13012,11288,678,679),r=Jt(8),v[r+4>>2]=0,v[r>>2]=680,$(21777,4929,4,13040,11344,681,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=682,$(21777,4931,2,13056,11360,683,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=684,$(21777,2249,3,13064,11376,685,0|r,0,0),C(21780,21781,21782,0,11288,686,11291,0,11291,0,6946,11293,687),P(21780,1,13076,11288,688,689),P(21780,3,13080,13092,690,691),r=Jt(4),v[r>>2]=0,n=Jt(4),v[n>>2]=0,I(21780,1391,21686,13097,692,0|r,21686,13101,693,0|n),r=Jt(4),v[r>>2]=4,n=Jt(4),v[n>>2]=4,I(21780,1217,21686,13097,692,0|r,21686,13101,693,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=694,$(21780,2242,4,13120,13136,695,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=696,$(21780,4696,2,13144,13097,697,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=698,$(21780,4936,2,13152,11360,699,0|r,0,0),C(21783,21784,21785,0,11288,700,11291,0,11291,0,3548,11293,701),P(21783,1,13160,11288,702,703),P(21783,5,13168,13188,704,705),r=Jt(8),v[r+4>>2]=0,v[r>>2]=706,$(21783,2242,6,13200,13224,707,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=708,$(21783,6325,6,13200,13224,707,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=709,$(21783,3772,2,13232,11360,710,0|r,0,0),r=Jt(4),v[r>>2]=4,n=Jt(4),v[n>>2]=4,I(21783,3765,21686,13097,711,0|r,21686,13101,712,0|n),r=Jt(4),v[r>>2]=8,n=Jt(4),v[n>>2]=8,I(21783,4927,21686,13097,711,0|r,21686,13101,712,0|n),r=Jt(4),v[r>>2]=12,n=Jt(4),v[n>>2]=12,I(21783,6345,21686,13097,711,0|r,21686,13101,712,0|n),r=Jt(4),v[r>>2]=16,n=Jt(4),v[n>>2]=16,I(21783,6560,21686,13097,711,0|r,21686,13101,712,0|n),C(4305,7474,7468,0,11288,713,11291,0,11291,0,4305,11293,714),r=Jt(8),v[r+4>>2]=1,v[r>>2]=4,$(4305,1198,3,13240,13252,715,0|r,1,0),C(21786,21787,21788,0,11288,716,11291,0,11291,0,2253,11293,717),P(21786,1,13260,11288,718,719),C(6406,8004,7998,0,11288,720,11291,0,11291,0,6406,11293,721),P(6406,2,13264,11360,722,723),r=Jt(8),v[r+4>>2]=0,v[r>>2]=724,I(6406,5773,21664,11360,725,0|r,0,0,0,0),r=Jt(4),v[r>>2]=16,n=Jt(4),v[n>>2]=16,I(6406,3633,21681,11360,726,0|r,21681,13272,727,0|n),r=Jt(4),v[r>>2]=20,n=Jt(4),v[n>>2]=20,I(6406,6279,21789,11360,728,0|r,21789,13272,729,0|n),C(6385,7954,7948,6406,11288,730,11288,731,11288,732,6385,11293,733),P(6385,2,13280,11360,734,735),r=Jt(4),v[r>>2]=736,$(6385,2780,2,13288,11360,1607,0|r,0,0),r=Jt(4),v[r>>2]=40,n=Jt(4),v[n>>2]=40,I(6385,2246,21790,11360,737,0|r,21790,13272,738,0|n),r=Jt(4),v[r>>2]=44,n=Jt(4),v[n>>2]=44,I(6385,3998,21687,11360,739,0|r,21687,13272,740,0|n),r=Jt(4),v[r>>2]=48,n=Jt(4),v[n>>2]=48,I(6385,2581,21789,11360,741,0|r,21789,13272,742,0|n),r=Jt(4),v[r>>2]=49,n=Jt(4),v[n>>2]=49,I(6385,4833,21789,11360,741,0|r,21789,13272,742,0|n),r=Jt(4),v[r>>2]=50,n=Jt(4),v[n>>2]=50,I(6385,4494,21789,11360,741,0|r,21789,13272,742,0|n),r=Jt(4),v[r>>2]=52,n=Jt(4),v[n>>2]=52,I(6385,1231,21686,13097,743,0|r,21686,13101,744,0|n),r=Jt(4),v[r>>2]=56,n=Jt(4),v[n>>2]=56,I(6385,2590,21686,13097,743,0|r,21686,13101,744,0|n),C(6402,7978,7972,6406,11288,745,11288,746,11288,747,6402,11293,748),P(6402,2,13296,11360,749,750),r=Jt(4),v[r>>2]=751,$(6402,2780,2,13304,11360,1608,0|r,0,0),r=Jt(4),v[r>>2]=40,n=Jt(4),v[n>>2]=40,I(6402,2246,21791,11360,752,0|r,21791,13272,753,0|n),r=Jt(4),v[r>>2]=44,n=Jt(4),v[n>>2]=44,I(6402,5915,21671,11360,754,0|r,21671,13272,755,0|n),r=Jt(4),v[r>>2]=48,n=Jt(4),v[n>>2]=48,I(6402,5996,21672,11360,756,0|r,21672,13272,757,0|n),r=Jt(4),v[r>>2]=52,n=Jt(4),v[n>>2]=52,I(6402,6027,21673,11360,758,0|r,21673,13272,759,0|n),r=Jt(4),v[r>>2]=56,n=Jt(4),v[n>>2]=56,I(6402,4065,21686,13097,760,0|r,21686,13101,761,0|n),r=Jt(4),v[r>>2]=60,n=Jt(4),v[n>>2]=60,I(6402,3948,21686,13097,760,0|r,21686,13101,761,0|n),r=Jt(4),v[r>>2]=64,n=Jt(4),v[n>>2]=64,I(6402,4921,21686,13097,760,0|r,21686,13101,761,0|n),r=Jt(4),v[r>>2]=68,n=Jt(4),v[n>>2]=68,I(6402,1269,21686,13097,760,0|r,21686,13101,761,0|n),r=Jt(4),v[r>>2]=72,n=Jt(4),v[n>>2]=72,I(6402,1279,21686,13097,760,0|r,21686,13101,761,0|n),C(21792,21793,21794,0,11288,762,11291,0,11291,0,2976,11293,763),P(21792,1,13312,11288,764,765),r=Jt(8),v[r+4>>2]=0,v[r>>2]=766,$(21792,5116,4,13328,11344,767,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=768,$(21792,1682,4,13344,13136,769,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=770,$(21792,2071,6,13360,13224,771,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=772,$(21792,3872,3,13384,11376,773,0|r,0,0),r=Jt(4),v[r>>2]=774,$(21792,1668,4,13408,13136,1609,0|r,0,0),r=Jt(4),v[r>>2]=775,$(21792,2053,6,13424,13224,1610,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=776,$(21792,4740,2,13448,13097,777,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=778,$(21792,2190,2,13448,13097,777,0|r,0,0),C(21796,21746,21797,0,11288,779,11291,0,11291,0,1817,11293,780),P(21796,3,13456,13468,781,782),r=Jt(4),v[r>>2]=783,I(21796,6347,21798,11360,784,0|r,0,0,0,0),r=Jt(4),v[r>>2]=12,n=Jt(4),v[n>>2]=12,I(21796,5013,21687,11360,785,0|r,21687,13272,786,0|n),r=Jt(4),v[r>>2]=16,n=Jt(4),v[n>>2]=16,I(21796,5022,21686,13097,787,0|r,21686,13101,788,0|n),r=Jt(4),v[r>>2]=20,n=Jt(4),v[n>>2]=20,I(21796,5033,21664,11360,789,0|r,21664,13272,790,0|n),r=Jt(4),v[r>>2]=8,I(21796,5688,21686,13097,787,0|r,0,0,0,0),r=Jt(4),v[r>>2]=32,n=Jt(4),v[n>>2]=32,I(21796,5681,21686,13097,787,0|r,21686,13101,788,0|n),r=Jt(4),v[r>>2]=36,n=Jt(4),v[n>>2]=36,I(21796,6100,21686,13097,787,0|r,21686,13101,788,0|n),C(21798,21742,21799,0,11288,791,11291,0,11291,0,6421,11293,792),P(21798,2,13476,11360,793,794),r=Jt(8),v[r+4>>2]=0,v[r>>2]=795,I(21798,5773,21664,11360,796,0|r,0,0,0,0),r=Jt(4),v[r>>2]=16,n=Jt(4),v[n>>2]=16,I(21798,5013,21687,11360,797,0|r,21687,13272,798,0|n),r=Jt(4),v[r>>2]=20,n=Jt(4),v[n>>2]=20,I(21798,5022,21686,13097,799,0|r,21686,13101,800,0|n),r=Jt(4),v[r>>2]=24,n=Jt(4),v[n>>2]=24,I(21798,5033,21664,11360,801,0|r,21664,13272,802,0|n),r=Jt(4),v[r>>2]=36,n=Jt(4),v[n>>2]=36,I(21798,4765,21664,11360,801,0|r,21664,13272,802,0|n),r=Jt(4),v[r>>2]=48,n=Jt(4),v[n>>2]=48,I(21798,5681,21686,13097,799,0|r,21686,13101,800,0|n),r=Jt(4),v[r>>2]=52,n=Jt(4),v[n>>2]=52,I(21798,6100,21686,13097,799,0|r,21686,13101,800,0|n),C(2042,7281,7275,0,11288,803,11291,0,11291,0,2042,11293,804),r=Jt(8),v[r+4>>2]=0,v[r>>2]=805,I(2042,5773,21664,11360,806,0|r,0,0,0,0),C(1877,7140,7134,2042,11288,807,11288,808,11288,809,1877,11293,810),r=Jt(8),v[r+4>>2]=0,v[r>>2]=811,I(1877,6263,21687,11360,812,0|r,0,0,0,0),r=Jt(4),v[r>>2]=813,$(1877,2780,2,13484,11360,1611,0|r,0,0),r=Jt(4),v[r>>2]=814,$(1877,2910,2,13492,11360,1612,0|r,0,0),r=Jt(4),v[r>>2]=52,n=Jt(4),v[n>>2]=52,I(1877,4703,21681,11360,815,0|r,21681,13272,816,0|n),r=Jt(4),v[r>>2]=56,n=Jt(4),v[n>>2]=56,I(1877,1961,1877,11360,817,0|r,1877,13272,818,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=819,$(1877,2939,8,13504,13536,820,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=821,$(1877,3822,3,13548,13272,822,0|r,0,0),C(1855,7111,7105,1877,11288,823,11288,824,11288,825,1855,11293,826),P(1855,2,13560,11360,827,828),r=Jt(8),v[r+4>>2]=0,v[r>>2]=805,I(1855,5773,21664,11360,829,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(1855,1168,2,13568,11360,830,0|r,0,0),C(2017,7255,7249,1877,11288,831,11288,832,11288,833,2017,11293,834),P(2017,2,13576,11360,835,836),r=Jt(8),v[r+4>>2]=0,v[r>>2]=837,n=Jt(8),v[n+4>>2]=0,v[n>>2]=838,I(2017,1614,21791,11360,839,0|r,21791,13272,840,0|n),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(2017,1168,2,13584,11360,841,0|r,0,0),C(1999,7233,7227,1877,11288,842,11288,843,11288,844,1999,11293,845),P(1999,2,13592,11360,846,847),r=Jt(4),v[r>>2]=168,n=Jt(4),v[n>>2]=168,I(1999,4749,21664,11360,848,0|r,21664,13272,849,0|n),ct(3018,850),ct(3011,851),bt(2841,852),r=Jt(4),v[r>>2]=853,I(1999,3520,21783,11360,854,0|r,0,0,0,0),r=Jt(4),v[r>>2]=196,n=Jt(4),v[n>>2]=196,I(1999,4734,21686,13097,855,0|r,21686,13101,856,0|n),r=Jt(4),v[r>>2]=200,n=Jt(4),v[n>>2]=200,I(1999,2183,21686,13097,855,0|r,21686,13101,856,0|n);r=Jt(4),v[r>>2]=224,n=Jt(4),v[n>>2]=224,I(1999,4723,21687,11360,857,0|r,21687,13272,858,0|n),bt(2877,859),r=Jt(8),v[r+4>>2]=0,v[r>>2]=860,$(1999,3031,2,13616,13624,861,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=862,$(1999,4800,2,13628,11360,863,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=864,$(1999,4786,3,13636,13272,865,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(1999,1168,2,13648,11360,866,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=867,$(1999,4814,2,13628,11360,863,0|r,0,0),C(1981,7211,7205,1877,11288,868,11288,869,11288,870,1981,11293,871),P(1981,2,13656,11360,872,873),r=Jt(4),v[r>>2]=874,$(1981,2730,2,13664,11360,1615,0|r,0,0),r=Jt(4),v[r>>2]=80,n=Jt(4),v[n>>2]=80,I(1981,6272,21789,11360,875,0|r,21789,13272,876,0|n),r=Jt(4),v[r>>2]=81,n=Jt(4),v[n>>2]=81,I(1981,6311,21789,11360,875,0|r,21789,13272,876,0|n),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(1981,1168,2,13672,11360,877,0|r,0,0),C(1897,7164,7158,2042,11288,878,11288,879,11288,880,1897,11293,881),P(1897,2,13680,11360,882,883),r=Jt(4),v[r>>2]=20,n=Jt(4),v[n>>2]=20,I(1897,1391,21686,13097,884,0|r,21686,13101,885,0|n),r=Jt(4),v[r>>2]=24,n=Jt(4),v[n>>2]=24,I(1897,1217,21686,13097,884,0|r,21686,13101,885,0|n),r=Jt(4),v[r>>2]=28,n=Jt(4),v[n>>2]=28,I(1897,4056,21686,13097,884,0|r,21686,13101,885,0|n),r=Jt(4),v[r>>2]=886,$(1897,3957,5,13696,13716,1616,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=887,$(1897,4122,3,13724,11436,888,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(1897,1168,2,13736,11360,889,0|r,0,0),C(1944,7187,7181,2042,11288,890,11288,891,11288,892,1944,11293,893),P(1944,2,13744,11360,894,895),r=Jt(4),v[r>>2]=32,n=Jt(4),v[n>>2]=32,I(1944,1391,21686,13097,896,0|r,21686,13101,897,0|n),r=Jt(4),v[r>>2]=36,n=Jt(4),v[n>>2]=36,I(1944,1217,21686,13097,896,0|r,21686,13101,897,0|n),r=Jt(4),v[r>>2]=44,n=Jt(4),v[n>>2]=44,I(1944,6719,21686,13097,896,0|r,21686,13101,897,0|n),r=Jt(4),v[r>>2]=48,n=Jt(4),v[n>>2]=48,I(1944,6626,21686,13097,896,0|r,21686,13101,897,0|n),r=Jt(4),v[r>>2]=40,n=Jt(4),v[n>>2]=40,I(1944,4056,21686,13097,896,0|r,21686,13101,897,0|n),r=Jt(4),v[r>>2]=52,n=Jt(4),v[n>>2]=52,I(1944,4734,21686,13097,896,0|r,21686,13101,897,0|n),r=Jt(4),v[r>>2]=56,n=Jt(4),v[n>>2]=56,I(1944,2183,21686,13097,896,0|r,21686,13101,897,0|n),r=Jt(4),v[r>>2]=898,I(1944,3520,21783,11360,899,0|r,0,0,0,0),r=Jt(4),v[r>>2]=116,n=Jt(4),v[n>>2]=116,I(1944,4749,21664,11360,900,0|r,21664,13272,901,0|n),kt(2215,902),r=Jt(8),v[r+4>>2]=0,v[r>>2]=903,$(1944,3004,7,13760,13788,904,0|r,0,0),kt(3011,905),r=Jt(8),v[r+4>>2]=0,v[r>>2]=906,$(1944,2233,2,13800,13624,907,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=908,$(1944,2939,6,13808,13832,909,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(1944,1168,2,13840,11360,910,0|r,0,0),C(3667,7399,7393,0,11288,911,11291,0,11291,0,3667,11293,912),r=Jt(8),v[r+4>>2]=1,v[r>>2]=32,$(3667,2014,4,13856,13872,913,0|r,1,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=28,$(3667,1894,4,13888,13872,914,0|r,1,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=24,$(3667,1978,4,13904,13872,915,0|r,1,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=20,$(3667,1852,4,13920,13872,916,0|r,1,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=16,$(3667,1996,5,13936,13956,917,0|r,1,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(3667,1941,5,13968,13956,918,0|r,1,0),C(3662,7370,7364,3667,11288,919,11288,920,11288,921,3662,11293,922),P(3662,2,13988,11360,923,924),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(3662,1941,5,14e3,13956,925,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=16,$(3662,1996,5,14032,13956,926,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=20,$(3662,1852,4,14064,13872,927,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=24,$(3662,1978,4,14080,13872,928,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=28,$(3662,1894,4,14096,13872,929,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=32,$(3662,2014,4,14112,13872,930,0|r,0,0),C(21803,21804,21805,0,11288,931,11291,0,11291,0,5898,11293,932),P(21803,2,14128,11360,933,934),r=Jt(4),v[r>>2]=935,$(21803,5812,2,14136,11360,1618,0|r,0,0),r=Jt(4),v[r>>2]=44,n=Jt(4),v[n>>2]=44,I(21803,3554,21674,11360,936,0|r,21674,13272,937,0|n),r=Jt(4),v[r>>2]=48,n=Jt(4),v[n>>2]=48,I(21803,3564,21674,11360,936,0|r,21674,13272,937,0|n),r=Jt(4),v[r>>2]=52,n=Jt(4),v[n>>2]=52,I(21803,3784,21675,11360,938,0|r,21675,13272,939,0|n),r=Jt(4),v[r>>2]=56,n=Jt(4),v[n>>2]=56,I(21803,3778,21675,11360,938,0|r,21675,13272,939,0|n),r=Jt(4),v[r>>2]=60,n=Jt(4),v[n>>2]=60,I(21803,4734,21687,11360,940,0|r,21687,13272,941,0|n),r=Jt(4),v[r>>2]=64,n=Jt(4),v[n>>2]=64,I(21803,2183,21687,11360,940,0|r,21687,13272,941,0|n),C(21806,21807,21808,0,11288,942,11291,0,11291,0,4351,11293,943),r=Jt(4),v[r>>2]=944,$(21806,5812,2,14144,11360,1619,0|r,0,0),r=Jt(4),v[r>>2]=20,n=Jt(4),v[n>>2]=20,I(21806,1391,21687,11360,945,0|r,21687,13272,946,0|n),r=Jt(4),v[r>>2]=24,n=Jt(4),v[n>>2]=24,I(21806,1217,21687,11360,945,0|r,21687,13272,946,0|n),r=Jt(4),v[r>>2]=68,n=Jt(4),v[n>>2]=68,I(21806,1301,21687,11360,945,0|r,21687,13272,946,0|n),r=Jt(4),v[r>>2]=72,n=Jt(4),v[n>>2]=72,I(21806,5081,21789,11360,947,0|r,21789,13272,948,0|n),r=Jt(4),v[r>>2]=76,n=Jt(4),v[n>>2]=76,I(21806,2893,21687,11360,945,0|r,21687,13272,946,0|n),C(21809,21810,21811,0,11288,949,11291,0,11291,0,3684,11293,950),C(21812,21802,21813,0,11288,951,11291,0,11291,0,2991,11293,952),P(21812,4,14160,13872,953,954),r=Jt(8),v[r+4>>2]=0,v[r>>2]=955,$(21812,4370,3,14176,11376,956,0|r,0,0),C(4282,7423,7417,4305,11288,957,11288,958,11288,959,1393,11293,960),P(4282,2,14188,11360,961,962),C(4299,7447,7441,4305,11288,963,11288,964,11288,965,1402,11293,966),P(4299,2,14196,11360,967,968),C(21791,21721,21814,0,11288,969,11291,0,11291,0,6352,11293,970),P(21791,4,14208,13872,971,972),r=Jt(8),v[r+4>>2]=0,v[r>>2]=973,I(21791,1301,21687,11360,974,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=975,I(21791,5773,21664,11360,976,0|r,0,0,0,0),r=Jt(4),v[r>>2]=977,I(21791,6501,21790,11360,978,0|r,0,0,0,0),r=Jt(4),v[r>>2]=979,I(21791,3520,21783,11360,980,0|r,0,0,0,0),r=Jt(4),v[r>>2]=981,I(21791,3544,21783,11360,980,0|r,0,0,0,0),r=Jt(4),v[r>>2]=80,n=Jt(4),v[n>>2]=80,I(21791,6056,21669,11360,982,0|r,21669,13272,983,0|n),C(5833,7906,7900,0,11288,984,11291,0,11291,0,5833,11293,985),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(5833,5116,2,14224,13624,986,0|r,1,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=16,$(5833,4990,2,14232,11360,987,0|r,1,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=16,n=Jt(8),v[n+4>>2]=1,v[n>>2]=20,I(5833,4983,21789,11360,988,0|r,21789,13272,989,0|n),C(1728,7069,7063,5833,11288,990,11288,991,11288,992,1728,11293,993),P(1728,3,14240,11376,994,995),r=Jt(4),v[r>>2]=996,I(1728,6347,6385,11360,997,0|r,0,0,0,0),r=Jt(4),v[r>>2]=998,$(1728,2780,2,14252,11360,1620,0|r,0,0),r=Jt(4),v[r>>2]=40,n=Jt(4),v[n>>2]=40,I(1728,2246,5364,11360,999,0|r,5364,13272,1e3,0|n),r=Jt(4),v[r>>2]=24,n=Jt(4),v[n>>2]=24,I(1728,3998,21687,11360,1001,0|r,21687,13272,1002,0|n),r=Jt(4),v[r>>2]=28,n=Jt(4),v[n>>2]=28,I(1728,2581,21789,11360,1003,0|r,21789,13272,1004,0|n),r=Jt(4),v[r>>2]=29,n=Jt(4),v[n>>2]=29,I(1728,4833,21789,11360,1003,0|r,21789,13272,1004,0|n),r=Jt(4),v[r>>2]=32,n=Jt(4),v[n>>2]=32,I(1728,1231,21686,13097,1005,0|r,21686,13101,1006,0|n),r=Jt(4),v[r>>2]=36,n=Jt(4),v[n>>2]=36,I(1728,2590,21686,13097,1005,0|r,21686,13101,1006,0|n),R(1728,6957,8,14272,14304,1007,1008,0),R(1728,6939,9,14320,14356,1009,1010,0),C(1745,7089,7083,5833,11288,1011,11288,1012,11288,1013,1745,11293,1014),P(1745,3,14368,11376,1015,1016),r=Jt(4),v[r>>2]=1017,I(1745,6347,6402,11360,1018,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1019,$(1745,2780,2,14380,11360,1621,0|r,0,0),r=Jt(4),v[r>>2]=24,n=Jt(4),v[n>>2]=24,I(1745,2246,21800,11360,1020,0|r,21800,13272,1021,0|n),r=Jt(4),v[r>>2]=28,n=Jt(4),v[n>>2]=28,I(1745,3948,21686,13097,1022,0|r,21686,13101,1023,0|n),r=Jt(4),v[r>>2]=32,n=Jt(4),v[n>>2]=32,I(1745,4921,21686,13097,1022,0|r,21686,13101,1023,0|n),r=Jt(4),v[r>>2]=36,n=Jt(4),v[n>>2]=36,I(1745,1269,21686,13097,1022,0|r,21686,13101,1023,0|n),r=Jt(4),v[r>>2]=40,n=Jt(4),v[n>>2]=40,I(1745,1279,21686,13097,1022,0|r,21686,13101,1023,0|n),C(6361,7923,7917,6406,11288,1024,11288,1025,11288,1026,6361,11293,1027),P(6361,2,14388,11360,1028,1029),r=Jt(4),v[r>>2]=1030,$(6361,2780,2,14396,11360,1622,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1031,I(6361,2246,21790,11360,1032,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1033,I(6361,1269,21686,13097,1034,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1035,I(6361,1279,21686,13097,1034,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1036,I(6361,1292,21686,13097,1034,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1037,I(6361,1260,21686,13097,1034,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1038,I(6361,4065,21686,13097,1034,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1039,I(6361,6668,21686,13097,1034,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1040,I(6361,6562,21686,13097,1034,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1041,I(6361,6726,21686,13097,1034,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1042,I(6361,6633,21686,13097,1034,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1043,I(6361,6594,21686,13097,1034,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1044,I(6361,4999,21789,11360,1045,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1046,I(6361,4588,21789,11360,1045,0|r,0,0,0,0),C(1704,7042,7036,5833,11288,1047,11288,1048,11288,1049,1704,11293,1050),P(1704,3,14404,11376,1051,1052),r=Jt(4),v[r>>2]=1053,I(1704,6347,6361,11360,1054,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1055,$(1704,2780,2,14416,11360,1623,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1056,I(1704,2246,5364,11360,1057,0|r,0,0,0,0),r=Jt(4),v[r>>2]=28,n=Jt(4),v[n>>2]=28,I(1704,1269,21686,13097,1058,0|r,21686,13101,1059,0|n),r=Jt(4),v[r>>2]=32,n=Jt(4),v[n>>2]=32,I(1704,1279,21686,13097,1058,0|r,21686,13101,1059,0|n),r=Jt(4),v[r>>2]=36,n=Jt(4),v[n>>2]=36,I(1704,1292,21686,13097,1058,0|r,21686,13101,1059,0|n),r=Jt(4),v[r>>2]=40,n=Jt(4),v[n>>2]=40,I(1704,1260,21686,13097,1058,0|r,21686,13101,1059,0|n),C(5364,7495,7489,5833,11288,1060,11288,1061,11288,1062,5364,11293,1063),P(5364,4,14432,13872,1064,1065),r=Jt(4),v[r>>2]=1066,I(5364,6347,21790,11360,1067,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1068,I(5364,3838,21795,11360,1069,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1070,I(5364,1834,5364,11360,1071,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1072,$(5364,4444,2,14448,11360,1624,0|r,0,0),r=Jt(4),v[r>>2]=32,n=Jt(4),v[n>>2]=32,I(5364,1391,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=36,n=Jt(4),v[n>>2]=36,I(5364,1217,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=40,n=Jt(4),v[n>>2]=40,I(5364,4056,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=44,n=Jt(4),v[n>>2]=44,I(5364,6719,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=48,n=Jt(4),v[n>>2]=48,I(5364,6626,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=52,n=Jt(4),v[n>>2]=52,I(5364,6693,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=56,n=Jt(4),v[n>>2]=56,I(5364,6587,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=60,n=Jt(4),v[n>>2]=60,I(5364,1390,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=64,n=Jt(4),v[n>>2]=64,I(5364,1216,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=68,n=Jt(4),v[n>>2]=68,I(5364,4055,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=72,n=Jt(4),v[n>>2]=72,I(5364,6718,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=76,n=Jt(4),v[n>>2]=76,I(5364,6625,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=80,n=Jt(4),v[n>>2]=80,I(5364,6692,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=84,n=Jt(4),v[n>>2]=84,I(5364,6586,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=88,n=Jt(4),v[n>>2]=88,I(5364,6253,21789,11360,1075,0|r,21789,13272,1076,0|n),r=Jt(4),v[r>>2]=92,n=Jt(4),v[n>>2]=92,I(5364,6560,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=96,n=Jt(4),v[n>>2]=96,I(5364,6345,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=104,n=Jt(4),v[n>>2]=104,I(5364,6343,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=108,n=Jt(4),v[n>>2]=108,I(5364,6341,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=100,n=Jt(4),v[n>>2]=100,I(5364,6754,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(4),v[r>>2]=112,n=Jt(4),v[n>>2]=112,I(5364,6661,21686,13097,1073,0|r,21686,13101,1074,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1077,$(5364,4473,2,14456,13624,1078,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1079,$(5364,4671,9,14464,14500,1080,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1081,$(5364,5130,2,14456,13624,1078,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1082,$(5364,6700,2,14512,13097,1083,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1084,$(5364,6607,2,14512,13097,1083,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1085,$(5364,6739,2,14512,13097,1083,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1086,$(5364,6646,2,14512,13097,1083,0|r,0,0),st(4594,1087),st(6169,1088),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1089,$(5364,4080,3,14532,13252,1090,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1091,$(5364,4101,3,14532,13252,1090,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1092,$(5364,6182,3,14544,13101,1093,0|r,0,0),C(21790,21717,21816,0,11288,1094,11291,0,11291,0,6510,11293,1095),P(21790,4,14560,13872,1096,1097),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1098,I(21790,1301,21687,11360,1099,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1100,I(21790,5773,21664,11360,1101,0|r,0,0,0,0),r=Jt(4),v[r>>2]=20,n=Jt(4),v[n>>2]=20,I(21790,1834,21790,11360,1102,0|r,21790,13272,1103,0|n),r=Jt(4),v[r>>2]=24,n=Jt(4),v[n>>2]=24,I(21790,4696,21686,13097,1104,0|r,21686,13101,1105,0|n),r=Jt(4),v[r>>2]=28,n=Jt(4),v[n>>2]=28,I(21790,1391,21686,13097,1104,0|r,21686,13101,1105,0|n),r=Jt(4),v[r>>2]=32,n=Jt(4),v[n>>2]=32,I(21790,1217,21686,13097,1104,0|r,21686,13101,1105,0|n),r=Jt(4),v[r>>2]=36,n=Jt(4),v[n>>2]=36,I(21790,4056,21686,13097,1104,0|r,21686,13101,1105,0|n),r=Jt(4),v[r>>2]=40,n=Jt(4),v[n>>2]=40,I(21790,6719,21686,13097,1104,0|r,21686,13101,1105,0|n),r=Jt(4),v[r>>2]=44,n=Jt(4),v[n>>2]=44,I(21790,6626,21686,13097,1104,0|r,21686,13101,1105,0|n),r=Jt(4),v[r>>2]=48,n=Jt(4),v[n>>2]=48,I(21790,6693,21686,13097,1104,0|r,21686,13101,1105,0|n),r=Jt(4),v[r>>2]=52,n=Jt(4),v[n>>2]=52,I(21790,6587,21686,13097,1104,0|r,21686,13101,1105,0|n),r=Jt(4),v[r>>2]=56,n=Jt(4),v[n>>2]=56,I(21790,5948,21670,11360,1106,0|r,21670,13272,1107,0|n),r=Jt(4),v[r>>2]=60,n=Jt(4),v[n>>2]=60,I(21790,6279,21789,11360,1108,0|r,21789,13272,1109,0|n),C(21800,21734,21817,0,11288,1110,11291,0,11291,0,1617,11293,1111),P(21800,3,14576,11376,1112,1113),r=Jt(4),v[r>>2]=1114,I(21800,6347,21791,11360,1115,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1116,I(21800,5343,5364,11360,1117,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1118,I(21800,3520,21783,11360,1119,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1120,I(21800,3544,21783,11360,1119,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1121,$(21800,4509,2,14588,11360,1626,0|r,0,0),r=Jt(4),v[r>>2]=1122,$(21800,3860,2,14596,11360,1627,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1123,$(21800,1927,2,14604,11360,1124,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1125,$(21800,1913,3,14612,13272,1126,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1127,$(21800,5701,3,14624,13101,1128,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1129,$(21800,5719,2,14636,13097,1130,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1131,$(21800,5130,2,14644,13624,1132,0|r,0,0),C(21801,21738,21818,0,11288,1133,11291,0,11291,0,4427,11293,1134),P(21801,2,14652,11360,1135,1136),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1137,I(21801,5773,21664,11360,1138,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1139,$(21801,2780,2,14660,11360,1628,0|r,0,0),r=Jt(4),v[r>>2]=1140,$(21801,2454,2,14668,11360,1629,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1141,$(21801,1913,5,14688,14708,1142,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1143,$(21801,4424,3,14716,13272,1144,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1145,$(21801,4386,3,14716,13272,1144,0|r,0,0),r=Jt(4),v[r>>2]=1146,$(21801,1572,3,14728,11376,1630,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1147,$(21801,1927,4,14752,13872,1148,0|r,0,0),r=Jt(4),v[r>>2]=1149,$(21801,2558,2,14768,11360,1631,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1150,$(21801,2036,4,14784,11344,1151,0|r,0,0),r=Jt(4),v[r>>2]=1152,$(21801,1550,3,14800,11376,1632,0|r,0,0),C(21819,21776,21820,0,11288,1153,11291,0,11291,0,1069,11293,1154),P(21819,4,14816,13872,1155,1156),r=Jt(4),v[r>>2]=0,n=Jt(4),v[n>>2]=0,I(21819,1307,21681,11360,1157,0|r,21681,13272,1158,0|n),r=Jt(4),v[r>>2]=1159,$(21819,5812,2,14832,11360,1633,0|r,0,0),r=Jt(4),v[r>>2]=1160,$(21819,1927,2,14840,11360,1634,0|r,0,0),C(21822,21823,21824,0,11288,1161,11291,0,11291,0,4904,11293,1162),P(21822,1,14848,11288,1163,1164),r=Jt(4),v[r>>2]=1165,I(21822,2960,21683,11360,1166,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1167,I(21822,2854,21704,11360,1168,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1169,I(21822,3041,21683,11360,1166,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1170,$(21822,1520,4,14864,13872,1171,0|r,0,0);r=Jt(8),v[r+4>>2]=0,v[r>>2]=1172,$(21822,1589,3,14880,13272,1173,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1174,$(21822,6139,2,14892,13624,1175,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1176,$(21822,4893,2,14900,11360,1177,0|r,0,0),C(21825,21826,21827,0,11288,1178,11291,0,11291,0,6469,11293,1179),P(21825,1,14908,11288,1180,1181),r=Jt(4),v[r>>2]=4,n=Jt(4),v[n>>2]=4,I(21825,5773,21664,11360,1182,0|r,21664,13272,1183,0|n),r=Jt(4),v[r>>2]=1184,$(21825,2780,2,14912,11360,1635,0|r,0,0),r=Jt(4),v[r>>2]=1185,$(21825,2445,2,14920,11360,1636,0|r,0,0),r=Jt(4),v[r>>2]=1186,$(21825,2691,2,14928,11360,1637,0|r,0,0),r=Jt(4),v[r>>2]=64,n=Jt(4),v[n>>2]=64,I(21825,4395,21801,11360,1187,0|r,21801,13272,1188,0|n),r=Jt(4),v[r>>2]=1189,$(21825,2536,2,14936,11360,1638,0|r,0,0),r=Jt(4),v[r>>2]=1190,$(21825,2671,2,14944,11360,1639,0|r,0,0),r=Jt(4),v[r>>2]=1191,$(21825,2493,2,14952,11360,1640,0|r,0,0),r=Jt(4),v[r>>2]=1192,$(21825,2469,2,14960,11360,1641,0|r,0,0),r=Jt(4),v[r>>2]=1193,$(21825,2510,2,14968,11360,1642,0|r,0,0),r=Jt(4),v[r>>2]=148,n=Jt(4),v[n>>2]=148,I(21825,1391,21686,13097,1194,0|r,21686,13101,1195,0|n),r=Jt(4),v[r>>2]=152,n=Jt(4),v[n>>2]=152,I(21825,1217,21686,13097,1194,0|r,21686,13101,1195,0|n),r=Jt(4),v[r>>2]=156,n=Jt(4),v[n>>2]=156,I(21825,4734,21686,13097,1194,0|r,21686,13101,1195,0|n),r=Jt(4),v[r>>2]=160,n=Jt(4),v[n>>2]=160,I(21825,2183,21686,13097,1194,0|r,21686,13101,1195,0|n),r=Jt(4),v[r>>2]=164,n=Jt(4),v[n>>2]=164,I(21825,4319,21664,11360,1182,0|r,21664,13272,1183,0|n),r=Jt(4),v[r>>2]=176,n=Jt(4),v[n>>2]=176,I(21825,4828,21664,11360,1182,0|r,21664,13272,1183,0|n),r=Jt(4),v[r>>2]=204,n=Jt(4),v[n>>2]=204,I(21825,2613,21686,13097,1194,0|r,21686,13101,1195,0|n),r=Jt(4),v[r>>2]=208,n=Jt(4),v[n>>2]=208,I(21825,4754,21664,11360,1182,0|r,21664,13272,1183,0|n),r=Jt(4),v[r>>2]=220,n=Jt(4),v[n>>2]=220,I(21825,4765,21664,11360,1182,0|r,21664,13272,1183,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1196,$(21825,5360,3,14976,11376,1197,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1198,$(21825,1376,3,14988,11376,1199,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1200,$(21825,1605,3,15e3,11376,1201,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1202,$(21825,1317,3,14988,11376,1199,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1203,$(21825,4415,3,15012,11376,1204,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1205,$(21825,1813,3,15024,11376,1206,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1207,$(21825,4239,3,15036,11376,1208,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1209,$(21825,1724,3,15048,11376,1210,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1211,$(21825,1700,3,15060,11376,1212,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1213,$(21825,1741,3,15072,11376,1214,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1215,$(21825,1331,3,14988,11376,1199,0|r,0,0),C(21828,21750,21829,0,11288,1216,11291,0,11291,0,4256,11293,1217),P(21828,4,15088,15104,1218,1219),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1220,I(21828,5773,21664,11360,1221,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1222,$(21828,2789,2,15116,11360,1643,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1223,$(21828,5483,3,15124,11376,1224,0|r,0,0),r=Jt(4),v[r>>2]=32,n=Jt(4),v[n>>2]=32,I(21828,4143,21686,13097,1225,0|r,21686,13101,1226,0|n),C(5672,7890,7884,0,11288,1227,11291,0,11291,0,5672,11293,1228),r=Jt(8),v[r+4>>2]=1,v[r>>2]=16,$(5672,6329,2,15136,11360,1229,0|r,1,0),C(5620,7801,7795,5672,11288,1230,11288,1231,11288,1232,5620,11293,1233),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1234,$(5620,1648,2,15144,11360,1235,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1236,$(5620,3732,3,15152,13272,1237,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1238,$(5620,6300,3,15152,13272,1237,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1239,$(5620,4952,7,15168,15196,1240,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1241,$(5620,2109,4,15216,15232,1242,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1243,$(5620,5270,3,15240,11436,1244,0|r,0,0),C(5649,7844,7838,5620,11288,1245,11288,1246,11288,1247,5649,11293,1248),P(5649,2,15252,11360,1249,1250),S(5649,6761,21687,11068,11288,1251,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1252,$(5649,5764,6,15264,15288,1253,0|r,0,0),C(5667,7869,7863,5649,11288,1254,11288,1255,11288,1256,5667,11293,1257),P(5667,2,15296,11360,1258,1259),C(5530,7720,7714,5649,11288,1260,11288,1261,11288,1262,5530,11293,1263),P(5530,2,15304,11360,1264,1265),C(5634,7822,7816,5620,11288,1266,11288,1267,11288,1268,5634,11293,1269),P(5634,2,15312,11360,1270,1271),r=Jt(4),v[r>>2]=20,n=Jt(4),v[n>>2]=20,I(5634,1366,21687,11360,1272,0|r,21687,13272,1273,0|n),r=Jt(4),v[r>>2]=1274,$(5634,2802,2,15320,11360,1644,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1275,$(5634,5764,5,15328,13716,1276,0|r,0,0),C(5498,7674,7668,5620,11288,1277,11288,1278,11288,1279,5498,11293,1280),P(5498,2,15348,11360,1281,1282),S(5498,6761,21687,9364,11288,1251,0,0),r=Jt(4),v[r>>2]=20,n=Jt(4),v[n>>2]=20,I(5498,1307,21687,11360,1283,0|r,21687,13272,1284,0|n),r=Jt(4),v[r>>2]=1285,$(5498,2802,2,15356,11360,1645,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1286,$(5498,5764,8,15376,15408,1287,0|r,0,0),C(5495,7650,7644,5620,11288,1288,11288,1289,11288,1290,5495,11293,1291),P(5495,2,15420,11360,1292,1293),S(5495,6761,21687,9364,11288,1251,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1294,n=Jt(8),v[n+4>>2]=0,v[n>>2]=1295,I(5495,1307,21687,11360,1296,0|r,21687,13272,1297,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1298,$(5495,5764,11,15440,15484,1299,0|r,0,0),C(5464,7624,7618,5672,11288,1300,11288,1301,11288,1302,5464,11293,1303),P(5464,2,15500,11360,1304,1305),r=Jt(4),v[r>>2]=4,n=Jt(4),v[n>>2]=4,I(5464,1307,21681,11360,1306,0|r,21681,13272,1307,0|n),r=Jt(4),v[r>>2]=1308,$(5464,2802,2,15508,11360,1646,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1309,$(5464,2812,2,15516,11360,1310,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1311,$(5464,1648,2,15524,11360,1312,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1313,$(5464,5764,5,15536,15556,1314,0|r,0,0),C(5575,7779,7773,5620,11288,1315,11288,1316,11288,1317,5575,11293,1318),P(5575,2,15564,11360,1319,1320),r=Jt(4),v[r>>2]=20,n=Jt(4),v[n>>2]=20,I(5575,1307,21687,11360,1321,0|r,21687,13272,1322,0|n),r=Jt(4),v[r>>2]=56,n=Jt(4),v[n>>2]=56,I(5575,1841,1877,11360,1323,0|r,1877,13272,1324,0|n),r=Jt(4),v[r>>2]=1325,$(5575,2802,2,15572,11360,1647,0|r,0,0),r=Jt(4),v[r>>2]=1326,$(5575,2922,2,15580,11360,1648,0|r,0,0),r=Jt(4),v[r>>2]=1327,$(5575,5764,5,15600,15556,1649,0|r,0,0),C(5450,7603,7597,5672,11288,1328,11288,1329,11288,1330,5450,11293,1331),P(5450,2,15620,11360,1332,1333),r=Jt(4),v[r>>2]=1334,$(5450,2802,2,15628,11360,1650,0|r,0,0),r=Jt(4),v[r>>2]=1335,$(5450,2536,2,15636,11360,1651,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1336,$(5450,1648,2,15644,11360,1337,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1338,$(5450,5764,4,15664,11344,1339,0|r,0,0),C(5512,7695,7689,5672,11288,1340,11288,1341,11288,1342,5512,11293,1343),P(5512,2,15680,11360,1344,1345),r=Jt(4),v[r>>2]=1346,$(5512,2802,2,15688,11360,1652,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1347,$(5512,1648,2,15696,11360,1348,0|r,0,0),r=Jt(4),v[r>>2]=1349,$(5512,2599,2,15704,11360,1653,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1350,$(5512,5764,5,15712,15556,1351,0|r,0,0),C(5429,7575,7569,5620,11288,1352,11288,1353,11288,1354,5429,11293,1355),P(5429,2,15732,11360,1356,1357),S(5429,6761,21687,9716,11288,1251,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1358,$(5429,5764,9,15744,15780,1359,0|r,0,0),C(5401,7540,7534,5620,11288,1360,11288,1361,11288,1362,5401,11293,1363),P(5401,2,15792,11360,1364,1365),S(5401,6761,21687,11036,11288,1251,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1366,$(5401,5764,8,15808,15408,1367,0|r,0,0),C(5544,7741,7735,5620,11288,1368,11288,1369,11288,1370,5544,11293,1371),P(5544,2,15840,11360,1372,1373),S(5544,6761,21687,11036,11288,1251,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1374,$(5544,5764,5,15856,13716,1375,0|r,0,0),C(5375,7507,7501,5620,11288,1376,11288,1377,11288,1378,5375,11293,1379),P(5375,2,15876,11360,1380,1381),S(5375,6761,21687,9972,11288,1251,0,0),C(21831,21769,21832,0,11288,1382,11291,0,11291,0,1089,11293,1383),P(21831,1,15884,11288,1384,1385),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1386,I(21831,4164,21828,11360,1387,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1388,I(21831,1397,21831,11360,1389,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1390,I(21831,4519,21831,11360,1389,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1391,I(21831,3829,21831,11360,1389,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1392,I(21831,1355,21687,11360,1393,0|r,0,0,0,0),r=Jt(4),v[r>>2]=36,n=Jt(4),v[n>>2]=36,I(21831,3767,21789,11360,1394,0|r,21789,13272,1395,0|n),r=Jt(4),v[r>>2]=37,n=Jt(4),v[n>>2]=37,I(21831,2419,21789,11360,1394,0|r,21789,13272,1395,0|n),r=Jt(4),v[r>>2]=40,n=Jt(4),v[n>>2]=40,I(21831,6194,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=44,n=Jt(4),v[n>>2]=44,I(21831,6209,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=48,n=Jt(4),v[n>>2]=48,I(21831,6229,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=52,n=Jt(4),v[n>>2]=52,I(21831,1530,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=56,n=Jt(4),v[n>>2]=56,I(21831,6147,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1398,n=Jt(8),v[n+4>>2]=0,v[n>>2]=1399,I(21831,1469,21686,13097,1400,0|r,21686,13101,1401,0|n),r=Jt(4),v[r>>2]=68,n=Jt(4),v[n>>2]=68,I(21831,1213,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=72,n=Jt(4),v[n>>2]=72,I(21831,5754,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=84,n=Jt(4),v[n>>2]=84,I(21831,6160,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=88,n=Jt(4),v[n>>2]=88,I(21831,5868,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=92,n=Jt(4),v[n>>2]=92,I(21831,6519,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=96,n=Jt(4),v[n>>2]=96,I(21831,5693,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=100,n=Jt(4),v[n>>2]=100,I(21831,4152,21686,13097,1396,0|r,21686,13101,1397,0|n),r=Jt(4),v[r>>2]=112,n=Jt(4),v[n>>2]=112,I(21831,6114,21667,11360,1402,0|r,21667,13272,1403,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1404,$(21831,5737,2,15888,13097,1405,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1406,$(21831,5070,2,15896,11360,1407,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1408,$(21831,2617,2,15904,13624,1409,0|r,0,0),C(21833,21834,21835,0,11288,1410,11291,0,11291,0,6482,11293,1411),P(21833,2,15912,11360,1412,1413),r=Jt(4),v[r>>2]=8,n=Jt(4),v[n>>2]=8,I(21833,1235,21686,13097,1414,0|r,21686,13101,1415,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1416,I(21833,6456,21825,11360,1417,0|r,0,0,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1418,$(21833,1246,5,15920,15940,1419,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1420,$(21833,4626,5,15952,15940,1421,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1422,$(21833,1253,4,15984,16e3,1423,0|r,0,0),C(21836,21837,21838,0,11288,1424,11291,0,11291,0,5091,11293,1425),P(21836,2,16008,11360,1426,1427),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1428,I(21836,6347,21833,11360,1429,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1430,$(21836,2700,2,16016,11360,1654,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1431,n=Jt(8),v[n+4>>2]=0,v[n>>2]=1432,I(21836,5868,21686,13097,1433,0|r,21686,13101,1434,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1435,$(21836,5116,3,16024,13101,1436,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1437,$(21836,1198,3,16036,11376,1438,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1439,$(21836,2710,2,16048,13624,1440,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1441,$(21836,4615,3,16056,13272,1442,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1443,$(21836,4210,5,16080,13956,1444,0|r,0,0),r=Jt(4),v[r>>2]=1445,$(21836,4637,5,16112,13956,1655,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1446,$(21836,4253,6,16144,16168,1447,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1448,$(21836,4654,6,16176,16168,1449,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1450,$(21836,4174,4,16208,15104,1451,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1452,$(21836,4192,5,16224,16244,1453,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1454,$(21836,2652,3,16024,13101,1436,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1455,$(21836,1823,3,16252,11376,1456,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1457,$(21836,5045,2,16048,13624,1440,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1458,$(21836,5058,2,16048,13624,1440,0|r,0,0),C(21795,21839,21840,0,11288,1459,11291,0,11291,0,3907,11293,1460),P(21795,2,16264,11360,1461,1462),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1463,I(21795,6347,21825,11360,1464,0|r,0,0,0,0),r=Jt(4),v[r>>2]=1465,$(21795,2780,2,16272,11360,1656,0|r,0,0),at(2445,1466),at(3649,1467),r=Jt(4),v[r>>2]=1468,$(21795,2493,2,16288,11360,1658,0|r,0,0),r=Jt(4),v[r>>2]=1469,$(21795,2469,2,16296,11360,1659,0|r,0,0),r=Jt(4),v[r>>2]=1470,$(21795,2510,2,16304,11360,1660,0|r,0,0),r=Jt(4),v[r>>2]=1471,$(21795,1409,2,16312,11360,1661,0|r,0,0),r=Jt(4),v[r>>2]=136,n=Jt(4),v[n>>2]=136,I(21795,4381,21801,11360,1472,0|r,21801,13272,1473,0|n),r=Jt(4),v[r>>2]=1474,I(21795,3520,21783,11360,1475,0|r,0,0,0,0),r=Jt(4),v[r>>2]=160,n=Jt(4),v[n>>2]=160,I(21795,5688,21686,13097,1476,0|r,21686,13101,1477,0|n),r=Jt(4),v[r>>2]=164,n=Jt(4),v[n>>2]=164,I(21795,6719,21686,13097,1476,0|r,21686,13101,1477,0|n),r=Jt(4),v[r>>2]=168,n=Jt(4),v[n>>2]=168,I(21795,6626,21686,13097,1476,0|r,21686,13101,1477,0|n),r=Jt(4),v[r>>2]=172,n=Jt(4),v[n>>2]=172,I(21795,1391,21686,13097,1476,0|r,21686,13101,1477,0|n),r=Jt(4),v[r>>2]=176,n=Jt(4),v[n>>2]=176,I(21795,1217,21686,13097,1476,0|r,21686,13101,1477,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1478,$(21795,5886,2,16320,13624,1479,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1480,$(21795,4473,2,16320,13624,1479,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1481,$(21795,5130,2,16320,13624,1479,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1482,$(21795,5165,2,16320,13624,1479,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1483,$(21795,5145,2,16320,13624,1479,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1484,$(21795,5348,2,16328,11360,1485,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1486,$(21795,5360,3,16336,11376,1487,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1488,$(21795,1376,3,16348,11376,1489,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1490,$(21795,1605,3,16360,11376,1491,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1492,$(21795,1317,3,16348,11376,1489,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1493,$(21795,5798,3,16372,13272,1494,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1495,$(21795,4407,3,16384,13272,1496,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1497,$(21795,5778,4,16400,13872,1498,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1499,$(21795,1927,4,16416,13872,1500,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1501,$(21795,1913,4,16432,11344,1502,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1503,$(21795,1724,3,16448,11376,1504,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1505,$(21795,1700,3,16460,11376,1506,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1507,$(21795,1741,3,16472,11376,1508,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1509,$(21795,5116,3,16484,13101,1510,0|r,0,0),C(2295,7350,7344,0,11288,1511,11291,0,11291,0,2295,11293,1512),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(2295,4432,3,16496,13272,1513,0|r,1,0),r=Jt(4),v[r>>2]=1514,$(2295,4463,4,16512,16528,1662,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=20,$(2295,6135,2,16536,13624,1515,0|r,1,0),C(2271,7299,7293,2295,11288,1516,11288,1517,11288,1518,2311,11293,1519),P(2271,3,16544,13092,1520,1521),r=Jt(4),v[r>>2]=4,n=Jt(4),v[n>>2]=4,I(2271,6676,21686,13097,1522,0|r,21686,13101,1523,0|n),r=Jt(4),v[r>>2]=8,n=Jt(4),v[n>>2]=8,I(2271,6570,21686,13097,1522,0|r,21686,13101,1523,0|n),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(2271,4432,3,16556,13272,1524,0|r,0,0),r=Jt(4),v[r>>2]=1525,$(2271,4463,4,16512,16528,1662,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=20,$(2271,6135,2,16568,13624,1526,0|r,0,0),C(2290,7325,7319,2295,11288,1527,11288,1528,11288,1529,2339,11293,1530),P(2290,3,16576,13468,1531,1532),r=Jt(8),v[r+4>>2]=1,v[r>>2]=12,$(2290,4432,3,16588,13272,1533,0|r,0,0),r=Jt(4),v[r>>2]=1534,$(2290,4463,4,16512,16528,1662,0|r,0,0),r=Jt(8),v[r+4>>2]=1,v[r>>2]=20,$(2290,6135,2,16600,13624,1535,0|r,0,0),r=Jt(4),v[r>>2]=4,n=Jt(4),v[n>>2]=4,I(2290,6684,21686,13097,1536,0|r,21686,13101,1537,0|n),r=Jt(4),v[r>>2]=8,n=Jt(4),v[n>>2]=8,I(2290,6578,21686,13097,1536,0|r,21686,13101,1537,0|n),r=Jt(4),v[r>>2]=12,n=Jt(4),v[n>>2]=12,I(2290,2432,21686,13097,1536,0|r,21686,13101,1537,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1538,n=Jt(8),v[n+4>>2]=0,v[n>>2]=1539,I(2290,5820,21686,13097,1540,0|r,21686,13101,1541,0|n),r=Jt(4),v[r>>2]=20,n=Jt(4),v[n>>2]=20,I(2290,6754,21686,13097,1536,0|r,21686,13101,1537,0|n),r=Jt(4),v[r>>2]=24,n=Jt(4),v[n>>2]=24,I(2290,6661,21686,13097,1536,0|r,21686,13101,1537,0|n),C(21841,21842,21843,0,11288,1542,11291,0,11291,0,4554,11293,1543),r=Jt(4),v[r>>2]=32,n=Jt(4),v[n>>2]=32,I(21841,1634,21697,11360,1544,0|r,21697,13272,1545,0|n),r=Jt(4),v[r>>2]=36,n=Jt(4),v[n>>2]=36,I(21841,1641,21697,11360,1544,0|r,21697,13272,1545,0|n),r=Jt(4),v[r>>2]=40,n=Jt(4),v[n>>2]=40,I(21841,3122,21697,11360,1544,0|r,21697,13272,1545,0|n),r=Jt(4),v[r>>2]=44,n=Jt(4),v[n>>2]=44,I(21841,3278,21697,11360,1544,0|r,21697,13272,1545,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1546,$(21841,2747,2,16608,11360,1547,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1548,$(21841,6431,2,16616,11360,1549,0|r,0,0),C(21682,21844,21845,0,11288,1550,11291,0,11291,0,5322,11293,1551),r=Jt(4),v[r>>2]=0,n=Jt(4),v[n>>2]=0,I(21682,5206,21697,11360,1552,0|r,21697,13272,1553,0|n),r=Jt(4),v[r>>2]=4,n=Jt(4),v[n>>2]=4,I(21682,2207,21697,11360,1552,0|r,21697,13272,1553,0|n);r=Jt(4),v[r>>2]=8,n=Jt(4),v[n>>2]=8,I(21682,1634,21697,11360,1552,0|r,21697,13272,1553,0|n),r=Jt(4),v[r>>2]=12,n=Jt(4),v[n>>2]=12,I(21682,2225,21697,11360,1552,0|r,21697,13272,1553,0|n),r=Jt(4),v[r>>2]=16,n=Jt(4),v[n>>2]=16,I(21682,1641,21697,11360,1552,0|r,21697,13272,1553,0|n),C(21846,21847,21848,0,11288,1554,11291,0,11291,0,6083,11293,1555),P(21846,1,16624,11288,1556,1557),r=Jt(4),v[r>>2]=0,n=Jt(4),v[n>>2]=0,I(21846,5878,21789,11360,1558,0|r,21789,13272,1559,0|n),r=Jt(4),v[r>>2]=4,n=Jt(4),v[n>>2]=4,I(21846,5123,21686,13097,1560,0|r,21686,13101,1561,0|n),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1562,$(21846,3847,3,16628,11376,1563,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1564,$(21846,4210,5,16640,16660,1565,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1566,$(21846,4407,3,16668,13272,1567,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1568,$(21846,4223,3,16680,13101,1569,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1570,$(21846,6439,2,16692,11360,1571,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1572,$(21846,6540,3,16700,13272,1573,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1574,$(21846,1773,3,16700,13272,1573,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1575,$(21846,3526,6,16720,16744,1576,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1577,$(21846,2308,3,16752,13272,1578,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1579,$(21846,2336,3,16764,13272,1580,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1581,$(21846,2324,2,16776,13624,1582,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1583,$(21846,5088,2,16784,11360,1584,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1585,$(21846,1246,5,16800,15940,1586,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1587,$(21846,3617,3,16820,13272,1588,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1589,$(21846,3595,4,16832,11344,1590,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1591,$(21846,5983,3,16700,13272,1573,0|r,0,0),r=Jt(4),v[r>>2]=1592,$(21846,2759,2,16848,11360,1663,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1593,$(21846,4334,6,16864,13832,1594,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1595,$(21846,1173,2,16776,13624,1582,0|r,0,0),r=Jt(8),v[r+4>>2]=0,v[r>>2]=1596,$(21846,5191,4,16896,11344,1597,0|r,0,0)}function X(r){var n,f=0,i=0,e=0,t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=0,h=0;V=n=V-16|0;r:{n:{f:{i:{e:{t:{u:{o:{a:{c:{b:{k:{s:{v:{if((r|=0)>>>0<=244){if(3&(f=(o=v[5489])>>>(i=(a=r>>>0<11?16:r+11&-8)>>>3|0)|0)){f=21996+(r=(i=i+(1&~f)|0)<<3)|0,e=v[r+22004>>2],(0|f)!=(0|(r=v[e+8>>2]))?(v[r+12>>2]=f,v[f+8>>2]=r):(s=21956,h=dt(i)&o,v[s>>2]=h),r=e+8|0,f=i<<3,v[e+4>>2]=3|f,v[4+(f=f+e|0)>>2]=1|v[f+4>>2];break r}if((k=v[5491])>>>0>=a>>>0)break v;if(f){f=21996+(r=(e=lu(0-(r=(0-(r=2<<i)|r)&f<<i)&r))<<3)|0,t=v[r+22004>>2],(0|f)!=(0|(r=v[t+8>>2]))?(v[r+12>>2]=f,v[f+8>>2]=r):(o=dt(e)&o,v[5489]=o),v[t+4>>2]=3|a,e=(r=e<<3)-a|0,v[4+(i=t+a|0)>>2]=1|e,v[r+t>>2]=e,k&&(f=21996+(-8&k)|0,u=v[5494],(r=1<<(k>>>3))&o?r=v[f+8>>2]:(v[5489]=r|o,r=f),v[f+8>>2]=u,v[r+12>>2]=u,v[u+12>>2]=f,v[u+8>>2]=r),r=t+8|0,v[5494]=i,v[5491]=e;break r}if(!(b=v[5490]))break v;for(i=v[22260+(lu(0-b&b)<<2)>>2],u=(-8&v[i+4>>2])-a|0,f=i;(r=v[f+16>>2])||(r=v[f+20>>2]);)u=(e=(f=(-8&v[r+4>>2])-a|0)>>>0<u>>>0)?f:u,i=e?r:i,f=r;if(c=v[i+24>>2],(0|(e=v[i+12>>2]))!=(0|i)){r=v[i+8>>2],v[r+12>>2]=e,v[e+8>>2]=r;break n}if(!(r=v[(f=i+20|0)>>2])){if(!(r=v[i+16>>2]))break s;f=i+16|0}for(;t=f,e=r,(r=v[(f=r+20|0)>>2])||(f=e+16|0,r=v[e+16>>2]););v[t>>2]=0;break n}if(a=-1,!(r>>>0>4294967231)&&(a=-8&(r=r+11|0),b=v[5490])){u=0-a|0,o=0,a>>>0<256||(o=31,a>>>0>16777215||(o=62+((a>>>38-(r=F(r>>>8|0))&1)-(r<<1)|0)|0));l:{h:{if(f=v[22260+(o<<2)>>2])for(r=0,i=a<<(31!=(0|o)?25-(o>>>1|0):0);;){if(!((t=(-8&v[f+4>>2])-a|0)>>>0>=u>>>0||(e=f,u=t,t))){u=0,r=f;break h}if(t=v[f+20>>2],f=v[16+((i>>>29&4)+f|0)>>2],r=t?(0|t)==(0|f)?r:t:r,i<<=1,!f)break}else r=0;if(!(r|e)){if(e=0,!(r=(0-(r=2<<o)|r)&b))break v;r=v[22260+(lu(r&0-r)<<2)>>2]}if(!r)break l}for(;u=(i=(f=(-8&v[r+4>>2])-a|0)>>>0<u>>>0)?f:u,e=i?r:e,r=(f=v[r+16>>2])||v[r+20>>2];);}if(!(!e|v[5491]-a>>>0<=u>>>0)){if(o=v[e+24>>2],(0|e)!=(0|(i=v[e+12>>2]))){r=v[e+8>>2],v[r+12>>2]=i,v[i+8>>2]=r;break f}if(!(r=v[(f=e+20|0)>>2])){if(!(r=v[e+16>>2]))break k;f=e+16|0}for(;t=f,i=r,(r=v[(f=r+20|0)>>2])||(f=i+16|0,r=v[i+16>>2]););v[t>>2]=0;break f}}}if((r=v[5491])>>>0>=a>>>0){e=v[5494],(f=r-a|0)>>>0>=16?(v[4+(i=e+a|0)>>2]=1|f,v[r+e>>2]=f,v[e+4>>2]=3|a):(v[e+4>>2]=3|r,v[4+(r=r+e|0)>>2]=1|v[r+4>>2],i=0,f=0),v[5491]=f,v[5494]=i,r=e+8|0;break r}if((c=v[5492])>>>0>a>>>0){f=c-a|0,v[5492]=f,r=(i=v[5495])+a|0,v[5495]=r,v[r+4>>2]=1|f,v[i+4>>2]=3|a,r=i+8|0;break r}if(r=0,b=a+47|0,v[5607]?i=v[5609]:(v[5610]=-1,v[5611]=-1,v[5608]=4096,v[5609]=4096,v[5607]=n+12&-16^1431655768,v[5612]=0,v[5600]=0,i=4096),(f=(t=b+i|0)&(u=0-i|0))>>>0<=a>>>0)break r;if((e=v[5599])&&e>>>0<(o=(i=v[5597])+f|0)>>>0|i>>>0>=o>>>0)break r;v:{if(!(4&l[22400])){l:{h:{d:{p:{if(e=v[5495])for(r=22404;;){if((i=v[r>>2])>>>0<=e>>>0&e>>>0<i+v[r+4>>2]>>>0)break p;if(!(r=v[r+8>>2]))break}if(-1==(0|(i=Pi(0))))break l;if(o=f,(r=(e=v[5608])-1|0)&i&&(o=(f-i|0)+(r+i&0-e)|0),o>>>0<=a>>>0)break l;if((e=v[5599])&&e>>>0<(u=(r=v[5597])+o|0)>>>0|r>>>0>=u>>>0)break l;if((0|i)!=(0|(r=Pi(o))))break d;break v}if((0|(i=Pi(o=u&t-c)))==(v[r>>2]+v[r+4>>2]|0))break h;r=i}if(-1==(0|r))break l;if(a+48>>>0<=o>>>0){i=r;break v}if(-1==(0|Pi(i=(i=v[5609])+(b-o|0)&0-i)))break l;o=i+o|0,i=r;break v}if(-1!=(0|i))break v}v[5600]=4|v[5600]}if(-1==(0|(i=Pi(f)))|-1==(0|(r=Pi(0)))|r>>>0<=i>>>0)break a;if((o=r-i|0)>>>0<=a+40>>>0)break a}r=v[5597]+o|0,v[5597]=r,r>>>0>d[5598]&&(v[5598]=r);v:{if(t=v[5495]){for(r=22404;;){if(((e=v[r>>2])+(f=v[r+4>>2])|0)==(0|i))break v;if(!(r=v[r+8>>2]))break}break b}for((r=v[5493])>>>0<=i>>>0&&r||(v[5493]=i),r=0,v[5602]=o,v[5601]=i,v[5497]=-1,v[5498]=v[5607],v[5604]=0;f=21996+(e=r<<3)|0,v[e+22004>>2]=f,v[e+22008>>2]=f,32!=(0|(r=r+1|0)););f=(e=o-40|0)-(r=i+8&7?-8-i&7:0)|0,v[5492]=f,r=r+i|0,v[5495]=r,v[r+4>>2]=1|f,v[4+(i+e|0)>>2]=40,v[5496]=v[5611];break c}if(8&v[r+12>>2]|i>>>0<=t>>>0|e>>>0>t>>>0)break b;v[r+4>>2]=f+o,i=(r=t+8&7?-8-t&7:0)+t|0,v[5495]=i,r=(f=v[5492]+o|0)-r|0,v[5492]=r,v[i+4>>2]=1|r,v[4+(f+t|0)>>2]=40,v[5496]=v[5611];break c}e=0;break n}i=0;break f}d[5493]>i>>>0&&(v[5493]=i),f=i+o|0,r=22404;b:{k:{s:{for(;;){if((0|f)!=v[r>>2]){if(r=v[r+8>>2])continue;break s}break}if(!(8&l[r+12|0]))break k}for(r=22404;;){if((f=v[r>>2])>>>0<=t>>>0&&(u=f+v[r+4>>2]|0)>>>0>t>>>0)break b;r=v[r+8>>2]}}if(v[r>>2]=i,v[r+4>>2]=v[r+4>>2]+o,v[4+(b=(i+8&7?-8-i&7:0)+i|0)>>2]=3|a,r=(o=f+(f+8&7?-8-f&7:0)|0)-(c=a+b|0)|0,(0|t)==(0|o)){v[5495]=c,r=v[5492]+r|0,v[5492]=r,v[c+4>>2]=1|r;break i}if(v[5494]==(0|o)){v[5494]=c,r=v[5491]+r|0,v[5491]=r,v[c+4>>2]=1|r,v[r+c>>2]=r;break i}if(1!=(3&(u=v[o+4>>2])))break e;if(t=-8&u,u>>>0<=255){if((0|(i=v[o+12>>2]))==(0|(f=v[o+8>>2]))){s=21956,h=v[5489]&dt(u>>>3|0),v[s>>2]=h;break t}v[f+12>>2]=i,v[i+8>>2]=f;break t}if(a=v[o+24>>2],(0|o)!=(0|(i=v[o+12>>2]))){f=v[o+8>>2],v[f+12>>2]=i,v[i+8>>2]=f;break u}if(!(u=v[(f=o+20|0)>>2])){if(!(u=v[o+16>>2]))break o;f=o+16|0}for(;e=f,(u=v[(f=(i=u)+20|0)>>2])||(f=i+16|0,u=v[i+16>>2]););v[e>>2]=0;break u}for(f=(e=o-40|0)-(r=i+8&7?-8-i&7:0)|0,v[5492]=f,r=r+i|0,v[5495]=r,v[r+4>>2]=1|f,v[4+(i+e|0)>>2]=40,v[5496]=v[5611],v[(e=(r=(u+(u-39&7?39-u&7:0)|0)-47|0)>>>0<t+16>>>0?t:r)+4>>2]=27,r=v[5604],v[e+16>>2]=v[5603],v[e+20>>2]=r,r=v[5602],v[e+8>>2]=v[5601],v[e+12>>2]=r,v[5603]=e+8,v[5602]=o,v[5601]=i,v[5604]=0,r=e+24|0;v[r+4>>2]=7,f=r+8|0,r=r+4|0,f>>>0<u>>>0;);if((0|e)!=(0|t))if(v[e+4>>2]=-2&v[e+4>>2],u=e-t|0,v[t+4>>2]=1|u,v[e>>2]=u,u>>>0<=255)f=21996+(-8&u)|0,(i=v[5489])&(r=1<<(u>>>3))?r=v[f+8>>2]:(v[5489]=r|i,r=f),v[f+8>>2]=t,v[r+12>>2]=t,v[t+12>>2]=f,v[t+8>>2]=r;else{r=31,u>>>0<=16777215&&(r=62+((u>>>38-(r=F(u>>>8|0))&1)-(r<<1)|0)|0),v[t+28>>2]=r,v[t+16>>2]=0,v[t+20>>2]=0,f=22260+(r<<2)|0;b:{if((e=v[5490])&(i=1<<r)){for(r=u<<(31!=(0|r)?25-(r>>>1|0):0),e=v[f>>2];;){if((0|u)==(-8&v[(f=e)+4>>2]))break b;if(i=r>>>29|0,r<<=1,!(e=v[16+(i=(4&i)+f|0)>>2]))break}v[i+16>>2]=t}else v[5490]=i|e,v[f>>2]=t;v[t+24>>2]=f,v[t+12>>2]=t,v[t+8>>2]=t;break c}r=v[f+8>>2],v[r+12>>2]=t,v[f+8>>2]=t,v[t+24>>2]=0,v[t+12>>2]=f,v[t+8>>2]=r}}if(!((r=v[5492])>>>0<=a>>>0)){f=r-a|0,v[5492]=f,r=(i=v[5495])+a|0,v[5495]=r,v[r+4>>2]=1|f,v[i+4>>2]=3|a,r=i+8|0;break r}}v[5488]=48,r=0;break r}i=0}if(a){e=v[o+28>>2];u:{if(v[(f=22260+(e<<2)|0)>>2]==(0|o)){if(v[f>>2]=i,i)break u;s=21960,h=v[5490]&dt(e),v[s>>2]=h;break t}if(v[a+(v[a+16>>2]==(0|o)?16:20)>>2]=i,!i)break t}v[i+24>>2]=a,(f=v[o+16>>2])&&(v[i+16>>2]=f,v[f+24>>2]=i),(f=v[o+20>>2])&&(v[i+20>>2]=f,v[f+24>>2]=i)}}r=r+t|0,u=v[4+(o=t+o|0)>>2]}if(v[o+4>>2]=-2&u,v[c+4>>2]=1|r,v[r+c>>2]=r,r>>>0<=255)f=21996+(-8&r)|0,(i=v[5489])&(r=1<<(r>>>3))?r=v[f+8>>2]:(v[5489]=r|i,r=f),v[f+8>>2]=c,v[r+12>>2]=c,v[c+12>>2]=f,v[c+8>>2]=r;else{u=31,r>>>0<=16777215&&(u=62+((r>>>38-(f=F(r>>>8|0))&1)-(f<<1)|0)|0),v[c+28>>2]=u,v[c+16>>2]=0,v[c+20>>2]=0,f=22260+(u<<2)|0;e:{if((e=v[5490])&(i=1<<u)){for(u=r<<(31!=(0|u)?25-(u>>>1|0):0),i=v[f>>2];;){if(f=i,(-8&v[i+4>>2])==(0|r))break e;if(i=u>>>29|0,u<<=1,!(i=v[16+(e=(4&i)+f|0)>>2]))break}v[e+16>>2]=c}else v[5490]=i|e,v[f>>2]=c;v[c+24>>2]=f,v[c+12>>2]=c,v[c+8>>2]=c;break i}r=v[f+8>>2],v[r+12>>2]=c,v[f+8>>2]=c,v[c+24>>2]=0,v[c+12>>2]=f,v[c+8>>2]=r}}r=b+8|0;break r}f:if(o){f=v[e+28>>2];i:{if(v[(r=22260+(f<<2)|0)>>2]==(0|e)){if(v[r>>2]=i,i)break i;b=dt(f)&b,v[5490]=b;break f}if(v[o+(v[o+16>>2]==(0|e)?16:20)>>2]=i,!i)break f}v[i+24>>2]=o,(r=v[e+16>>2])&&(v[i+16>>2]=r,v[r+24>>2]=i),(r=v[e+20>>2])&&(v[i+20>>2]=r,v[r+24>>2]=i)}f:if(u>>>0<=15)r=u+a|0,v[e+4>>2]=3|r,v[4+(r=r+e|0)>>2]=1|v[r+4>>2];else if(v[e+4>>2]=3|a,v[4+(t=e+a|0)>>2]=1|u,v[t+u>>2]=u,u>>>0<=255)f=21996+(-8&u)|0,(i=v[5489])&(r=1<<(u>>>3))?r=v[f+8>>2]:(v[5489]=r|i,r=f),v[f+8>>2]=t,v[r+12>>2]=t,v[t+12>>2]=f,v[t+8>>2]=r;else{r=31,u>>>0<=16777215&&(r=62+((u>>>38-(r=F(u>>>8|0))&1)-(r<<1)|0)|0),v[t+28>>2]=r,v[t+16>>2]=0,v[t+20>>2]=0,f=22260+(r<<2)|0;i:{if((i=1<<r)&b){for(r=u<<(31!=(0|r)?25-(r>>>1|0):0),a=v[f>>2];;){if((-8&v[(f=a)+4>>2])==(0|u))break i;if(i=r>>>29|0,r<<=1,!(a=v[16+(i=(4&i)+f|0)>>2]))break}v[i+16>>2]=t}else v[5490]=i|b,v[f>>2]=t;v[t+24>>2]=f,v[t+12>>2]=t,v[t+8>>2]=t;break f}r=v[f+8>>2],v[r+12>>2]=t,v[f+8>>2]=t,v[t+24>>2]=0,v[t+12>>2]=f,v[t+8>>2]=r}r=e+8|0;break r}n:if(c){f=v[i+28>>2];f:{if(v[(r=22260+(f<<2)|0)>>2]==(0|i)){if(v[r>>2]=e,e)break f;s=21960,h=dt(f)&b,v[s>>2]=h;break n}if(v[c+(v[c+16>>2]==(0|i)?16:20)>>2]=e,!e)break n}v[e+24>>2]=c,(r=v[i+16>>2])&&(v[e+16>>2]=r,v[r+24>>2]=e),(r=v[i+20>>2])&&(v[e+20>>2]=r,v[r+24>>2]=e)}u>>>0<=15?(r=u+a|0,v[i+4>>2]=3|r,v[4+(r=r+i|0)>>2]=1|v[r+4>>2]):(v[i+4>>2]=3|a,v[4+(e=i+a|0)>>2]=1|u,v[e+u>>2]=u,k&&(f=21996+(-8&k)|0,t=v[5494],(r=1<<(k>>>3))&o?r=v[f+8>>2]:(v[5489]=r|o,r=f),v[f+8>>2]=t,v[r+12>>2]=t,v[t+12>>2]=f,v[t+8>>2]=r),v[5494]=e,v[5491]=u),r=i+8|0}return V=n+16|0,0|r}function Q(r,n,f,i,t){var o,a=0,c=0,b=0,s=0,h=0,d=0,y=0,m=w(0),g=0,F=0,A=0,T=0,$=0,I=0,C=0,P=0,E=0,O=0,R=0,S=0,W=0,G=0,U=0,j=0,H=0,L=0,M=0,_=0,z=0,x=0,J=0,K=0,B=0,N=0,q=0,D=0,Z=0,Y=0,X=0,Q=0,rr=0,nr=0,fr=0;V=o=V-32|0;r:{n:{f:{i:{if(f){if($=zn(n,1))break i;break n}if(y=Of(a=ht(68),f=$t(b=o+20|0,f=(f=zn(n,1))?v[(v[i+200>>2]+(f<<2)|0)-4>>2]:0,0)),Oe(f),(0|(a=zn(n,1)))>0)for(b=y+36|0,f=0;s=zn(n,1),_n(b,v[i+28>>2]+(s<<2)|0),(0|a)!=(0|(f=f+1|0)););if((0|(a=zn(n,1)))>0)for(b=y+52|0,f=0;s=zn(n,1),v[o+20>>2]=v[v[i+112>>2]+(s<<2)>>2],_n(b,o+20|0),(0|a)!=(0|(f=f+1|0)););if((0|(a=zn(n,1)))>0)for(b=y+52|0,f=0;s=zn(n,1),v[o+20>>2]=v[v[i+128>>2]+(s<<2)>>2],_n(b,o+20|0),(0|a)!=(0|(f=f+1|0)););if((0|(a=zn(n,1)))>0)for(b=y+52|0,f=0;s=zn(n,1),v[o+20>>2]=v[v[i+144>>2]+(s<<2)>>2],_n(b,o+20|0),(0|a)!=(0|(f=f+1|0)););$=zn(n,1);break f}y=Of(a=ht(68),f=$t(o+20|0,2155,0)),Oe(f)}if((0|$)<=0)break r;for(;;){f:{if(P=zn(n,1),C=0,(0|(E=zn(n,1)))>0)for(;;){b=I=$t(a=o+20|0,f=(f=zn(n,1))?v[(v[i+200>>2]+(f<<2)|0)-4>>2]:0,0),f=0,V=s=V-80|0,d=$t(c=s+68|0,a=(a=zn(n,1))?v[(v[i+200>>2]+(a<<2)|0)-4>>2]:0,0),v[d+4>>2]||sf(d,b),a=v[n+4>>2],v[n+4>>2]=a+1;i:{e:switch(l[0|a]){case 0:a=$t(a=s+56|0,f=(f=zn(n,1))?v[(v[i+200>>2]+(f<<2)|0)-4>>2]:0,0),v[a+4>>2]||sf(a,d),f=v[n+4>>2],v[n+4>>2]=f+1,b=l[0|f],v[n+4>>2]=f+2,h=l[f+1|0],v[n+4>>2]=f+3,g=l[f+2|0],v[n+4>>2]=f+4,F=l[f+3|0],v[n+4>>2]=f+5,A=l[f+4|0],v[n+4>>2]=f+6,T=l[f+5|0],v[n+4>>2]=f+7,S=l[f+6|0],v[n+4>>2]=f+8,W=l[f+7|0],v[n+4>>2]=f+9,G=l[f+8|0],v[n+4>>2]=f+10,U=l[f+9|0],v[n+4>>2]=f+11,j=l[f+10|0],v[n+4>>2]=f+12,H=l[f+11|0],v[n+4>>2]=f+13,L=l[f+12|0],v[n+4>>2]=f+14,M=l[f+13|0],v[n+4>>2]=f+15,_=l[f+14|0],v[n+4>>2]=f+16,z=l[f+15|0],v[n+4>>2]=f+17,x=l[f+16|0],v[n+4>>2]=f+18,J=l[f+17|0],v[n+4>>2]=f+19,K=l[f+18|0],v[n+4>>2]=f+20,B=l[f+19|0],v[n+4>>2]=f+21,N=l[f+20|0],v[n+4>>2]=f+22,q=l[f+21|0],v[n+4>>2]=f+23,D=l[f+22|0],v[n+4>>2]=f+24,Z=l[f+23|0],v[n+4>>2]=f+25,Y=l[f+24|0],v[n+4>>2]=f+26,X=l[f+25|0],v[n+4>>2]=f+27,Q=l[f+26|0],c=f+28|0,v[n+4>>2]=c,rr=l[f+27|0],l[21508]||(v[5373]=0,v[5374]=0,v[5375]=0,v[5376]=0,v[5372]=9404,e[21508]=1,c=v[n+4>>2]),v[n+4>>2]=c+1,p[5373]=w(l[0|c])/w(255),v[n+4>>2]=c+2,p[5374]=w(l[c+1|0])/w(255),v[n+4>>2]=c+3,p[5375]=w(l[c+2|0])/w(255),v[n+4>>2]=c+4,p[5376]=w(l[c+3|0])/w(255),f=v[r+4>>2],c=zf(s+44|0,d),O=zf(s+32|0,a),f=0|La[v[v[f>>2]+12>>2]](f,y,c,O),Oe(O),Oe(c),f&&(sf(f+116|0,a),v[f+40>>2]=F|(g|h<<8|b<<16)<<8,m=p[r+36>>2],v[f+48>>2]=(J<<8|x<<16|K)<<8|B,v[f+44>>2]=(M<<8|L<<16|_)<<8|z,p[f+56>>2]=m*(u(2,(X<<8|Y<<16|Q)<<8|rr),k()),p[f+52>>2]=m*(u(2,(q<<8|N<<16|D)<<8|Z),k()),p[f+36>>2]=m*(u(2,(U<<8|G<<16|j)<<8|H),k()),p[f+32>>2]=m*(u(2,(T<<8|A<<16|S)<<8|W),k()),b=v[5374],v[f+148>>2]=v[5373],v[f+152>>2]=b,b=v[5376],v[f+156>>2]=v[5375],v[f+160>>2]=b,Br(f),b=v[r+4>>2],La[v[v[b>>2]+36>>2]](b,f)),Oe(a);break i;case 1:a=zn(n,1),f=v[r+4>>2],b=zf(s+56|0,d),f=0|La[v[v[f>>2]+20>>2]](f,y,b),Oe(b),Pr(r,n,f,a),t&&(v[n+4>>2]=v[n+4>>2]+4),a=v[r+4>>2],La[v[v[a>>2]+36>>2]](a,f);break i;case 2:b=$t(a=s+56|0,f=(f=zn(n,1))?v[(v[i+200>>2]+(f<<2)|0)-4>>2]:0,0),v[b+4>>2]||sf(b,d),f=v[r+4>>2],a=zf(s+44|0,d),c=zf(s+32|0,b),f=0|La[v[v[f>>2]+16>>2]](f,y,a,c),Oe(c),Oe(a),f?(sf(f+168|0,b),c=Fa(f),a=v[n+4>>2],v[n+4>>2]=a+1,p[c+4>>2]=w(l[0|a])/w(255),v[n+4>>2]=a+2,p[c+8>>2]=w(l[a+1|0])/w(255),v[n+4>>2]=a+3,p[c+12>>2]=w(l[a+2|0])/w(255),v[n+4>>2]=a+4,p[c+16>>2]=w(l[a+3|0])/w(255),Yr(n,(a=zn(n,1))<<1,w(1),f+120|0),bf(n,f+136|0),Pr(r,n,f,a),hr(f),nr=f,fr=zn(n,1)<<1,v[nr+224>>2]=fr,t?(bf(n,f+152|0),a=v[n+4>>2],v[n+4>>2]=a+1,c=l[0|a],v[n+4>>2]=a+2,h=l[a+1|0],v[n+4>>2]=a+3,g=l[a+2|0],v[n+4>>2]=a+4,m=p[r+36>>2],p[f+196>>2]=m*(u(2,l[a+3|0]|(g|h<<8|c<<16)<<8),k()),v[n+4>>2]=a+5,c=l[a+4|0],v[n+4>>2]=a+6,h=l[a+5|0],v[n+4>>2]=a+7,g=l[a+6|0],v[n+4>>2]=a+8,m=w(m*(u(2,l[a+7|0]|(g|h<<8|c<<16)<<8),k()))):(v[f+196>>2]=0,m=w(0)),p[f+200>>2]=m,a=v[r+4>>2],La[v[v[a>>2]+36>>2]](a,f)):lf(r,8092,v[d+8>>2]),Oe(b);break i;case 3:b=$t(a=s+56|0,f=(f=zn(n,1))?v[(v[i+200>>2]+(f<<2)|0)-4>>2]:0,0),v[b+4>>2]||sf(b,d),f=v[r+4>>2],a=zf(s+44|0,d),c=zf(s+32|0,b),f=0|La[v[v[f>>2]+16>>2]](f,y,a,c),Oe(c),Oe(a),f?(sf(f+168|0,b),c=Fa(f),a=v[n+4>>2],v[n+4>>2]=a+1,p[c+4>>2]=w(l[0|a])/w(255),v[n+4>>2]=a+2,p[c+8>>2]=w(l[a+1|0])/w(255),v[n+4>>2]=a+3,p[c+12>>2]=w(l[a+2|0])/w(255),v[n+4>>2]=a+4,p[c+16>>2]=w(l[a+3|0])/w(255),c=$t(c=s+44|0,a=(a=zn(n,1))?v[(v[i+200>>2]+(a<<2)|0)-4>>2]:0,0),h=$t(h=s+32|0,a=(a=zn(n,1))?v[(v[i+200>>2]+(a<<2)|0)-4>>2]:0,0),a=v[n+4>>2],v[n+4>>2]=a+1,g=l[0|a],t&&(v[n+4>>2]=a+2,F=l[a+1|0],v[n+4>>2]=a+3,A=l[a+2|0],v[n+4>>2]=a+4,T=l[a+3|0],v[n+4>>2]=a+5,m=p[r+36>>2],p[f+196>>2]=m*(u(2,l[a+4|0]|(T|A<<8|F<<16)<<8),k()),v[n+4>>2]=a+6,F=l[a+5|0],v[n+4>>2]=a+7,A=l[a+6|0],v[n+4>>2]=a+8,T=l[a+7|0],v[n+4>>2]=a+9,p[f+200>>2]=m*(u(2,l[a+8|0]|(T|A<<8|F<<16)<<8),k())),g=Je(T=ht(40),f,a=zf(s+20|0,c),P,A=zf(F=s+8|0,h),!!(0|g)),Oe(A),Oe(a),v[s+8>>2]=g,_n(r+8|0,F),Oe(h),Oe(c)):lf(r,8092,v[d+8>>2]),Oe(b);break i;case 4:if(a=v[r+4>>2],b=zf(s+56|0,d),a=0|La[v[v[a>>2]+24>>2]](a,y,b),Oe(b),!a){lf(r,8092,v[d+8>>2]);break i}if(f=v[n+4>>2],v[n+4>>2]=f+1,c=0,e[a+80|0]=0!=l[0|f],v[n+4>>2]=f+2,e[a+81|0]=0!=l[f+1|0],Pr(r,n,a,f=zn(n,1)),v[s+56>>2]=0,An(a- -64|0,h=(0|f)/3|0,s+56|0),(0|f)>=3)for(g=v[a+76>>2],f=v[n+4>>2];v[n+4>>2]=f+1,F=l[0|f],v[n+4>>2]=f+2,A=l[f+1|0],v[n+4>>2]=f+3,T=l[f+2|0],b=f+4|0,v[n+4>>2]=b,p[g+(c<<2)>>2]=p[r+36>>2]*(u(2,l[f+3|0]|(T|A<<8|F<<16)<<8),k()),f=b,(0|h)!=(0|(c=c+1|0)););t&&(v[n+4>>2]=v[n+4>>2]+4),f=v[r+4>>2],La[v[v[f>>2]+36>>2]](f,a),f=a;break i;case 5:if(a=v[r+4>>2],b=zf(s+56|0,d),a=0|La[v[v[a>>2]+28>>2]](a,y,b),Oe(b),!a){lf(r,8092,v[d+8>>2]);break i}f=v[n+4>>2],v[n+4>>2]=f+1,b=l[0|f],v[n+4>>2]=f+2,c=l[f+1|0],v[n+4>>2]=f+3,h=l[f+2|0],v[n+4>>2]=f+4,v[a+28>>2]=l[f+3|0]|(h|c<<8|b<<16)<<8,v[n+4>>2]=f+5,b=l[f+4|0],v[n+4>>2]=f+6,c=l[f+5|0],v[n+4>>2]=f+7,h=l[f+6|0],v[n+4>>2]=f+8,m=p[r+36>>2],p[a+20>>2]=m*(u(2,l[f+7|0]|(h|c<<8|b<<16)<<8),k()),v[n+4>>2]=f+9,b=l[f+8|0],v[n+4>>2]=f+10,c=l[f+9|0],v[n+4>>2]=f+11,h=l[f+10|0],v[n+4>>2]=f+12,p[a+24>>2]=m*(u(2,l[f+11|0]|(h|c<<8|b<<16)<<8),k()),t&&(v[n+4>>2]=f+16),f=v[r+4>>2],La[v[v[f>>2]+36>>2]](f,a),f=a;break i;case 6:break e;default:break i}b=zn(n,1),c=zn(n,1),a=v[r+4>>2],(a=0|La[v[v[a>>2]+32>>2]](a,y,d))?(Pr(r,n,a,c),v[a+64>>2]=v[v[i+44>>2]+(b<<2)>>2],t&&(v[n+4>>2]=v[n+4>>2]+4),f=v[r+4>>2],La[v[v[f>>2]+36>>2]](f,a),f=a):lf(r,8092,v[d+8>>2])}if(Oe(d),V=s+80|0,!f)break f;if(Ht(y,P,a=zf(o+8|0,I),f),Oe(a),Oe(I),(0|E)==(0|(C=C+1|0)))break}if((0|(R=R+1|0))!=(0|$))continue;break r}break}La[v[v[y>>2]+4>>2]](y),Oe(I)}y=0}return V=o+32|0,y}function rr(r,n,f){r|=0,n|=0,f|=0;var i,t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=0,h=0,d=w(0),y=w(0),m=0,g=0,F=w(0),A=w(0),T=w(0),$=0,I=0,C=w(0),P=w(0),E=0,O=0,R=w(0),S=w(0),W=w(0),G=w(0),U=w(0),j=w(0),H=0,L=0,M=0;if(V=i=V-16|0,!v[r+224>>2]){v[r+224>>2]=f,u=v[f+52>>2],v[i+12>>2]=0,An(b=r+128|0,u,i+12|0),Xe(f,n,0,u,b,0,2),en(b),f=0,V=c=V-16|0,n=v[b+4>>2],v[40+(a=r+4|0)>>2]=0,Oa(g=a+36|0,k=n>>>1|0),v[c+12>>2]=0,Tn(g,k,c+12|0);r:if(n>>>0>=2){for(u=k>>>0<=1?1:k,s=v[a+48>>2];v[s+(f<<2)>>2]=f,(0|u)!=(0|(f=f+1|0)););if(ae(u=a+52|0,k),f=0,e[c+11|0]=0,Sn(u,k,c+11|0),n>>>0<2)break r;for(u=k>>>0<=1?1:k,s=v[a- -64>>2];L=f+s|0,M=of(f,k,b,g),e[0|L]=M,(0|u)!=(0|(f=f+1|0)););}else ae(f=a+52|0,k),e[c+11|0]=0,Sn(f,k,c+11|0);if(v[a+72>>2]=0,Oa($=a+68|0,(((0|k)<=2?2:k)<<2)-8|0),n>>>0>=8)for(t=v[a- -64>>2];;){I=v[b+12>>2],h=v[a+48>>2],n=0,f=1,s=k=(o=k)-1|0;r:{for(;;){n:{u=n,n=f;f:if(!l[t+u|0]){if((0|s)==(0|(f=(0|(f=n+1|0))!=(0|o)?f:0)))break n;for(m=I+(v[h+(n<<2)>>2]<<3)|0,C=p[m>>2],E=I+(v[h+(u<<2)>>2]<<3)|0,T=p[E>>2],O=I+(v[h+(s<<2)>>2]<<3)|0,R=p[O>>2],y=p[m+4>>2],d=p[E+4>>2],G=w(y-d),A=p[O+4>>2],U=w(d-A),j=w(A-y);;){if(l[f+t|0]&&(m=I+(v[h+(f<<2)>>2]<<3)|0,P=p[m>>2],F=p[m+4>>2],!(!(w(w(P*j)+w(w(C*w(F-A))+w(R*w(y-F))))>=w(0))|!(w(w(P*U)+w(w(R*w(F-d))+w(T*w(A-F))))>=w(0)))&&w(w(P*G)+w(w(T*w(F-y))+w(C*w(d-F))))>=w(0)))break f;if((0|s)==(0|(f=(f+1>>>0)%(o>>>0)|0)))break}break n}if(n){f=(0|(f=n+1|0))!=(0|o)?f:0,s=u;continue}for(;;){if(!l[t+u|0])break n;if(n=0,!(u=u-1|0))break r}}break}n=u}if(_n($,h+(((I=n+k|0)>>>0)%(o>>>0)<<2)|0),_n($,(f=n<<2)+v[a+48>>2]|0),_n($,v[a+48>>2]+((n+1>>>0)%(o>>>0)<<2)|0),u=v[a+40>>2]-1|0,v[a+40>>2]=u,n>>>0<u>>>0)for(s=v[a+48>>2],o=v[s+f>>2],f=n;m=s+(f<<2)|0,t=s+((f=f+1|0)<<2)|0,v[m>>2]=v[t>>2],v[t>>2]=o,(0|f)!=(0|u););if(s=v[a+56>>2]-1|0,v[a+56>>2]=s,t=v[a+64>>2],n>>>0<s>>>0)for(o=l[n+t|0],f=n;m=l[0|(h=(u=f+1|0)+t|0)],e[0|h]=o,e[f+t|0]=m,(0|s)!=(0|(f=u)););if(L=(f=(I-1>>>0)%(k>>>0)|0)+t|0,M=of(f,k,b,g),e[0|L]=M,L=(n=(0|n)!=(0|k)?n:0)+t|0,M=of(n,k,b,g),e[0|L]=M,!(k>>>0>3))break}if(3==(0|k)&&(_n($,v[a+48>>2]+8|0),_n($,v[a+48>>2]),_n($,v[a+48>>2]+4|0)),f=$,n=0,V=t=(V=c+16|0)-48|0,u=v[a+8>>2])for(s=a+84|0;Tf(s,v[v[a+16>>2]+(n<<2)>>2]),(0|u)!=(0|(n=n+1|0)););if(n=0,v[a+8>>2]=0,u=v[a+24>>2])for(s=a+104|0;Tf(s,v[v[a+32>>2]+(n<<2)>>2]),(0|u)!=(0|(n=n+1|0)););if(s=a+4|0,v[a+24>>2]=0,n=da(k=a+104|0),v[t+44>>2]=n,v[n+4>>2]=0,n=pa($=a+84|0),v[t+40>>2]=n,v[n+4>>2]=0,E=v[f+4>>2]){for(g=a+20|0,I=-1,u=0,n=0;o=v[f+12>>2]+(n<<2)|0,h=(c=v[o>>2])<<1,v[t+36>>2]=h,m=v[o+4>>2],v[t+32>>2]=m<<1,O=v[o+8>>2],v[t+28>>2]=O<<1,c=(o=v[b+12>>2])+(c<<3)|0,p[t+24>>2]=p[c>>2],p[t+20>>2]=p[c+4>>2],c=o+(m<<3)|0,p[t+16>>2]=p[c>>2],p[t+12>>2]=p[c+4>>2],F=p[(o=o+(O<<3)|0)>>2],p[t+8>>2]=F,y=p[o+4>>2],p[t+4>>2]=y,c=v[t+40>>2],m=v[c+4>>2],(0|h)!=(0|I)||(o=v[c+12>>2],d=p[(h=o+(m<<2)|0)-16>>2],C=p[h-12>>2],A=w(p[h-4>>2]-C),T=w(p[h-8>>2]-d),(0|(w(w(w(T*C)+w(w(F*A)-w(y*T)))-w(d*A))>=w(0)?1:-1))!=(0|u)||(d=w(p[o>>2]-F),A=w(d*y),y=w(p[o+4>>2]-y),(0|(w(w(A+w(w(p[o+8>>2]*y)-w(p[o+12>>2]*d)))-w(F*y))>=w(0)?1:-1))!=(0|u)))?(m?(_n(s,t+40|0),_n(g,t+44|0)):(Tf($,c),Tf(k,v[t+44>>2])),u=pa($),v[t+40>>2]=u,v[u+4>>2]=0,Mn(u,t+24|0),Mn(v[t+40>>2],t+20|0),Mn(v[t+40>>2],t+16|0),Mn(v[t+40>>2],t+12|0),Mn(v[t+40>>2],t+8|0),Mn(v[t+40>>2],t+4|0),u=da(k),v[t+44>>2]=u,v[u+4>>2]=0,_n(u,t+36|0),_n(v[t+44>>2],t+32|0),_n(v[t+44>>2],t+28|0),y=p[t+20>>2],F=w(p[t+12>>2]-y),d=p[t+24>>2],A=w(p[t+16>>2]-d),u=w(w(w(A*y)+w(w(p[t+8>>2]*F)-w(p[t+4>>2]*A)))-w(d*F))>=w(0)?1:-1,I=v[t+36>>2]):(Mn(c,t+8|0),Mn(v[t+40>>2],t+4|0),_n(v[t+44>>2],t+28|0)),E>>>0>(n=n+3|0)>>>0;);v[v[t+40>>2]+4>>2]&&(_n(s,t+40|0),_n(g,t+44|0))}if(o=v[a+8>>2]){for(f=0;;){if(n=v[(u=f<<2)+v[a+32>>2]>>2],v[t+44>>2]=n,b=v[n+4>>2])for(n=v[n+12>>2],c=v[(n+(b<<2)|0)-4>>2],h=v[n>>2],n=v[u+v[a+16>>2]>>2],v[t+40>>2]=n,u=v[n+4>>2]<<2,n=v[n+12>>2],F=p[(u=u+n|0)-8>>2],d=p[u-4>>2],A=p[u-12>>2],y=w(d-A),C=p[u-16>>2],T=w(F-C),R=p[n>>2],P=p[n+4>>2],G=w(w(w(T*A)+w(w(R*y)-w(P*T)))-w(C*y)),U=p[n+12>>2],j=p[n+8>>2],n=0;y=d,(0|n)!=(0|f)?(u=v[(g=n<<2)+v[a+32>>2]>>2],3!=v[u+4>>2]||(b=v[u+12>>2],I=v[b+4>>2],m=v[b>>2],v[t+36>>2]=v[b+8>>2],b=v[g+v[a+16>>2]>>2],g=(v[b+4>>2]<<2)+v[b+12>>2]|0,d=p[g-8>>2],p[t+32>>2]=d,T=p[g-4>>2],p[t+28>>2]=T,(0|h)!=(0|m)|(0|c)!=(0|I)||(g=G>=w(0),S=w(F-C),W=w(y-A),(0|g)==(0|!(w(w(w(S*A)+w(w(d*W)-w(T*S)))-w(C*W))>=w(0)))||(S=w(R-d),W=w(S*T),T=w(P-T),g^w(w(W+w(w(j*T)-w(U*S)))-w(d*T))>=w(0))))?d=y:(n=0,v[b+4>>2]=0,v[u+4>>2]=0,Mn(v[t+40>>2],t+32|0),Mn(v[t+40>>2],t+28|0),_n(v[t+44>>2],t+36|0),d=p[t+28>>2],A=y,C=F,F=p[t+32>>2])):n=f,o>>>0>(n=n+1|0)>>>0;);if((0|o)==(0|(f=f+1|0)))break}if(!((0|(u=v[a+8>>2]))<=0))for(;;){if(o=v[(c=(f=u-1|0)<<2)+v[a+16>>2]>>2],v[t+40>>2]=o,!v[o+4>>2]){if(b=v[a+8>>2]-1|0,v[a+8>>2]=b,b>>>0>(n=f)>>>0){for(;o=v[a+16>>2],g=v[(h=o+(n<<2)|0)>>2],m=o,o=(n=n+1|0)<<2,v[h>>2]=v[m+o>>2],v[o+v[a+16>>2]>>2]=g,(0|n)!=(0|b););o=v[t+40>>2]}if(Tf($,o),o=v[c+v[a+32>>2]>>2],v[t+44>>2]=o,b=v[a+24>>2]-1|0,v[a+24>>2]=b,b>>>0>(n=f)>>>0){for(;o=v[a+32>>2],h=v[(c=o+(n<<2)|0)>>2],m=o,o=(n=n+1|0)<<2,v[c>>2]=v[m+o>>2],v[o+v[a+32>>2]>>2]=h,(0|n)!=(0|b););o=v[t+44>>2]}Tf(k,o)}if(n=(0|u)>1,u=f,!n)break}}if(V=t+48|0,f=s,v[r+228>>2]=f,v[f+4>>2])for(;en(n=v[v[f+12>>2]+(H<<2)>>2]),Mn(n,v[n+12>>2]),Mn(n,v[n+12>>2]+4|0),f=v[r+228>>2],(u=v[f+4>>2])>>>0>(H=H+1|0)>>>0;);else u=0}return V=i+16|0,0|u}function nr(r){var n,f,i,t=0,u=0,o=0,a=0,c=0,b=0,k=0,h=0,d=0,p=0,y=0,m=0;if(v[124+(r|=0)>>2]=0,v[r+108>>2]=0,u=v[r+12>>2])for(;a=v[v[r+20>>2]+(t<<2)>>2],h=l[v[a+4>>2]+60|0],e[a+116|0]=h,e[a+117|0]=1^h,(0|u)!=(0|(t=t+1|0)););if((h=v[r+136>>2])&&(u=v[h+40>>2]))for(t=0;;){for(a=v[v[r+20>>2]+(v[v[v[h+48>>2]+(t<<2)>>2]+4>>2]<<2)>>2];s[a+116>>1]=256,a=v[a+12>>2];);if((0|u)==(0|(t=t+1|0)))break}r:if(h=(i=v[r+92>>2])+((n=v[r+76>>2])+(f=v[r+60>>2])|0)|0)for(a=0;;){n:{f:{i:if(f){if(t=0,d=1,u=v[v[r+68>>2]>>2],v[v[u+4>>2]+16>>2]!=(0|a)){for(;;){if((0|f)==(0|(t=t+1|0)))break i;if(u=v[v[r+68>>2]+(t<<2)>>2],v[v[u+4>>2]+16>>2]==(0|a))break}d=t>>>0<f>>>0}V=c=V-16|0;e:{t:if(l[v[u+40>>2]+117|0]){u:{if(l[v[u+4>>2]+20|0]){if(!(t=v[r+136>>2]))break t;if(!(o=v[t+56>>2])){e[u+44|0]=0;break e}if(k=v[u+4>>2],b=v[t- -64>>2],(0|k)!=v[b>>2]){for(t=0;(0|o)!=(0|(t=t+1|0))&(0|k)!=v[b+(t<<2)>>2];);if(t=t>>>0<o>>>0,e[u+44|0]=t,t)break u;break e}}e[u+44|0]=1}ce(r,v[u+40>>2]),ce(r,k=v[v[u+20>>2]>>2]);u:if(!((t=v[u+12>>2])>>>0<2)){if(o=v[(v[u+20>>2]+(t<<2)|0)-4>>2],v[c+12>>2]=o,b=v[r+108>>2]){if(p=v[r+116>>2],(0|o)==v[p>>2])break u;for(t=0;(0|b)!=(0|(t=t+1|0))&(0|o)!=v[p+(t<<2)>>2];);if(t>>>0<b>>>0)break u}_n(r+120|0,c+12|0)}v[c+8>>2]=u,_n(r+104|0,c+8|0),pi(k+16|0),e[v[(v[u+20>>2]+(v[u+12>>2]<<2)|0)-4>>2]+116|0]=1;break e}e[u+44|0]=0}if(V=c+16|0,a=a+1|0,d)break f}i:if(n){if(t=0,d=1,u=v[v[r+84>>2]>>2],v[v[u+4>>2]+16>>2]!=(0|a)){for(;;){if((0|n)==(0|(t=t+1|0)))break i;if(u=v[v[r+84>>2]+(t<<2)>>2],v[v[u+4>>2]+16>>2]==(0|a))break}d=t>>>0<n>>>0}V=c=V-16|0;e:{t:{u:if(l[v[u+24>>2]+117|0]){o:{if(l[v[u+4>>2]+20|0]){if(!(t=v[r+136>>2]))break u;if(!(o=v[t+56>>2])){e[u+44|0]=0;break e}if(k=v[u+4>>2],b=v[t- -64>>2],(0|k)!=v[b>>2]){for(t=0;(0|o)!=(0|(t=t+1|0))&(0|k)!=v[b+(t<<2)>>2];);if(t=t>>>0<o>>>0,e[u+44|0]=t,t)break o;break e}}e[u+44|0]=1}if(ce(r,v[u+24>>2]),o=v[u+12>>2],l[v[u+4>>2]+85|0]){if(!o)break t;for(p=r+120|0,b=0;;){t=v[v[u+20>>2]+(b<<2)>>2],v[c+12>>2]=t,ce(r,v[t+12>>2]);o:{if(k=v[r+108>>2]){if(t=0,y=v[c+12>>2],m=v[r+116>>2],(0|y)==v[m>>2])break o;for(;(0|k)!=(0|(t=t+1|0))&v[(t<<2)+m>>2]!=(0|y););if(t>>>0<k>>>0)break o}_n(p,c+12|0)}if((0|o)==(0|(b=b+1|0)))break}}else{if(!o)break t;for(t=0;ce(r,v[v[u+20>>2]+(t<<2)>>2]),(0|o)!=(0|(t=t+1|0)););}if(v[c+8>>2]=u,_n(r+104|0,c+8|0),!o)break e;for(t=0;pi(v[v[u+20>>2]+(t<<2)>>2]+16|0),(0|o)!=(0|(t=t+1|0)););if(!o)break e;for(u=v[u+20>>2],t=0;e[v[u+(t<<2)>>2]+116|0]=1,(0|o)!=(0|(t=t+1|0)););break e}e[u+44|0]=0;break e}v[c+8>>2]=u,_n(r+104|0,c+8|0)}if(V=c+16|0,a=a+1|0,d)break f}if(t=0,!i)break n;for(;;){if(u=v[v[r+100>>2]+(t<<2)>>2],v[v[u+4>>2]+16>>2]!=(0|a)){if((0|i)!=(0|(t=t+1|0)))continue;break n}break}V=c=V-16|0;i:{e:{t:if(l[v[v[u+24>>2]+8>>2]+117|0]){u:{if(l[v[u+4>>2]+20|0]){if(!(t=v[r+136>>2]))break t;if(!(o=v[t+56>>2])){e[u+140|0]=0;break i}if(b=v[u+4>>2],d=v[t- -64>>2],(0|b)!=v[d>>2]){for(t=0;(0|o)!=(0|(t=t+1|0))&(0|b)!=v[d+(t<<2)>>2];);if(t=t>>>0<o>>>0,e[u+140|0]=t,t)break u;break i}}e[u+140|0]=1}if(b=v[u+24>>2],d=v[v[b+4>>2]+4>>2],o=v[b+8>>2],(t=v[r+136>>2])?(Qr(r,t,d,o),k=v[r+136>>2]):k=0,t=v[r+4>>2],!(p=v[t+64>>2])|(0|k)==(0|p)||(Qr(r,p,d,o),t=v[r+4>>2]),k=v[t+52>>2])for(t=0;Qr(r,v[v[v[r+4>>2]+60>>2]+(t<<2)>>2],d,o),(0|k)!=(0|(t=t+1|0)););if((t=v[b+60>>2])&&xe(0|La[v[v[t>>2]+8>>2]](t),21356)&&Kn(r,t,o),!(o=v[u+12>>2])){v[c+12>>2]=u,_n(r+104|0,c+12|0);break i}for(t=0;ce(r,v[v[u+20>>2]+(t<<2)>>2]),(0|o)!=(0|(t=t+1|0)););break e}e[u+140|0]=0;break i}if(v[c+12>>2]=u,_n(r+104|0,c+12|0),o){for(t=0;pi(v[v[u+20>>2]+(t<<2)>>2]+16|0),(0|o)!=(0|(t=t+1|0)););if(o)for(u=v[u+20>>2],t=0;e[v[u+(t<<2)>>2]+116|0]=1,(0|o)!=(0|(t=t+1|0)););}}V=c+16|0,a=a+1|0}if(h>>>0>a>>>0)continue;break r}if(!(h>>>0>(a=a+1|0)>>>0))break}if(h=v[r+12>>2])for(a=0;ce(r,v[v[r+20>>2]+(a<<2)>>2]),(0|h)!=(0|(a=a+1|0)););}function fr(r,n,f,i,e,t){var u,o,a,c=0,b=0,k=0,l=0,d=w(0),y=0,F=w(0),A=w(0),T=0,$=0,I=0,C=w(0),P=0,E=w(0),O=0,R=0,S=w(0),W=0,G=0,U=w(0),j=0,H=w(0),L=w(0),M=w(0),_=w(0),z=w(0),x=w(0),J=0,K=w(0),B=w(0),N=w(0),q=0,D=0,Z=0,Y=w(0),X=w(0),Q=w(0),rr=w(0),nr=w(0),fr=w(0),ir=w(0),er=w(0),tr=w(0),ur=w(0),or=0,ar=w(0),cr=w(0),br=w(0),kr=0;V=u=V-16|0,o=v[r+228>>2],a=v[o+4>>2],v[r+196>>2]=0,v[r+164>>2]=0,v[r+180>>2]=0;r:if(i)for(q=r+192|0,D=r+176|0,Z=r+160|0,O=r+144|0;;){n:{if(a)for(k=m(h[(c=(G<<1)+f|0)>>1],t)<<2,Y=p[k+e>>2],l=m(h[c+4>>1],t)<<2,X=p[l+e>>2],c=m(h[c+2>>1],t)<<2,Q=p[c+e>>2],rr=p[(y=k+4|0)+e>>2],nr=p[(T=l+4|0)+e>>2],fr=p[(b=c+4|0)+e>>2],K=p[n+b>>2],U=p[n+T>>2],ir=w(K-U),B=p[n+k>>2],H=p[n+l>>2],er=w(B-H),N=p[n+c>>2],tr=w(H-N),x=p[n+y>>2],ur=w(w(1)/w(w(ir*er)+w(tr*w(x-U)))),br=w(U-x),J=0;;){$=v[r+164>>2],j=v[v[o+12>>2]+(J<<2)>>2],I=0,V=b=V-48|0,p[b+40>>2]=x,p[b+44>>2]=B,p[b+36>>2]=N,p[b+32>>2]=K,p[b+28>>2]=H,p[b+24>>2]=U,c=r+208|0,l=2&v[j+4>>2],v[(k=l?O:c)+4>>2]=0,Mn(k,y=b+44|0),Mn(k,T=b+40|0),Mn(k,b+36|0),Mn(k,b+32|0),Mn(k,b+28|0),Mn(k,b+24|0),Mn(k,y),Mn(k,T),v[(c=l?c:O)+4>>2]=0,kr=v[j+4>>2]-4|0,y=0;f:{i:{for(;;){if(P=v[j+12>>2],F=p[(R=P+(l=y<<2)|0)>>2],p[b+20>>2]=F,d=p[P+(4|l)>>2],p[b+16>>2]=d,or=v[k+4>>2]-2|0){for(l=k,L=p[P+((T=y+2|0)<<2)>>2],ar=w(F-L),F=p[R+12>>2],cr=w(-w(d-F)),k=0;;){P=v[l+12>>2],d=p[P+(4|(R=k<<2))>>2],A=p[(R=P+R|0)>>2],E=p[P+((k=k+2|0)<<2)>>2],p[b+12>>2]=E,C=p[R+12>>2],p[b+8>>2]=C,S=w(w(ar*w(C-F))+w(w(E-L)*cr));e:if(w(w(ar*w(d-F))+w(w(A-L)*cr))>w(0)){if(S>w(0)){Mn(c,b+12|0),Mn(c,b+8|0);break e}if(S=w(C-d),C=p[b+20>>2],M=w(L-C),_=p[b+16>>2],E=w(E-A),z=w(w(S*M)-w(w(F-_)*E)),w(g(z))>w(9.999999974752427e-7)){d=w(w(w(E*w(_-d))-w(w(C-A)*S))/z),p[b+4>>2]=w(M*d)+C,Mn(c,I=b+4|0),A=p[b+16>>2],p[b+4>>2]=w(w(F-A)*d)+A,Mn(c,I),I=1;break e}Mn(c,b+20|0),Mn(c,b+16|0),I=1}else I=1,S>w(0)&&(S=w(C-d),C=p[b+20>>2],M=w(L-C),_=p[b+16>>2],E=w(E-A),z=w(w(S*M)-w(w(F-_)*E)),w(g(z))>w(9.999999974752427e-7)?(d=w(w(w(E*w(_-d))-w(w(C-A)*S))/z),p[b+4>>2]=w(M*d)+C,Mn(c,P=b+4|0),A=p[b+16>>2],p[b+4>>2]=w(w(F-A)*d)+A,Mn(c,P)):(Mn(c,b+20|0),Mn(c,b+16|0)),Mn(c,b+12|0),Mn(c,b+8|0));if(!(k>>>0<or>>>0))break}if(v[c+4>>2]){if(Mn(c,v[c+12>>2]),Mn(c,v[c+12>>2]+4|0),(0|y)==(0|kr))break i;v[l+4>>2]=0,y=T,k=c,c=l;continue}}break}v[O+4>>2]=0,I=1;break f}if((0|c)==(0|O))c=v[O+4>>2],v[b+20>>2]=0,An(O,c-2|0,b+20|0);else{if(k=0,v[O+4>>2]=0,!(l=v[c+4>>2]-2|0))break f;for(;Mn(O,v[c+12>>2]+(k<<2)|0),(0|l)!=(0|(k=k+1|0)););}}if(V=b+48|0,!I)break n;if(k=v[r+148>>2]){for(c=0,v[u+12>>2]=0,An(Z,l=(-2&k)+$|0,y=u+12|0),v[u+12>>2]=0,An(q,l,y),l=v[r+204>>2],y=v[r+172>>2],T=v[r+156>>2];F=p[(I=c<<2)+T>>2],j=4+(b=$<<2)|0,d=p[T+(4|I)>>2],p[j+y>>2]=d,p[b+y>>2]=F,A=w(F-H),d=w(d-U),F=w(ur*w(w(ir*A)+w(tr*d))),d=w(ur*w(w(br*A)+w(er*d))),A=w(w(w(1)-F)-d),p[l+j>>2]=w(nr*A)+w(w(rr*F)+w(fr*d)),p[b+l>>2]=w(X*A)+w(w(Y*F)+w(Q*d)),$=$+2|0,k>>>0>(c=c+2|0)>>>0;);if($=v[r+180>>2],s[u+12>>1]=0,$n(D,(m(k=k>>>1|0,3)+$|0)-6|0,u+12|0),(y=k-1|0)>>>0>=2)for(T=v[r+188>>2],c=1;s[(l=T+($<<1)|0)>>1]=W,b=c+W|0,s[l+2>>1]=b,s[l+4>>1]=b+1,$=$+3|0,(0|y)!=(0|(c=c+1|0)););W=k+W|0}if((0|a)==(0|(J=J+1|0)))break}if((G=G+3|0)>>>0<i>>>0)continue;break r}if(v[u+12>>2]=0,An(Z,c=$+6|0,l=u+12|0),v[u+12>>2]=0,An(q,c,l),k=v[r+172>>2],p[k+(y=20+(c=$<<2)|0)>>2]=U,p[(T=c+16|0)+k>>2]=H,p[(b=c+12|0)+k>>2]=K,p[($=c+8|0)+k>>2]=N,p[(I=c+4|0)+k>>2]=x,p[c+k>>2]=B,k=v[r+204>>2],p[k+y>>2]=nr,p[k+T>>2]=X,p[b+k>>2]=fr,p[k+$>>2]=Q,p[k+I>>2]=rr,p[c+k>>2]=Y,c=v[r+180>>2],s[u+12>>1]=0,$n(D,c+3|0,l),c=v[r+188>>2]+(c<<1)|0,s[c>>1]=W,s[c+4>>1]=W+2,s[c+2>>1]=W+1,W=W+3|0,!((G=G+3|0)>>>0<i>>>0))break}V=u+16|0}function ir(r,n){var f,i=0,e=0,o=0,a=0,c=0,s=0,l=0,h=0,d=0,p=0,F=0,T=0,$=0,I=0,C=0,P=0,E=0,O=0,R=0,S=0,W=0,G=0;V=f=V-16|0,b(r);r:if((o=2147483647&(P=t(2)))>>>0<=1305022426){if(C=(h=+r)+-1.5707963109016418*(i=.6366197723675814*h+6755399441055744-6755399441055744)+-1.5893254773528196e-8*i,y[n>>3]=C,a=C<-.7853981852531433,o=g(i)<2147483648?~~i:-2147483648,a){i+=-1,y[n>>3]=h+-1.5707963109016418*i+-1.5893254773528196e-8*i,o=o-1|0;break r}if(!(C>.7853981852531433))break r;i+=1,y[n>>3]=h+-1.5707963109016418*i+-1.5893254773528196e-8*i,o=o+1|0}else if(o>>>0>=2139095040)y[n>>3]=w(r-r),o=0;else{if(e=o,o=(o>>>23|0)-150|0,y[f+8>>3]=(u(2,e-(o<<23)|0),k()),E=f+8|0,V=c=V-560|0,l=o+m($=(0|(e=(o-3|0)/24|0))>0?e:0,-24)|0,(0|(p=v[4456]))>=0)for(o=p+1|0,e=$;y[(c+320|0)+(a<<3)>>3]=(0|e)<0?0:+v[17840+(e<<2)>>2],e=e+1|0,(0|o)!=(0|(a=a+1|0)););for(F=l-24|0,o=0,a=(0|p)>0?p:0;;){for(e=0,i=0;i=y[(e<<3)+E>>3]*y[(c+320|0)+(o-e<<3)>>3]+i,1!=(0|(e=e+1|0)););if(y[(o<<3)+c>>3]=i,e=(0|o)==(0|a),o=o+1|0,e)break}W=47-l|0,O=48-l|0,R=(0|l)<25,G=l-25|0,o=p;n:{for(;;){if(i=y[(o<<3)+c>>3],e=0,a=o,!(d=(0|o)<=0))for(;T=(c+480|0)+(e<<2)|0,s=g(h=5.960464477539063e-8*i)<2147483648?~~h:-2147483648,s=g(i=-16777216*(h=+(0|s))+i)<2147483648?~~i:-2147483648,v[T>>2]=s,i=y[((a=a-1|0)<<3)+c>>3]+h,(0|(e=e+1|0))!=(0|o););i=xn(i,F),i+=-8*A(.125*i),i-=+(0|(T=g(i)<2147483648?~~i:-2147483648));f:{i:{e:{if(R){if(F)break e;s=v[476+((o<<2)+c|0)>>2]>>23}else I=e=(o<<2)+c|0,e=(s=v[e+476>>2])-((a=s>>O)<<O)|0,v[I+476>>2]=e,T=a+T|0,s=e>>W;if((0|s)<=0)break f;break i}if(s=2,!(i>=.5)){s=0;break f}}if(e=0,a=0,!d)for(;S=v[(I=(c+480|0)+(e<<2)|0)>>2],d=16777215,a||(d=16777216,S)?(v[I>>2]=d-S,a=1):a=0,(0|(e=e+1|0))!=(0|o););i:if(!R){e=8388607;e:switch(0|G){case 1:e=4194303;break;case 0:break e;default:break i}v[476+(d=(o<<2)+c|0)>>2]=v[d+476>>2]&e}T=T+1|0,2==(0|s)&&(i=1-i,s=2,a&&(i-=xn(1,F)))}if(0!=i)break;if(e=1,d=0,a=o,!((0|o)<=(0|p))){for(;d=v[(c+480|0)+((a=a-1|0)<<2)>>2]|d,(0|a)>(0|p););if(d){for(l=F;l=l-24|0,!v[(c+480|0)+((o=o-1|0)<<2)>>2];);break n}}for(;a=e,e=e+1|0,!v[(c+480|0)+(p-a<<2)>>2];);for(a=o+a|0;;){for(y[(c+320|0)+((o=o+1|0)<<3)>>3]=v[17840+(o+$<<2)>>2],e=0,i=0;i=y[(e<<3)+E>>3]*y[(c+320|0)+(o-e<<3)>>3]+i,1!=(0|(e=e+1|0)););if(y[(o<<3)+c>>3]=i,!((0|o)<(0|a)))break}o=a}(i=xn(i,24-l|0))>=16777216?(F=(c+480|0)+(o<<2)|0,e=g(h=5.960464477539063e-8*i)<2147483648?~~h:-2147483648,a=g(i=-16777216*+(0|e)+i)<2147483648?~~i:-2147483648,v[F>>2]=a,o=o+1|0):(e=g(i)<2147483648?~~i:-2147483648,l=F),v[(c+480|0)+(o<<2)>>2]=e}if(i=xn(1,l),(0|o)>=0){for(a=o;y[((e=a)<<3)+c>>3]=i*+v[(c+480|0)+(e<<2)>>2],a=e-1|0,i*=5.960464477539063e-8,e;);for(a=o;;){if(i=0,e=0,(0|(F=(0|(l=o-a|0))>(0|p)?p:l))>=0)for(;i=y[20608+(e<<3)>>3]*y[(e+a<<3)+c>>3]+i,$=(0|e)!=(0|F),e=e+1|0,$;);if(y[(c+160|0)+(l<<3)>>3]=i,e=(0|a)>0,a=a-1|0,!e)break}}if(i=0,(0|o)>=0)for(;a=o,o=o-1|0,i+=y[(c+160|0)+(a<<3)>>3],a;);y[f>>3]=s?-i:i,V=c+560|0,o=7&T,i=y[f>>3],(0|P)<0?(y[n>>3]=-i,o=0-o|0):y[n>>3]=i}return V=f+16|0,o}function er(r,n,f){var i,e=0,t=0,u=0,o=0,a=0,c=0,b=0,k=0;if(V=i=V+-64|0,v[i+60>>2]=r,v[i+52>>2]=0,v[i+56>>2]=0,v[i+48>>2]=17208,(0|(c=v[n+4>>2]))>0)for(r=0;;){t=(e=m(r,12))+v[n+12>>2]|0,u=e+v[f+12>>2]|0;r:{n:{if(e=v[i+52>>2])for(;;){if(se(o=e+4|0,t))break n;if(!(e=v[e+28>>2]))break}e=ht(36),v[e>>2]=17224,o=Ut(e+4|0),a=Ut(e+16|0),v[e+28>>2]=0,v[e+32>>2]=0,sf(o,t),sf(a,u),(t=v[i+52>>2])&&(v[t+32>>2]=e,v[e+28>>2]=t),v[i+52>>2]=e,v[i+56>>2]=v[i+56>>2]+1;break r}sf(o,t),sf(e+16|0,u)}if((0|c)==(0|(r=r+1|0)))break}r:{n:{if(e=v[5476])for(t=v[i+60>>2],r=e;;){if(v[r+4>>2]==(0|t))break n;if(!(r=v[r+12>>2]))break}r=ht(12),v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=17240,v[i+44>>2]=r;f:{i:{if(r=v[5476])for(n=v[i+60>>2];;){if((0|n)==v[r+4>>2])break i;if(!(r=v[r+12>>2]))break}r=ht(20),v[r+12>>2]=0,v[r+16>>2]=0,v[r>>2]=17096,v[r+4>>2]=v[i+60>>2],v[r+8>>2]=v[i+44>>2],(n=v[5476])&&(v[n+16>>2]=r,v[r+12>>2]=n),v[5476]=r,v[5477]=v[5477]+1;break f}v[r+4>>2]=n,v[r+8>>2]=v[i+44>>2]}t=v[i+60>>2];break r}n:{f:{for(;;){if(v[e+4>>2]==(0|t))break f;if(!(e=v[e+12>>2]))break}r=0;break n}r=v[e+8>>2]}v[i+44>>2]=r}if(c=v[t+52>>2])for(;;){au(i+32|0,v[v[t+60>>2]+(b<<2)>>2]),r=v[i+36>>2],n=v[i+32>>2];r:if(!(r>>>0>=(e=v[n+4>>2])>>>0))for(;;){if(f=v[i+40>>2],u=v[n+12>>2],f>>>0>=d[4+(u+(r<<4)|0)>>2]){n:{f:{for(;;){if((0|e)==(0|(r=r+1|0)))break f;if(v[4+(u+(r<<4)|0)>>2])break}n=r;break n}n=e}if(v[i+36>>2]=n,e=r>>>0>=e>>>0,f=0,r=n,e)break r}r=v[12+(u+(r<<4)|0)>>2],v[i+40>>2]=f+1,v[i+28>>2]=0,n=v[16+(r+m(f,20)|0)>>2],v[i+24>>2]=n;n:{f:{i:{if(r=e=v[v[i+44>>2]+4>>2])for(;;){if((0|n)==v[r+4>>2])break i;if(!(r=v[r+12>>2]))break}f=0;e:if(Su(0|La[v[v[n>>2]+8>>2]](n),21440)){if(!(r=v[n+24>>2]))break e;for(f=Yn(Jt(32),4,17184,6,v[r+4>>2]+16|0),e=v[n+112>>2],u=v[v[f+4>>2]>>2],r=0,n=0;o=u+m(r,24)|0,a=n<<2,p[o+12>>2]=p[a+e>>2],p[o+16>>2]=p[e+(4|a)>>2],n=n+2|0,4!=(0|(r=r+1|0)););}else if(Su(0|La[v[v[n>>2]+8>>2]](n),21344)&&(r=v[n+68>>2])&&(f=Yn(Jt(32),v[n+52>>2]>>>1|0,v[n+148>>2],v[n+140>>2],v[r+4>>2]+16|0),r=v[n+52>>2]))for(u=v[v[f+4>>2]>>2],o=r-1>>>1|0,e=v[n+116>>2],n=0,r=0;a=u+m(r,24)|0,k=n<<2,p[a+12>>2]=p[k+e>>2],p[a+16>>2]=p[e+(4|k)>>2],n=n+2|0,a=(0|r)!=(0|o),r=r+1|0,a;);if(v[i+28>>2]=f,!f)break n;e:{t:{if(n=v[i+44>>2],r=v[n+4>>2])for(f=v[i+24>>2];;){if((0|f)==v[r+4>>2])break t;if(!(r=v[r+12>>2]))break}r=ht(20),v[r+12>>2]=0,v[r+16>>2]=0,v[r>>2]=17256,v[r+4>>2]=v[i+24>>2],v[r+8>>2]=v[i+28>>2],(f=v[n+4>>2])&&(v[f+16>>2]=r,v[r+12>>2]=f),v[n+4>>2]=r,v[n+8>>2]=v[n+8>>2]+1;break e}v[r+4>>2]=f,v[r+8>>2]=v[i+28>>2]}r=v[i+28>>2];break f}i:{e:{for(;;){if((0|n)==v[e+4>>2])break e;if(!(e=v[e+12>>2]))break}r=0;break i}r=v[e+8>>2]}v[i+28>>2]=r}if(r){n=r+20|0;f:{if(r=v[i+52>>2])for(;;){if(se(r+4|0,n))break f;if(!(r=v[r+28>>2]))break}r=zf(i+12|0,n),f=v[r+4>>2],e=(u=cf(8020))+f|0,v[r+4>>2]=e,o=v[5316],a=v[r+8>>2],e=0|La[v[v[o>>2]+16>>2]](o,a,e+1|0,8610,140),v[r+8>>2]=e,Me(f+e|0,8020!=(0|a)?8020:e,u+1|0),Gf(r,n),v[i>>2]=v[i+20>>2],H(20976,17196,0|i),Oe(r);break n}f=i+12|0;f:{i:{if(r=v[i+52>>2])for(;;){if(se(r+4|0,n))break i;if(!(r=v[r+28>>2]))break}Ut(f);break f}zf(f,r+16|0)}sf(v[i+28>>2]+8|0,f),Oe(f)}}if(r=v[i+36>>2],n=v[i+32>>2],!(r>>>0<(e=v[n+4>>2])>>>0))break}if((0|c)==(0|(b=b+1|0)))break}Ri(i+48|0),V=i- -64|0}function tr(r,n,f,i,t){var u,o,a=0,c=0,b=0,k=0,s=0,h=0,d=0,y=0,m=0,g=0,F=0,A=w(0),T=w(0),$=0,I=0,C=0,P=0,E=0;if(V=u=V+-64|0,v[u+60>>2]=n,(0|(o=cf(i)))>0&&(d=47!=(0|(c=l[(i+o|0)-1|0]))&92!=(0|c)),v[u+56>>2]=0,vf(u+60|0,c=n+f|0,u+48|0))for($=r+4|0,I=r+20|0,C=1+(o+d|0)|0,m=u+44|0,g=u+36|0,s=12|(n=u+16|0),h=4|n,F=u+52|0;;){if((0|(a=v[u+52>>2]))!=(0|(f=v[u+48>>2])))if(v[u+56>>2])Ze((n=ht(112))+4|0,0,108),v[n>>2]=9048,Ut(n+8|0),v[n+108>>2]=0,v[n+100>>2]=0,v[n+104>>2]=0,v[n+96>>2]=8764,v[n+92>>2]=0,v[n+84>>2]=0,v[n+88>>2]=0,v[n+80>>2]=8764,v[u>>2]=n,v[n+4>>2]=v[u+56>>2],b=v[5316],n=a-f|0,f=Me(0|La[v[v[b>>2]+12>>2]](b,n+1|0,8610,308),f,n),e[n+f|0]=0,n=$t(u+4|0,f,1),sf(v[u>>2]+8|0,n),Oe(n),Vi(u+60|0,c,u+48|0),n=90,Wf(5008,f=v[u+48>>2],a=v[u+52>>2]-f|0)&&(n=0,Wf(5185,f,a)&&(n=Uu(f,F))),f=v[u>>2],v[f+76>>2]=n,e[f+72|0]=90==(0|n),dn(u+60|0,c,u+16|0),n=Uu(v[u+16>>2],h),v[v[u>>2]+20>>2]=n,n=Uu(v[u+24>>2],s),v[v[u>>2]+24>>2]=n,dn(u+60|0,c,u+16|0),n=Uu(v[u+16>>2],h),v[v[u>>2]+28>>2]=n,f=Uu(v[u+24>>2],s),n=v[u>>2],v[n+32>>2]=f,a=v[n+20>>2],k=v[u+56>>2],A=w(v[k+60>>2]),p[n+36>>2]=w(0|a)/A,b=v[n+24>>2],T=w(v[k+64>>2]),p[n+40>>2]=w(0|b)/T,l[n+72|0]?(b=b+v[n+28>>2]|0,f=f+a|0):(b=f+b|0,f=a+v[n+28>>2]|0),p[n+48>>2]=w(0|b)/T,p[n+44>>2]=w(0|f)/A,4==(0|dn(u+60|0,c,u+16|0))&&(v[u+4>>2]=0,Tn(n+80|0,4,u+4|0),n=Uu(v[u+16>>2],h),v[v[v[u>>2]+92>>2]>>2]=n,n=Uu(v[u+24>>2],s),v[v[v[u>>2]+92>>2]+4>>2]=n,n=Uu(v[u+32>>2],g),v[v[v[u>>2]+92>>2]+8>>2]=n,n=Uu(v[u+40>>2],m),f=v[u>>2],v[v[f+92>>2]+12>>2]=n,4==(0|dn(u+60|0,c,u+16|0))&&(v[u+4>>2]=0,Tn(f+96|0,4,u+4|0),n=Uu(v[u+16>>2],h),v[v[v[u>>2]+108>>2]>>2]=n,n=Uu(v[u+24>>2],s),v[v[v[u>>2]+108>>2]+4>>2]=n,n=Uu(v[u+32>>2],g),v[v[v[u>>2]+108>>2]+8>>2]=n,n=Uu(v[u+40>>2],m),v[v[v[u>>2]+108>>2]+12>>2]=n,dn(u+60|0,c,u+16|0))),n=Uu(v[u+16>>2],h),v[v[u>>2]+60>>2]=n,n=Uu(v[u+24>>2],s),v[v[u>>2]+64>>2]=n,dn(n=u+60|0,c,u+16|0),f=Uu(v[u+16>>2],h),p[v[u>>2]+52>>2]=0|f,f=Uu(v[u+24>>2],s),p[v[u>>2]+56>>2]=0|f,Vi(n,c,u+48|0),n=Uu(v[u+48>>2],F),v[v[u>>2]+68>>2]=n,_n(I,u);else{for(n=v[5316],a=a-f|0,n=Me(0|La[v[v[n>>2]+12>>2]](n,a+1|0,8610,308),f,a),e[n+a|0]=0,f=cf(n),a=v[5316],b=Me(0|La[v[v[a>>2]+12>>2]](a,f+C|0,8610,116),i,o),d&&(e[b+o|0]=47),Cn((b+o|0)+d|0,n),P=u,E=ii(f=ht(68),n=$t(u+4|0,n,1)),v[P+56>>2]=E,Oe(n),dn(u+60|0,c,u+16|0),n=Uu(v[u+16>>2],h),v[v[u+56>>2]+60>>2]=n,n=Uu(v[u+24>>2],s),a=v[u+56>>2],v[a+64>>2]=n,dn(u+60|0,c,u+16|0),f=v[u+16>>2],k=v[u+20>>2]-f|0,n=8;;){if(n){if(Wf(v[8944+((n=n-1|0)<<2)>>2],f,k))continue}else n=0;break}for(v[a+40>>2]=n,dn(u+60|0,c,u+16|0),f=v[u+16>>2],k=v[u+20>>2]-f|0,n=8;;){if(n){if(Wf(v[8976+((n=n-1|0)<<2)>>2],f,k))continue}else n=0;break}for(v[a+44>>2]=n,f=v[u+24>>2],k=v[u+28>>2]-f|0,n=8;;){if(n){if(Wf(v[8976+((n=n-1|0)<<2)>>2],f,k))continue}else n=0;break}v[a+48>>2]=n,Vi(u+60|0,c,u+48|0),v[a+52>>2]=1,v[a+56>>2]=1;r:if(Wf(5338,k=v[u+48>>2],y=v[u+52>>2]-k|0)){f=a+56|0,n=a+52|0;n:{f:{if(1==(0|y))switch(l[0|k]-120|0){case 0:break n;case 1:break f;default:break r}if(Wf(1056,k,y))break r;v[a+52>>2]=2}n=f}v[n>>2]=2}t?((n=v[r+36>>2])&&(f=$t(u+4|0,b,0),La[v[v[n>>2]+8>>2]](n,a,f),Oe(f)),n=v[5316],La[v[v[n>>2]+20>>2]](n,b,8610,156)):(n=$t(u+4|0,b,1),sf(v[u+56>>2]+28|0,n),Oe(n)),_n($,u+56|0)}else v[u+56>>2]=0;if(!vf(u+60|0,c,u+48|0))break}V=u- -64|0}function ur(r,n){var f=0,i=0,t=0,u=0,o=0,a=0,c=0,b=0;r:{n:{f:{i:{e:{if((0|(f=e[0|n]))<=90)switch(f-34|0){case 11:case 14:case 15:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:break n;case 0:break e;default:break r}if((0|f)<=109){if(91==(0|f))break i;if(102!=(0|f))break r;if(Wf(n+1|0,5186,4))break r;return v[r+8>>2]=0,n+5|0}if(123==(0|f))break f;if(116!=(0|f)){if(110!=(0|f))break r;if(Wf(n+1|0,4536,3))break r;return v[r+8>>2]=2,n+4|0}if(Wf(n+1|0,5009,3))break r;return v[r+20>>2]=1,v[r+8>>2]=1,n+4|0}return wr(r,n)}for(t=r,v[r+8>>2]=5,n=n+1|0;n=(r=n)+1|0,((f=l[0|r])-1&255)>>>0<32;);i:{e:{if(93!=(0|f)){for(Nf(f=Jt(32),0),v[t+4>>2]=f;n=r,r=r+1|0,(l[0|n]-1&255)>>>0<32;);if(!(n=ur(f,n)))break e;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);for(n=1;;){t:{if(v[t+12>>2]=n,44!=(0|(n=l[0|r]))){if(93!=(0|n))break t;n=r+1|0;break i}for(Nf(i=Jt(32),0),v[f>>2]=i;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(!(n=ur(i,r)))break e;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);n=v[t+12>>2]+1|0,f=i;continue}break}v[5326]=r,n=0}break i}n=0}return n}for(o=r,v[r+8>>2]=6,f=n+1|0;f=(r=f)+1|0,((n=l[0|r])-1&255)>>>0<32;);f:{i:{e:if(125!=(0|n)){for(Nf(i=Jt(32),0),v[o+4>>2]=i;n=r,r=r+1|0,(l[0|n]-1&255)>>>0<32;);if(r=0,!(f=wr(i,n)))break f;for(;f=(r=f)+1|0,(l[0|r]-1&255)>>>0<32;);if(v[i+28>>2]=v[i+16>>2],f=0,v[i+16>>2]=0,58!=l[0|r])break i;for(;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(n=ur(i,r)){for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);for(f=1;;){t:{if(v[o+12>>2]=f,44!=(0|(n=l[0|r]))){if(125!=(0|n))break t;r=r+1|0;break f}for(Nf(t=Jt(32),0),v[i>>2]=t;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(f=0,!(n=wr(t,r)))break e;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);if(v[t+28>>2]=v[t+16>>2],v[t+16>>2]=0,58!=l[0|r]){v[5326]=r,r=0;break f}for(;n=l[r+1|0],r=r+1|0,(n-1&255)>>>0<32;);if(!(n=ur(t,r)))break e;for(;n=(r=n)+1|0,(l[0|r]-1&255)>>>0<32;);f=v[o+12>>2]+1|0,i=t;continue}break}f=0,v[5326]=r}}r=f;break f}v[5326]=r,r=0}return r}if(i=r,t=n,a=l[0|n],((n=l[0|(f=(45==(0|a))+n|0)])-48&255)>>>0>9)r=f;else for(;u=10*u+ +((255&n)-48|0),n=l[f+1|0],f=r=f+1|0,(n-48&255)>>>0<10;);if(46==(255&n)){if(n=r+1|0,((f=l[r+1|0])-48&255)>>>0>9)r=n;else for(;c=10*c+ +((255&f)-48|0),o=o+1|0,f=l[n+1|0],n=r=n+1|0,(f-48&255)>>>0<10;);u+=c/(+(Oi(o)>>>0)+4294967296*+(Z>>>0)),n=l[0|r]}if(b=c=45==(0|a)?-u:u,101==(255&n|32)){if(n=(f=45==(0|(a=l[r+1|0]))|43==(0|a))?r+2|0:r+1|0,o=0,((f=l[(f?2:1)+r|0])-48&255)>>>0>9)r=n;else for(;o=(m(o,10)+(255&f)|0)-48|0,f=l[n+1|0],n=r=n+1|0,(f-48&255)>>>0<10;);b=c/(u=+(Oi(o)>>>0)+4294967296*+(Z>>>0)),45!=(0|a)&&(b=c*u)}return u=b,(0|r)==(0|t)?(v[5326]=t,r=0):(v[i+8>>2]=3,n=g(u)<2147483648?~~u:-2147483648,v[i+20>>2]=n,p[i+24>>2]=u),r}return v[5326]=n,0}function or(r,n){r|=0,n|=0;var f,i=0,t=0,u=0,o=0,a=0,c=w(0),b=0,k=0,s=0,h=0,d=w(0),y=0,m=0,g=0,F=w(0),A=0,T=0,$=0,I=w(0);if(V=f=V-16|0,l[r+88|0]){if(e[r+88|0]=0,i=v[r+80>>2])for(;t=v[i+12>>2],La[v[v[i>>2]+4>>2]](i),i=t,t;);if(v[r+80>>2]=0,v[r+84>>2]=0,T=v[r+44>>2])for(;;){if(t=v[v[r+52>>2]+(y<<2)>>2]){for(;i=t,t=v[t+24>>2];);for(;;){if(t=v[i+28>>2],3!=v[i+112>>2]||!t){b=0,V=o=V-16|0,m=v[i+16>>2],s=v[m+8>>2],u=v[i+28>>2],v[o+12>>2]=0,Tn(i+116|0,s,a=o+12|0),v[o+12>>2]=0,In((t=i)+132|0,s,a);r:if(!u|!l[u+37|0]){if(s)for(A=r+76|0;;){i=v[(h=b<<2)+v[m+16>>2]>>2],$=0|La[v[v[i>>2]+16>>2]](i),v[o+12>>2]=$;n:{if(a=v[r+80>>2])for(;;){if(v[a+4>>2]==(0|$)){a=0;break n}if(!(a=v[a+12>>2]))break}if(a=1,e[o+11|0]=1,Nn(A,o+12|0,o+11|0),u&&!Su(0|La[v[v[i>>2]+8>>2]](i),21132)&&!Su(0|La[v[v[i>>2]+8>>2]](i),21240)&&!Su(0|La[v[v[i>>2]+8>>2]](i),21252)&&Gi(v[(i=u)+16>>2],v[o+12>>2])){for(;;){if(a=3,!(i=v[i+28>>2]))break n;if(!Gi(v[i+16>>2],v[o+12>>2]))break}if(p[i+100>>2]>w(0)){if(v[h+v[t+128>>2]>>2]=4,v[h+v[t+144>>2]>>2]=i,s>>>0>(b=b+1|0)>>>0)continue;break r}}}if(v[h+v[t+128>>2]>>2]=a,!(s>>>0>(b=b+1|0)>>>0))break}}else{if(!s)break r;for(b=r+76|0,i=0;;){u=v[(h=i<<2)+v[m+16>>2]>>2],A=0|La[v[v[u>>2]+16>>2]](u),v[o+12>>2]=A;n:{if(a=v[r+80>>2])for(;;){if(u=2,(0|A)==v[a+4>>2])break n;if(!(a=v[a+12>>2]))break}e[o+11|0]=1,Nn(b,o+12|0,o+11|0),u=3}if(v[h+v[t+128>>2]>>2]=u,(0|s)==(0|(i=i+1|0)))break}}V=o+16|0,t=v[t+28>>2]}if(!(i=t))break}}if((0|T)==(0|(y=y+1|0)))break}}if(y=v[r+44>>2])for(s=r+56|0;;){if(!(!(t=v[v[r+52>>2]+(g<<2)>>2])|p[t+68>>2]>w(0))){i=g?v[t+112>>2]:1,d=p[t+92>>2],v[t+24>>2]?d=w(d*dr(r,t,n,i)):p[t+72>>2]>=p[t+84>>2]&&(d=v[t+20>>2]?d:w(0)),c=p[t+52>>2],I=p[t+60>>2];r:if(l[t+36|0]){if((F=w(p[t+56>>2]-c))==w(0))break r;c=w(c+Or(p[t+72>>2],F))}else c=(c=w(c+p[t+72>>2]))<(F=p[t+56>>2])?c:F;b=v[t+16>>2],o=v[b+8>>2];r:if(!(!g&d==w(1))&3!=(0|i)){if(h=t+148|0,(m=v[t+152>>2])||(v[f+12>>2]=0,An(h,o<<1,f+12|0)),o)for(k=0;a=v[(u=k<<2)+v[t+128>>2]>>2]?0:i,u=v[u+v[b+16>>2]>>2],Su(0|La[v[v[u>>2]+8>>2]](u),21452)?pr(u,n,c,d,a,h,k<<1,!m):Su(0|La[v[v[u>>2]+8>>2]](u),21132)?Fn(r,u,n,c,a,1):La[v[v[u>>2]+12>>2]](u,n,I,c,s,d,a,0),(0|o)!=(0|(k=k+1|0)););}else{if(!o)break r;for(k=0;u=v[v[b+16>>2]+(k<<2)>>2],Su(0|La[v[v[u>>2]+8>>2]](u),21132)?Fn(r,u,n,c,i,1):La[v[v[u>>2]+12>>2]](u,n,I,c,s,d,i,0),(0|o)!=(0|(k=k+1|0)););}qr(r,t,c),v[r+60>>2]=0,p[t+64>>2]=c,p[t+80>>2]=p[t+72>>2],k=1}if((0|y)==(0|(g=g+1|0)))break}if(i=v[r+100>>2],(0|(o=v[n+28>>2]))>0){for(a=i+1|0,i=0;t=v[v[n+36>>2]+(i<<2)>>2],(0|a)==v[t+64>>2]&&(u=ba(v[t+4>>2]),$e(t,u=v[u+4>>2]?Ui(n,v[v[t+4>>2]+4>>2],u):0)),(0|o)!=(0|(i=i+1|0)););i=v[r+100>>2]}return v[r+100>>2]=i+2,mr(v[r+72>>2]),V=f+16|0,0|k}function ar(r){var n=0,f=0,i=0,e=0,t=0,u=0,o=0,a=0,c=0;r:if(r|=0){t=(i=r-8|0)+(r=-8&(n=v[r-4>>2]))|0;n:if(!(1&n)){if(!(3&n))break r;if((i=i-(n=v[i>>2])|0)>>>0<d[5493])break r;r=r+n|0;f:{i:{if(v[5494]!=(0|i)){if(n>>>0<=255){if(e=n>>>3|0,(0|(n=v[i+12>>2]))==(0|(f=v[i+8>>2]))){a=21956,c=v[5489]&dt(e),v[a>>2]=c;break n}v[f+12>>2]=n,v[n+8>>2]=f;break n}if(u=v[i+24>>2],(0|i)!=(0|(n=v[i+12>>2]))){f=v[i+8>>2],v[f+12>>2]=n,v[n+8>>2]=f;break f}if(!(f=v[(e=i+20|0)>>2])){if(!(f=v[i+16>>2]))break i;e=i+16|0}for(;o=e,(f=v[(e=(n=f)+20|0)>>2])||(e=n+16|0,f=v[n+16>>2]););v[o>>2]=0;break f}if(3&~(n=v[t+4>>2]))break n;return v[5491]=r,v[t+4>>2]=-2&n,v[i+4>>2]=1|r,void(v[t>>2]=r)}n=0}if(u){f=v[i+28>>2];f:{if(v[(e=22260+(f<<2)|0)>>2]==(0|i)){if(v[e>>2]=n,n)break f;a=21960,c=v[5490]&dt(f),v[a>>2]=c;break n}if(v[u+(v[u+16>>2]==(0|i)?16:20)>>2]=n,!n)break n}v[n+24>>2]=u,(f=v[i+16>>2])&&(v[n+16>>2]=f,v[f+24>>2]=n),(f=v[i+20>>2])&&(v[n+20>>2]=f,v[f+24>>2]=n)}}if(!(i>>>0>=t>>>0)&&1&(n=v[t+4>>2])){n:{f:{i:{e:{if(!(2&n)){if(v[5495]==(0|t)){if(v[5495]=i,r=v[5492]+r|0,v[5492]=r,v[i+4>>2]=1|r,v[5494]!=(0|i))break r;return v[5491]=0,void(v[5494]=0)}if(v[5494]==(0|t))return v[5494]=i,r=v[5491]+r|0,v[5491]=r,v[i+4>>2]=1|r,void(v[r+i>>2]=r);if(r=(-8&n)+r|0,n>>>0<=255){if(e=n>>>3|0,(0|(n=v[t+12>>2]))==(0|(f=v[t+8>>2]))){a=21956,c=v[5489]&dt(e),v[a>>2]=c;break f}v[f+12>>2]=n,v[n+8>>2]=f;break f}if(u=v[t+24>>2],(0|t)!=(0|(n=v[t+12>>2]))){f=v[t+8>>2],v[f+12>>2]=n,v[n+8>>2]=f;break i}if(!(f=v[(e=t+20|0)>>2])){if(!(f=v[t+16>>2]))break e;e=t+16|0}for(;o=e,(f=v[(e=(n=f)+20|0)>>2])||(e=n+16|0,f=v[n+16>>2]););v[o>>2]=0;break i}v[t+4>>2]=-2&n,v[i+4>>2]=1|r,v[r+i>>2]=r;break n}n=0}if(u){f=v[t+28>>2];i:{if(v[(e=22260+(f<<2)|0)>>2]==(0|t)){if(v[e>>2]=n,n)break i;a=21960,c=v[5490]&dt(f),v[a>>2]=c;break f}if(v[u+(v[u+16>>2]==(0|t)?16:20)>>2]=n,!n)break f}v[n+24>>2]=u,(f=v[t+16>>2])&&(v[n+16>>2]=f,v[f+24>>2]=n),(f=v[t+20>>2])&&(v[n+20>>2]=f,v[f+24>>2]=n)}}if(v[i+4>>2]=1|r,v[r+i>>2]=r,v[5494]==(0|i))return void(v[5491]=r)}if(r>>>0<=255)return n=21996+(-8&r)|0,(f=v[5489])&(r=1<<(r>>>3))?r=v[n+8>>2]:(v[5489]=r|f,r=n),v[n+8>>2]=i,v[r+12>>2]=i,v[i+12>>2]=n,void(v[i+8>>2]=r);f=31,r>>>0<=16777215&&(f=62+((r>>>38-(n=F(r>>>8|0))&1)-(n<<1)|0)|0),v[i+28>>2]=f,v[i+16>>2]=0,v[i+20>>2]=0,n=22260+(f<<2)|0;n:{f:{if((e=v[5490])&(o=1<<f)){for(f=r<<(31!=(0|f)?25-(f>>>1|0):0),n=v[n>>2];;){if(e=n,(-8&v[n+4>>2])==(0|r))break f;if(n=f>>>29|0,f<<=1,!(n=v[16+(o=e+(4&n)|0)>>2]))break}v[o+16>>2]=i,v[i+24>>2]=e}else v[5490]=e|o,v[n>>2]=i,v[i+24>>2]=n;v[i+12>>2]=i,v[i+8>>2]=i;break n}r=v[e+8>>2],v[r+12>>2]=i,v[e+8>>2]=i,v[i+24>>2]=0,v[i+12>>2]=e,v[i+8>>2]=r}r=v[5497]-1|0,v[5497]=r||-1}}}function cr(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t|=0,u=w(u),o=w(o);var a,c,b,k,s=w(0),h=w(0),d=w(0),y=w(0),m=w(0),F=w(0),A=0,$=w(0),I=w(0),C=w(0),P=w(0),E=w(0),O=w(0),R=0,S=w(0),W=w(0),G=w(0),U=w(0),j=w(0),H=w(0);if(A=v[r+12>>2],o!=w(0)){if(l[r+88|0]||Tr(r),l[n+88|0]||Tr(n),P=p[n+60>>2],G=p[r+64>>2],U=p[r+60>>2],h=p[n+72>>2],$=p[A+96>>2],y=p[A+104>>2],I=p[A+92>>2],C=p[v[n+4>>2]+24>>2],d=p[A+108>>2],R=(O=p[r+72>>2])<w(0),a=(s=p[r+76>>2])<w(0),m=R?w(-O):O,j=a?w(-s):s,(S=(s=w(m-j))<w(0)?w(-s):s)<=w(9999999747378752e-20)?(W=p[n+64>>2],s=w(p[r+112>>2]+w(w(p[r+104>>2]*P)+w(W*p[r+108>>2]))),F=w(p[r+100>>2]+w(w(p[r+92>>2]*P)+w(W*p[r+96>>2])))):(s=w(w(p[r+104>>2]*P)+p[r+112>>2]),F=w(w(p[r+92>>2]*P)+p[r+100>>2])),E=w(F-p[A+100>>2]),s=w(s-p[A+112>>2]),F=w(w(1)/w(w(I*d)-w(y*$))),H=w(w(w(w(d*E)-w($*s))*F)-U),s=w(w(w(w(s*I)-w(y*E))*F)-G),+(s=w(T(w(w(H*H)+w(s*s)))))<1e-4)return Ar(r,f,i,0,t,0,o),void kr(n,P,W,w(0),p[n+72>>2],p[n+76>>2],p[n+80>>2],p[n+84>>2]);k=0-(c=R?-1:1)|0,E=(b=h<w(0))?w(-h):h,C=w(E*C),h=w(f-p[A+100>>2]),i=w(i-p[A+112>>2]),f=w(w(w(w(h*d)-w($*i))*F)-U),h=w(w(w(w(i*I)-w(y*h))*F)-G),d=w(w(f*f)+w(h*h)),u!=w(0)&&(i=w(w(w(m*w(E+w(1)))*w(.5))*u),$=w(T(d)),(u=w(i+w(w($-s)-w(C*m))))>w(0)&&(d=i,i=(i=w(u/w(i+i)))>w(1)?w(0):w(i+w(-1)),i=w(w(-w(u-w(d*w(w(1)-w(i*i)))))/$),f=w(w(i*f)+f),h=w(w(i*h)+h),d=w(w(f*f)+w(h*h)))),A=a?k:c,E=w(R?180:0),i=w(m*C),S<=w(9999999747378752e-20)?(u=w(-1),(m=w(w(w(d-w(s*s))-w(i*i))/w(i*w(s+s))))<w(-1)||(u=(R=m>w(1))?w(1):m,!R|!t||(u=w(1),O=w(O*w(w(w(w(w(T(d))/w(i+s))+w(-1))*o)+w(1))))),s=w(w(i*u)+s),u=w(nn(u)*w(0|e)),i=w(i*Hr(u)),d=Rr(w(w(h*s)-w(i*f)),w(w(f*s)+w(h*i)))):($=w(0),S=Rr(h,f),C=w(j*C),f=w(C*C),u=w(w(f*w(-2))*s),h=w(i*i),y=w(f-h),I=w(w(w(f*w(s*s))+w(h*d))-w(h*f)),(F=w(w(u*u)+w(w(y*w(-4))*I)))>=w(0)&&(F=w(T(F)),u=w(w(u+(u<w(0)?w(-F):F))*w(-.5)),y=w(u/y),u=w(I/u),u=w(g(y))<w(g(u))?y:u,(y=w(u*u))<=d)?(f=w(w(T(w(d-y)))*w(0|e)),d=w(S-Rr(f,u)),u=Rr(w(f/j),w(w(u-s)/m))):(y=w(i+s),I=w(y*y),u=w(s-i),m=w(u*u),F=w(3.1415927410125732),!(!((f=w(w(s*w(-i))/w(h-f)))>=w(-1))|!(f<=w(1)))&&(h=Hr(f=nn(f)),s=w(w(i*Ur(f))+s),h=w(C*h),m>(i=w(w(s*s)+w(h*h)))&&(F=f,m=i,$=h,u=s),i>I)||(f=w(0),s=y,i=I,h=w(0)),t=w(w(m+i)*w(.5))>=d,i=w(0|e),d=w(S-Rr(w((t?$:h)*i),t?u:s)),u=w((t?F:f)*i))),f=w(b?180:0),i=w(0|A),h=w(Rr(W,P)*i),m=p[r+68>>2],(s=w(w(w(w(d-h)*w(57.2957763671875))+E)-m))>w(180)?s=w(s+w(-360)):s<w(-180)&&(s=w(s+w(360))),kr(r,U,G,w(w(s*o)+m),O,p[r+76>>2],w(0),w(0)),s=w(w(u+h)*w(57.2957763671875)),u=p[n+80>>2],i=w(w(w(s-u)*i)+f),f=p[n+68>>2],(s=w(i-f))>w(180)?s=w(s+w(-360)):s<w(-180)&&(s=w(s+w(360))),kr(n,P,W,w(w(s*o)+f),p[n+72>>2],p[n+76>>2],u,p[n+84>>2])}else De(n)}function br(r,n){var f,i=0,e=0,t=0,u=0,o=0,a=0,c=0;f=r+n|0;r:{n:if(!(1&(i=v[r+4>>2]))){if(!(3&i))break r;n=(i=v[r>>2])+n|0;f:{i:{e:{if((0|(r=r-i|0))!=v[5494]){if(i>>>0<=255){if((0|(e=v[r+8>>2]))!=(0|(t=v[r+12>>2])))break e;a=21956,c=v[5489]&dt(i>>>3|0),v[a>>2]=c;break n}if(u=v[r+24>>2],(0|(i=v[r+12>>2]))!=(0|r)){e=v[r+8>>2],v[e+12>>2]=i,v[i+8>>2]=e;break f}if(!(e=v[(t=r+20|0)>>2])){if(!(e=v[r+16>>2]))break i;t=r+16|0}for(;o=t,(e=v[(t=(i=e)+20|0)>>2])||(t=i+16|0,e=v[i+16>>2]););v[o>>2]=0;break f}if(3&~(i=v[f+4>>2]))break n;return v[5491]=n,v[f+4>>2]=-2&i,v[r+4>>2]=1|n,void(v[f>>2]=n)}v[e+12>>2]=t,v[t+8>>2]=e;break n}i=0}if(u){e=v[r+28>>2];f:{if(v[(t=22260+(e<<2)|0)>>2]==(0|r)){if(v[t>>2]=i,i)break f;a=21960,c=v[5490]&dt(e),v[a>>2]=c;break n}if(v[u+(v[u+16>>2]==(0|r)?16:20)>>2]=i,!i)break n}v[i+24>>2]=u,(e=v[r+16>>2])&&(v[i+16>>2]=e,v[e+24>>2]=i),(e=v[r+20>>2])&&(v[i+20>>2]=e,v[e+24>>2]=i)}}n:{f:{i:{e:{if(!(2&(i=v[f+4>>2]))){if(v[5495]==(0|f)){if(v[5495]=r,n=v[5492]+n|0,v[5492]=n,v[r+4>>2]=1|n,v[5494]!=(0|r))break r;return v[5491]=0,void(v[5494]=0)}if(v[5494]==(0|f))return v[5494]=r,n=v[5491]+n|0,v[5491]=n,v[r+4>>2]=1|n,void(v[r+n>>2]=n);if(n=(-8&i)+n|0,i>>>0<=255){if(t=i>>>3|0,(0|(i=v[f+12>>2]))==(0|(e=v[f+8>>2]))){a=21956,c=v[5489]&dt(t),v[a>>2]=c;break f}v[e+12>>2]=i,v[i+8>>2]=e;break f}if(u=v[f+24>>2],(0|f)!=(0|(i=v[f+12>>2]))){e=v[f+8>>2],v[e+12>>2]=i,v[i+8>>2]=e;break i}if(!(e=v[(t=f+20|0)>>2])){if(!(e=v[f+16>>2]))break e;t=f+16|0}for(;o=t,(e=v[(t=(i=e)+20|0)>>2])||(t=i+16|0,e=v[i+16>>2]););v[o>>2]=0;break i}v[f+4>>2]=-2&i,v[r+4>>2]=1|n,v[r+n>>2]=n;break n}i=0}if(u){e=v[f+28>>2];i:{if(v[(t=22260+(e<<2)|0)>>2]==(0|f)){if(v[t>>2]=i,i)break i;a=21960,c=v[5490]&dt(e),v[a>>2]=c;break f}if(v[u+(v[u+16>>2]==(0|f)?16:20)>>2]=i,!i)break f}v[i+24>>2]=u,(e=v[f+16>>2])&&(v[i+16>>2]=e,v[e+24>>2]=i),(e=v[f+20>>2])&&(v[i+20>>2]=e,v[e+24>>2]=i)}}if(v[r+4>>2]=1|n,v[r+n>>2]=n,v[5494]==(0|r))return void(v[5491]=n)}if(n>>>0<=255)return i=21996+(-8&n)|0,(e=v[5489])&(n=1<<(n>>>3))?n=v[i+8>>2]:(v[5489]=n|e,n=i),v[i+8>>2]=r,v[n+12>>2]=r,v[r+12>>2]=i,void(v[r+8>>2]=n);e=31,n>>>0<=16777215&&(e=62+((n>>>38-(i=F(n>>>8|0))&1)-(i<<1)|0)|0),v[r+28>>2]=e,v[r+16>>2]=0,v[r+20>>2]=0,i=22260+(e<<2)|0;n:{if((t=v[5490])&(o=1<<e)){for(e=n<<(31!=(0|e)?25-(e>>>1|0):0),i=v[i>>2];;){if(t=i,(-8&v[i+4>>2])==(0|n))break n;if(i=e>>>29|0,e<<=1,!(i=v[16+(o=t+(4&i)|0)>>2]))break}v[o+16>>2]=r,v[r+24>>2]=t}else v[5490]=t|o,v[i>>2]=r,v[r+24>>2]=i;return v[r+12>>2]=r,void(v[r+8>>2]=r)}n=v[t+8>>2],v[n+12>>2]=r,v[t+8>>2]=r,v[r+24>>2]=0,v[r+12>>2]=t,v[r+8>>2]=n}}function kr(r,n,f,i,t,u,o,a){r|=0,n=w(n),f=w(f),i=w(i),t=w(t),u=w(u),o=w(o),a=w(a);var c,b=w(0),k=w(0),s=w(0),l=w(0),h=0,d=w(0),y=w(0),m=w(0),F=0,A=w(0);if(e[r+88|0]=1,p[r+84>>2]=a,p[r+80>>2]=o,p[r+76>>2]=u,p[r+72>>2]=t,p[r+68>>2]=i,p[r+64>>2]=f,p[r+60>>2]=n,c=v[r+8>>2],d=p[c+168>>2],y=p[c+164>>2],!(h=v[r+12>>2]))return o=w(w(i+o)*w(.01745329238474369)),F=r,A=w(w(Hr(o)*t)*d),p[F+104>>2]=A,F=r,A=w(w(Ur(o)*t)*y),p[F+92>>2]=A,i=w(w(w(i+w(90))+a)*w(.01745329238474369)),F=r,A=w(w(Hr(i)*u)*d),p[F+108>>2]=A,F=r,A=w(w(Ur(i)*u)*y),p[F+96>>2]=A,p[r+100>>2]=w(n*y)+p[c+172>>2],void(p[r+112>>2]=w(f*d)+p[c+176>>2]);b=p[h+104>>2],s=p[h+108>>2],k=p[h+92>>2],l=p[h+96>>2],p[r+100>>2]=w(w(k*n)+w(l*f))+p[h+100>>2],p[r+112>>2]=w(w(b*n)+w(s*f))+p[h+112>>2];r:{n:{f:{i:{e:{switch(0|(h=v[v[r+4>>2]+56>>2])){case 3:case 4:break n;case 2:break f;case 1:break i;case 0:break e}i=p[r+108>>2],n=p[r+104>>2],u=p[r+96>>2],t=p[r+92>>2];break r}return f=Hr(n=w(w(i+o)*w(.01745329238474369))),n=w(Ur(n)*t),f=w(f*t),p[r+104>>2]=w(b*n)+w(f*s),p[r+92>>2]=w(k*n)+w(f*l),f=Hr(n=w(w(w(i+w(90))+a)*w(.01745329238474369))),n=w(Ur(n)*u),f=w(f*u),p[r+108>>2]=w(b*n)+w(f*s),void(p[r+96>>2]=w(k*n)+w(f*l))}f=w(w(i+o)*w(.01745329238474369)),n=w(Hr(f)*t),t=w(Ur(f)*t),f=w(w(w(i+w(90))+a)*w(.01745329238474369)),i=w(Hr(f)*u),u=w(Ur(f)*u);break r}n=w(i+o),(f=w(w(k*k)+w(b*b)))>w(9999999747378752e-20)?(f=w(w(g(w(w(k*s)-w(b*l))))/f),s=w(k*f),l=w(b*f),f=w(Rr(b,k)*w(57.2957763671875))):(b=w(0),k=w(0),f=w(w(Rr(s,l)*w(-57.2957763671875))+w(90))),o=Hr(n=w(w(n-f)*w(.01745329238474369))),m=w(Ur(n)*t),t=w(o*t),n=w(w(b*m)+w(s*t)),t=w(w(k*m)-w(t*l)),i=Hr(f=w(w(w(w(i+a)-f)+w(90))*w(.01745329238474369))),f=w(Ur(f)*u),u=w(i*u),i=w(w(b*f)+w(s*u)),u=w(w(k*f)-w(u*l));break r}n=Hr(f=w(i*w(.01745329238474369))),i=Ur(f),m=f=w(w(w(k*i)+w(n*l))/y),n=w(w(w(b*i)+w(n*s))/d),i=(f=w(T(w(w(f*f)+w(n*n)))))>w(9999999747378752e-21)?w(w(1)/f):f,f=w(m*i),n=w(n*i),i=w(T(w(w(f*f)+w(n*n)))),b=3==(0|h)&&y<w(0)^d<w(0)^w(w(k*s)-w(b*l))<w(0)?w(-i):i,i=Hr(k=w(Rr(n,f)+w(1.5707963705062866))),s=Hr(a=w(w(a+w(90))*w(.01745329238474369))),a=w(Ur(a)*u),u=w(s*u),s=w(b*i),i=w(w(n*a)+w(u*s)),l=Hr(o=w(o*w(.01745329238474369))),o=w(Ur(o)*t),t=w(l*t),n=w(w(n*o)+w(t*s)),m=w(f*a),a=w(b*Ur(k)),u=w(m+w(u*a)),t=w(w(f*o)+w(t*a))}p[r+108>>2]=d*i,p[r+104>>2]=d*n,p[r+96>>2]=y*u,p[r+92>>2]=y*t}function sr(r,n){var f,i=0,e=0,t=0,u=0,o=0,a=0,c=0;if(V=f=V-16|0,v[r+4>>2]=n,v[r+120>>2]=9292,v[r+116>>2]=0,v[r+108>>2]=0,v[r+112>>2]=0,v[r+104>>2]=10328,v[r+100>>2]=0,v[r+92>>2]=0,v[r+96>>2]=0,v[r+88>>2]=10312,v[r+84>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+72>>2]=10296,v[r+68>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,v[r+56>>2]=10280,v[r+52>>2]=0,v[r+44>>2]=0,v[r+48>>2]=0,v[r+40>>2]=10264,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+24>>2]=10264,v[r+20>>2]=0,v[r+12>>2]=0,v[r+16>>2]=0,v[r+8>>2]=9292,v[r>>2]=10248,v[r+132>>2]=0,v[r+136>>2]=0,v[r+124>>2]=0,v[r+128>>2]=0,v[r+140>>2]=9404,v[r+144>>2]=1065353216,v[r+148>>2]=1065353216,v[r+152>>2]=1065353216,v[r+156>>2]=1065353216,mi(r+140|0),v[r+176>>2]=0,v[r+168>>2]=1065353216,v[r+172>>2]=0,v[r+160>>2]=0,v[r+164>>2]=1065353216,u=r+8|0,i=v[r+4>>2],Oa(u,v[(n=i)+20>>2]),v[n+20>>2])for(n=0;e=v[v[i+28>>2]+(n<<2)>>2],(t=v[e+20>>2])?(t=v[v[r+20>>2]+(v[t+4>>2]<<2)>>2],a=f,c=If(ht(120),e,r,t),v[a+12>>2]=c,_n(t+16|0,f+12|0)):(a=f,c=If(ht(120),e,r,0),v[a+12>>2]=c),_n(u,f+12|0),(n=n+1|0)>>>0<d[i+20>>2];);if(e=r+24|0,i=v[r+4>>2],Oa(e,v[(n=i)+36>>2]),Oa(t=r+40|0,v[n+36>>2]),v[n+36>>2])for(n=0;u=v[v[i+44>>2]+(n<<2)>>2],o=v[v[r+20>>2]+(v[v[u+20>>2]+4>>2]<<2)>>2],a=f,c=En(ht(88),u,o),v[a+12>>2]=c,_n(e,u=f+12|0),_n(t,u),(n=n+1|0)>>>0<d[i+36>>2];);if(e=r+56|0,i=v[r+4>>2],Oa(e,v[(n=i)+104>>2]),v[n+104>>2])for(n=0;t=v[v[i+112>>2]+(n<<2)>>2],a=f,c=kn(ht(48),t,r),v[a+12>>2]=c,_n(e,f+12|0),(n=n+1|0)>>>0<d[i+104>>2];);if(e=r+72|0,i=v[r+4>>2],Oa(e,v[(n=i)+120>>2]),v[n+120>>2])for(n=0;t=v[v[i+128>>2]+(n<<2)>>2],a=f,c=sn(ht(48),t,r),v[a+12>>2]=c,_n(e,f+12|0),(n=n+1|0)>>>0<d[i+120>>2];);if(e=r+88|0,i=v[r+4>>2],Oa(e,v[(n=i)+136>>2]),v[n+136>>2])for(n=0;t=v[v[i+144>>2]+(n<<2)>>2],a=f,c=Ir(ht(144),t,r),v[a+12>>2]=c,_n(e,f+12|0),(n=n+1|0)>>>0<d[i+136>>2];);return nr(r),V=f+16|0,r}function vr(r,n,f,i){var e,t=0,u=0,o=0,a=0,c=0,b=0,k=w(0),s=0,l=0;if(V=e=V-32|0,d[r+8>>2]<=n>>>0){v[e+24>>2]=0,v[e+16>>2]=0,v[e+20>>2]=0,v[e+12>>2]=10880,a=v[r+8>>2],t=n+1|0,v[r+8>>2]=t,(u=v[r+12>>2])>>>0<t>>>0&&(k=w(w(t>>>0)*w(1.75)),o=w(g(k))<w(2147483648)?~~k:-2147483648,t=(t=u?o:t)>>>0<=8?8:t,v[r+12>>2]=t,o=v[5316],s=r,l=0|La[v[v[o>>2]+16>>2]](o,v[r+16>>2],t<<4,8610,89),v[s+16>>2]=l,t=v[r+8>>2]),o=e+12|0;r:if(t>>>0<=a>>>0){if(t>>>0>=a>>>0)break r;for(;u=v[r+16>>2]+(t<<4)|0,La[v[v[u>>2]>>2]](u),(0|a)!=(0|(t=t+1|0)););}else for(;;){if(t=v[r+16>>2]+(a<<4)|0,v[t>>2]=10880,v[t+4>>2]=v[o+4>>2],u=v[o+8>>2],v[t+12>>2]=0,v[t+8>>2]=u,u&&(c=v[5316],s=t,l=0|La[v[v[c>>2]+12>>2]](c,m(u,20),8610,221),v[s+12>>2]=l,v[t+4>>2]))for(u=0;c=(b=m(u,20))+v[t+12>>2]|0,b=b+v[o+12>>2]|0,v[c>>2]=v[b>>2],zf(c+4|0,b+4|0),v[c+16>>2]=v[b+16>>2],(u=u+1|0)>>>0<d[t+4>>2];);if(!((a=a+1|0)>>>0<d[r+8>>2]))break}gf(o)}r:{n:{f:{if(t=v[r+16>>2]+(n<<4)|0,v[t+4>>2])for(r=0;;){if(se(4+(v[t+12>>2]+m(r,20)|0)|0,f))break f;if(!((r=r+1|0)>>>0<d[t+4>>2]))break}Zo(i);break n}if(Zo(i),!((0|r)<0)){r=m(r,20),Ve(v[16+(r+v[t+12>>2]|0)>>2]),v[16+(r+v[t+12>>2]|0)>>2]=i;break r}}v[e+12>>2]=n,a=zf(e+16|0,f),v[e+28>>2]=i,r=e+12|0,V=n=V-32|0,(0|(f=v[t+4>>2]))!=v[t+8>>2]?(v[t+4>>2]=f+1,f=v[t+12>>2]+m(f,20)|0,v[f>>2]=v[r>>2],zf(f+4|0,r+4|0),v[f+16>>2]=v[r+16>>2]):(v[n+12>>2]=v[r>>2],f=zf(n+16|0,r+4|0),v[n+28>>2]=v[r+16>>2],k=w(w(d[t+4>>2])*w(1.75)),r=(r=w(g(k))<w(2147483648)?~~k:-2147483648)>>>0<=8?8:r,v[t+8>>2]=r,i=v[5316],r=0|La[v[v[i>>2]+16>>2]](i,v[t+12>>2],m(r,20),8610,117),v[t+12>>2]=r,i=v[t+4>>2],v[t+4>>2]=i+1,r=r+m(i,20)|0,v[r>>2]=v[n+12>>2],zf(r+4|0,f),v[r+16>>2]=v[n+28>>2],Oe(f)),V=n+32|0,Oe(a)}V=e+32|0}function lr(r,n,f,i,e,t,u){var o,a=0,c=w(0),b=w(0),k=0,s=0,l=w(0),h=w(0),d=w(0),y=0,g=0,F=w(0),A=w(0),T=0,$=w(0),I=0,C=0;o=m(i>>>1|0,u)+t|0,k=v[n+8>>2];r:{n:{f:{if(v[r+24>>2]){if(f)break f;i=0;break n}if(t>>>0>=o>>>0)break r;for(c=p[k+108>>2],b=p[k+104>>2],l=p[k+96>>2],h=p[k+92>>2],d=p[k+112>>2],$=p[k+100>>2],r=v[12+(v[n+76>>2]?n+72|0:r+36|0)>>2];n=(t<<2)+e|0,F=p[(i=r+(f<<2)|0)>>2],A=p[i+4>>2],p[n+4>>2]=d+w(w(F*b)+w(c*A)),p[n>>2]=$+w(w(F*h)+w(l*A)),f=f+2|0,o>>>0>(t=t+u|0)>>>0;);break r}for(y=v[r+32>>2],i=0;i=(g=v[y+(a<<2)>>2])+i|0,a=1+(a+g|0)|0,(s=s+2|0)>>>0<f>>>0;);}if(k=v[k+8>>2],v[n+76>>2]){if(!(t>>>0>=o>>>0))for(s=m(i,3),f=i<<1,g=v[n+84>>2],I=v[r+48>>2],n=v[r+32>>2];;){if(i=a+1|0,(0|(r=v[n+(a<<2)>>2]))<=0)c=w(0),a=i,b=w(0);else for(a=(0|(r=r+i|0))>(0|(a=a+2|0))?r:a,C=v[k+20>>2],b=w(0),c=w(0);r=v[(v[n+(i<<2)>>2]<<2)+C>>2],T=g+(f<<2)|0,l=w(p[(y=(s<<2)+I|0)>>2]+p[T>>2]),h=w(p[y+4>>2]+p[T+4>>2]),d=p[y+8>>2],b=w(w(w(p[r+112>>2]+w(w(l*p[r+104>>2])+w(h*p[r+108>>2])))*d)+b),c=w(w(w(p[r+100>>2]+w(w(l*p[r+92>>2])+w(h*p[r+96>>2])))*d)+c),f=f+2|0,s=s+3|0,(0|a)!=(0|(i=i+1|0)););if(p[(r=(t<<2)+e|0)>>2]=c,p[r+4>>2]=b,!(o>>>0>(t=t+u|0)>>>0))break}}else{if(t>>>0>=o>>>0)break r;for(s=m(i,3),y=v[r+48>>2],n=v[r+32>>2];;){if(i=a+1|0,(0|(r=v[n+(a<<2)>>2]))<=0)c=w(0),b=w(0),a=i;else for(a=(0|(r=r+i|0))>(0|(f=a+2|0))?r:f,g=v[k+20>>2],b=w(0),c=w(0);r=v[g+(v[n+(i<<2)>>2]<<2)>>2],l=p[(f=y+(s<<2)|0)>>2],h=p[f+4>>2],d=p[f+8>>2],b=w(w(w(p[r+112>>2]+w(w(l*p[r+104>>2])+w(h*p[r+108>>2])))*d)+b),c=w(w(w(p[r+100>>2]+w(w(l*p[r+92>>2])+w(h*p[r+96>>2])))*d)+c),s=s+3|0,(0|a)!=(0|(i=i+1|0)););if(p[(r=(t<<2)+e|0)>>2]=c,p[r+4>>2]=b,!(o>>>0>(t=t+u|0)>>>0))break}}}}function hr(r){r|=0;var n,f=w(0),i=0,e=w(0),t=0,u=w(0),o=w(0),a=0,c=0,b=w(0),k=w(0),s=w(0),l=0;V=n=V-16|0,(0|(c=v[r+108>>2]))!=(0|(t=v[r+124>>2]))&&(v[n+12>>2]=0,An(r+104|0,t,n+12|0),c=v[r+124>>2]),f=p[r+184>>2],e=p[r+180>>2];r:{n:{if(270!=(0|(t=v[r+232>>2]))){if(180!=(0|t)){if(90!=(0|t))break n;if((0|c)<=0)break r;for(u=p[r+84>>2],o=w(u/w(p[r+192>>2]-f)),b=p[r+92>>2],u=w(f-w(w(w(b-p[r+76>>2])-u)/o)),k=p[r+88>>2],f=w(k/w(p[r+188>>2]-e)),s=e,e=p[r+96>>2],k=w(s-w(w(w(e-p[r+80>>2])-k)/f)),o=w(b/o),f=w(e/f),t=v[r+116>>2],a=v[r+132>>2],r=0;l=4|(i=r<<2),p[t+i>>2]=w(p[a+l>>2]*f)+k,p[t+l>>2]=w(w(w(1)-p[i+a>>2])*o)+u,(0|c)>(0|(r=r+2|0)););break r}if((0|c)<=0)break r;for(u=p[r+84>>2],o=w(u/w(p[r+188>>2]-e)),s=e,e=p[r+92>>2],u=w(s-w(w(w(e-p[r+76>>2])-u)/o)),b=w(p[r+88>>2]/w(p[r+192>>2]-f)),k=w(p[r+96>>2]/b),e=w(e/o),f=w(f-w(p[r+80>>2]/b)),t=v[r+116>>2],a=v[r+132>>2],r=0;p[(i=r<<2)+t>>2]=w(w(w(1)-p[i+a>>2])*e)+u,p[(i|=4)+t>>2]=w(w(w(1)-p[i+a>>2])*k)+f,(0|c)>(0|(r=r+2|0)););break r}if((0|c)<=0)break r;for(o=w(p[r+88>>2]/w(p[r+192>>2]-f)),u=w(p[r+92>>2]/o),b=w(p[r+84>>2]/w(p[r+188>>2]-e)),k=w(p[r+96>>2]/b),f=w(f-w(p[r+76>>2]/o)),e=w(e-w(p[r+80>>2]/b)),t=v[r+116>>2],a=v[r+132>>2],r=0;l=4|(i=r<<2),p[t+i>>2]=w(w(w(1)-p[a+l>>2])*k)+e,p[t+l>>2]=w(p[i+a>>2]*u)+f,(0|c)>(0|(r=r+2|0)););break r}if(!((0|c)<=0))for(u=p[r+88>>2],o=w(u/w(p[r+192>>2]-f)),s=f,f=p[r+96>>2],u=w(s-w(w(w(f-p[r+80>>2])-u)/o)),f=w(f/o),o=w(p[r+84>>2]/w(p[r+188>>2]-e)),b=w(p[r+92>>2]/o),e=w(e-w(p[r+76>>2]/o)),t=v[r+116>>2],a=v[r+132>>2],r=0;p[(i=r<<2)+t>>2]=w(p[i+a>>2]*b)+e,p[(i|=4)+t>>2]=w(p[i+a>>2]*f)+u,(0|c)>(0|(r=r+2|0)););}V=n+16|0}function dr(r,n,f,i){var e,t,u,o,a,c=w(0),b=0,k=w(0),s=0,h=0,d=w(0),y=0,m=w(0),g=w(0),F=w(0),A=w(0),T=0,$=0,I=w(0);V=u=V-16|0,e=v[n+24>>2],v[e+24>>2]&&dr(r,e,f,i),(c=p[n+100>>2])!=w(0)?(d=(c=w(p[n+96>>2]/c))>w(1)?w(1):c,s=1,1!=(0|i)&&(s=v[e+112>>2])):(d=w(1),s=1!=(0|i)?i:0),i=r+56|0,b=p[e+40>>2]>d,k=p[e+52>>2],g=p[e+60>>2],F=p[e+48>>2],I=p[e+44>>2];r:if(l[e+36|0]){if((c=w(p[e+56>>2]-k))==w(0))break r;k=w(k+Or(p[e+72>>2],c))}else k=(c=w(k+p[e+72>>2]))<(k=p[e+56>>2])?c:k;a=b?i:0,m=w(p[e+92>>2]*p[n+104>>2]),A=w(w(w(1)-d)*m),o=v[e+16>>2],t=v[o+8>>2];r:if(3!=(0|s)){if(T=e+148|0,($=v[e+152>>2])||(v[u+12>>2]=0,An(T,t<<1,u+12|0)),v[e+108>>2]=0,t)for(;;){b=v[(h=y<<2)+v[o+16>>2]>>2],i=0,c=A;n:{f:{switch(v[h+v[e+128>>2]>>2]){case 0:if(i=s,d<F)break f;if(!Su(0|La[v[v[b>>2]+8>>2]](b),21240))break f;break n;case 2:i=s;case 3:c=m;break f;case 1:break f}h=v[h+v[e+144>>2]>>2],c=w(w(1)-w(p[h+96>>2]/p[h+100>>2])),c=w(m*(c<w(0)?w(0):c))}p[e+108>>2]=c+p[e+108>>2],Su(0|La[v[v[b>>2]+8>>2]](b),21452)?pr(b,f,k,c,i,T,y<<1,!$):Su(0|La[v[v[b>>2]+8>>2]](b),21132)?Fn(r,b,f,k,i,d<I):(h=d<F?1^Su(0|La[v[v[b>>2]+8>>2]](b),21240)|!!(0|i):1,La[v[v[b>>2]+12>>2]](b,f,g,k,a,c,i,h))}if((0|(y=y+1|0))==(0|t))break}}else{if(!t)break r;for(i=0;s=v[v[o+16>>2]+(i<<2)>>2],La[v[v[s>>2]+12>>2]](s,f,g,k,a,A,3,1),(0|t)!=(0|(i=i+1|0)););}return p[n+100>>2]>w(0)&&qr(r,e,k),v[r+60>>2]=0,p[e+64>>2]=k,p[e+80>>2]=p[e+72>>2],V=u+16|0,d}function pr(r,n,f,i,e,t,u,o){var a,c=w(0),b=0,k=w(0),s=0,l=w(0);if(o&&(v[v[t+12>>2]+(u<<2)>>2]=0),i!=w(1)){a=v[v[n+20>>2]+(v[r+20>>2]<<2)>>2];r:if(0|La[v[v[a>>2]+16>>2]](a)){if(s=v[r+36>>2],p[s>>2]>f){n:switch(0|e){case 0:return void(p[a+40>>2]=p[v[a+4>>2]+36>>2]);case 1:break n;default:break r}l=p[a+40>>2],f=p[v[a+4>>2]+36>>2]}else n=e?a+40|0:v[a+4>>2]+36|0,l=p[n>>2],n=s+(v[r+28>>2]<<2)|0,p[n-8>>2]<=f?f=w(p[v[a+4>>2]+36>>2]+p[n-4>>2]):(n=(s=(e=Lf(r+24|0,f,2))<<2)+v[r+36>>2]|0,k=p[n-4>>2],c=f,f=p[n>>2],f=pn(r,(e>>1)-1|0,w(w(1)-w(w(c-f)/w(p[n-8>>2]-f)))),c=w(p[4+(v[r+36>>2]+s|0)>>2]-k),b=16384.499999999996-+w(c/w(360)),r=g(b)<2147483648?~~b:-2147483648,f=w(p[v[a+4>>2]+36>>2]+w(w(w(c-w(0|m(16384-r|0,360)))*f)+k)),b=16384.499999999996-+w(f/w(360)),r=g(b)<2147483648?~~b:-2147483648,f=w(f-w(0|m(16384-r|0,360))));f=w(f-l),b=+w(f/w(-360))+16384.499999999996,r=g(b)<2147483648?~~b:-2147483648,c=w(0),(k=w(f-w(0|m(16384-r|0,360))))!=w(0)?(f=k,o||(r=v[t+12>>2]+(u<<2)|0,c=p[r>>2],f=p[r+4>>2]),r=c>=w(0),n=k>w(0),!(w(g(f))<=w(90))|hu(f)==hu(k)||(w(g(c))>w(180)&&(c=w(w(hu(c)*w(360))+c)),r=n),f=w(w(k+c)-Or(c,w(360))),(0|r)!=(0|n)&&(f=w(w(hu(c)*w(360))+f)),r=v[t+12>>2],p[r+(u<<2)>>2]=f):(r=v[t+12>>2],f=p[r+(u<<2)>>2]),p[4+((u<<2)+r|0)>>2]=k,f=w(w(f*i)+l),b=+w(f/w(-360))+16384.499999999996,r=g(b)<2147483648?~~b:-2147483648,p[a+40>>2]=f-w(0|m(16384-r|0,360))}}else La[v[v[r>>2]+12>>2]](r,n,w(0),f,0,w(1),e,0)}function yr(r,n,f,i){var e,t=0,u=0,o=0,a=0,c=w(0),b=0,k=0,s=0;if(V=e=V+-64|0,v[e+60>>2]=0,v[e+52>>2]=0,v[e+56>>2]=0,v[e+48>>2]=8796,v[f+52>>2]=i,n=Un(n,2901),(u=v[n+12>>2])&&(v[e+56>>2]=u,t=v[5316],k=e,s=0|La[v[v[t>>2]+16>>2]](t,0,u<<2,8610,105),v[k+60>>2]=s),v[e+12>>2]=0,An(e+48|0,u,e+12|0),n=v[n+4>>2])for(t=0,o=v[e+60>>2];p[o+(t<<2)>>2]=p[n+24>>2],t=t+1|0,n=v[n>>2];);if((0|i)!=(0|u)){if(v[e+44>>2]=0,v[e+36>>2]=0,v[e+40>>2]=0,v[e+28>>2]=0,v[e+20>>2]=0,v[e+24>>2]=0,v[e+32>>2]=8796,v[e+16>>2]=10816,v[e+12>>2]=10800,i&&(v[e+24>>2]=m(i,3),n=v[5316],n=0|La[v[v[n>>2]+16>>2]](n,0,m(i,12),8610,105),v[e+40>>2]=m(i,9),v[e+28>>2]=n,n=v[5316],k=e,s=0|La[v[v[n>>2]+16>>2]](n,0,m(i,36),8610,105),v[k+44>>2]=s),n=e+32|0,i=e+16|0,u)for(t=0;;){if(c=p[v[e+60>>2]+(t<<2)>>2],o=w(g(c))<w(2147483648)?~~c:-2147483648,v[e+8>>2]=o,Ln(i,e+8|0),(t=t+1|0)>>>0<(b=t+(o<<2)|0)>>>0)for(;c=p[(o=t<<2)+v[e+60>>2]>>2],a=w(g(c))<w(2147483648)?~~c:-2147483648,v[e+8>>2]=a,Ln(i,a=e+8|0),p[e+8>>2]=p[4+(o+v[e+60>>2]|0)>>2]*p[r+24>>2],Mn(n,a),p[e+8>>2]=p[8+(o+v[e+60>>2]|0)>>2]*p[r+24>>2],Mn(n,a),Mn(n,12+(o+v[e+60>>2]|0)|0),b>>>0>(t=t+4|0)>>>0;);if(!(t>>>0<u>>>0))break}v[f+40>>2]=0,te(f+36|0,n),v[f+24>>2]=0,ee(f+20|0,i),v[e+12>>2]=10800,Lo(n),go(i)}else{if(!(!i|p[r+24>>2]==w(1)))for(n=0,t=v[e+60>>2];p[(u=t+(n<<2)|0)>>2]=p[r+24>>2]*p[u>>2],(0|i)!=(0|(n=n+1|0)););v[f+40>>2]=0,te(f+36|0,e+48|0)}Lo(e+48|0),V=e- -64|0}function mr(r){var n=0,f=0,i=0,t=0,u=0,o=0;if(!l[r+28|0]){if(e[r+28|0]=1,v[r+8>>2]){for(f=v[r+20>>2];;){t=v[r+16>>2]+(u<<4)|0,n=v[t+8>>2];r:{n:{f:{i:{e:switch(0|(i=v[t+4>>2])){case 5:break n;case 4:break f;case 2:break i;case 0:case 1:case 3:break e;default:break r}if((o=v[n+168>>2])?La[v[v[o>>2]+8>>2]](o,f,i,n,0):La[v[n+164>>2]](f,i,n,0),!(i=v[f+96>>2])){La[v[f+92>>2]](f,v[t+4>>2],n,0);break r}La[v[v[i>>2]+8>>2]](i,f,v[t+4>>2],n,0);break r}(i=v[n+168>>2])?La[v[v[i>>2]+8>>2]](i,f,2,n,0):La[v[n+164>>2]](f,2,n,0),(i=v[f+96>>2])?La[v[v[i>>2]+8>>2]](i,f,v[t+4>>2],n,0):La[v[f+92>>2]](f,v[t+4>>2],n,0)}(t=v[n+168>>2])?La[v[v[t>>2]+8>>2]](t,f,4,n,0):La[v[n+164>>2]](f,4,n,0),(t=v[f+96>>2])?La[v[v[t>>2]+8>>2]](t,f,4,n,0):La[v[f+92>>2]](f,4,n,0),v[n+16>>2]=0,v[n+20>>2]=0,v[n+24>>2]=0,v[n+28>>2]=0,(t=v[n+12>>2])&&(i=v[n+8>>2])&&La[0|t](i),v[n+168>>2]=0,v[n+164>>2]=9,v[n+8>>2]=0,v[n+12>>2]=0,v[n+152>>2]=0,v[n+136>>2]=0,v[n+120>>2]=0,Tf(v[r+24>>2],n);break r}(i=v[n+168>>2])?La[v[v[i>>2]+8>>2]](i,f,5,n,v[t+12>>2]):La[v[n+164>>2]](f,5,n,v[t+12>>2]),(i=v[f+96>>2])?La[v[v[i>>2]+8>>2]](i,f,v[t+4>>2],n,v[t+12>>2]):La[v[f+92>>2]](f,v[t+4>>2],n,v[t+12>>2])}if(!((n=v[r+8>>2])>>>0>(u=u+1|0)>>>0))break}if(n)for(f=0;n=v[r+16>>2]+(~f+n<<4)|0,La[v[v[n>>2]>>2]](n),(n=v[r+8>>2])>>>0>(f=f+1|0)>>>0;);}e[r+28|0]=0,v[r+8>>2]=0}}function wr(r,n){var f,i=0,t=0,u=0,o=0,a=0;V=f=V-16|0;r:if(34!=l[0|n])v[5326]=n,u=0;else{for(i=n=n+1|0;!(!(o=l[0|i])|34==(0|o));)t=t+1|0,i=(92==(0|o)?2:1)+i|0;if(o=v[5316],u=0,!(o=0|La[v[v[o>>2]+8>>2]](o,t+1|0,8610,265)))break r;for(i=o;;){n:{f:if(92==(0|(u=l[0|n]))){u=n+1|0;i:{e:{t:{u:{o:{a:{switch((a=e[n+1|0])-110|0){case 1:case 2:case 3:case 5:break e;case 7:break t;case 6:break u;case 4:break o;case 0:break a}c:switch(a-98|0){case 0:e[0|i]=8;break i;case 4:break c;default:break e}e[0|i]=12;break i}e[0|i]=10;break i}e[0|i]=13;break i}e[0|i]=9;break i}if(Vn(n+2|0,f+12|0),u=n+5|0,!(t=v[f+12>>2]))break f;if(56320==(0|(a=-1024&t)))break f;t:{u:{o:{if(55296!=(0|a)){if(n=1,t>>>0<128)break t;if(n=2,t>>>0<2048)break u;if(n=3,t>>>0<65536)break o}else{if(92!=l[n+6|0]|117!=l[n+7|0])break f;if(Vn(n+8|0,f+8|0),u=n+11|0,(n=v[f+8>>2])-57344>>>0<4294966272)break f;t=65536+(1023&n|t<<10&1047552)|0,v[f+12>>2]=t}e[i+3|0]=63&t|128,t=v[f+12>>2]>>>6|0,v[f+12>>2]=t,n=4}e[i+2|0]=63&t|128,t=v[f+12>>2]>>>6|0,v[f+12>>2]=t}e[i+1|0]=63&t|128,t=v[f+12>>2]>>>6|0,v[f+12>>2]=t}e[0|i]=l[n+9756|0]|t,i=n+i|0;break f}e[0|i]=a}i=i+1|0}else{if(!u|34==(0|u))break n;e[0|i]=u,i=i+1|0,u=n}n=u+1|0;continue}break}e[0|i]=0,u=l[0|n],v[r+8>>2]=4,v[r+16>>2]=o,u=(34==(0|u))+n|0}return V=f+16|0,u}function gr(r,n,f,i){var t,u=0,o=0,a=0,c=0,b=0,k=0,s=0,h=0,d=0,p=0,y=0,m=0,w=0,g=0,F=0;V=t=V-16|0;r:{n:{f:{if((0|f)<=36){if(u=l[0|r])break f;o=r;break n}v[5488]=28,i=0;break r}o=r;f:{for(;;){if(!_o(u<<24>>24))break f;if(u=l[o+1|0],o=o+1|0,!u)break}break n}f:switch((u&=255)-43|0){case 0:case 2:break f;default:break n}s=45==(0|u)?-1:0,o=o+1|0}n:if(16!=(16|f)|48!=l[0|o])d=f||10;else{if(w=1,88==(223&l[o+1|0])){o=o+2|0,d=16;break n}o=o+1|0,d=f||8}for(f=0;u=-48,(((a=e[0|o])-48&255)>>>0<10||(u=-87,(a-97&255)>>>0<26||(u=-55,!((a-65&255)>>>0>25))))&&!((0|(a=u+a|0))>=(0|d));)c=xf(h,0,0,0),b=Z,F=xf(k,0,d,0),u=Z,p=xf(0,0,k,0),y=Z,c=c+(y=(u=u+p|0)>>>0<p>>>0?y+1|0:y)|0,p=xf(h,0,d,0)+u|0,m=Z,m=(u=u>>>0>p>>>0?m+1|0:m)+c|0,v[t+8>>2]=m,b=c>>>0<y>>>0?b+1|0:b,v[t+12>>2]=u>>>0>m>>>0?b+1|0:b,v[t>>2]=F,v[t+4>>2]=p,u=1,v[t+8>>2]|v[t+12>>2]||(b=xf(k,h,d,0),-1==(0|(c=Z))&~a>>>0<b>>>0||(h=(k=a+b|0)>>>0<a>>>0?c+1|0:c,w=1,u=f)),o=o+1|0,f=u;n&&(v[n>>2]=w?o:r);n:{if(f)v[5488]=68,s=(r=1&i)?0:s,k=i,h=0;else{if(!h&i>>>0>k>>>0)break n;r=1&i}if(!(r|s)){v[5488]=68,i=(r=i)-1|0,g=0-!r|0;break r}if(!(!h&i>>>0>=k>>>0)){v[5488]=68;break r}}i=(r=s^k)-s|0,g=((n=s>>31)^h)-((r>>>0<s>>>0)+n|0)|0}return V=t+16|0,Z=g,i}function Fr(r,n){var f=0,i=0,e=0,c=0,s=0,l=w(0),h=0,d=0;b(n),c=Pu(e=t(2));r:{n:{f:{b(r);i:{if((f=t(2))-2139095040>>>0>=2164260864){if(c)break i;break n}if(!c)break f}if(l=w(1),1065353216==(0|f))break r;if(!(s=e<<1))break r;if(!(s>>>0<4278190081&(f<<=1)>>>0<=4278190080))return w(r+n);if(2130706432==(0|f))break r;return w(f>>>0>2130706431^(0|e)>=0?0:n*n)}if(Pu(f)){if(l=w(r*r),(0|f)<0&&(l=1==(0|ie(e))?w(-l):l),(0|e)>=0)break r;return Rt(w(w(1)/l))}if((0|f)<0){if(!(e=ie(e)))return r=w(r-r),w(r/r);s=(1==(0|e))<<16,f&=2147483647}f>>>0>8388607||(f=(2147483647&(b(w(r*w(8388608))),t(2)))-192937984|0)}if(c=(e=f-1060306944|0)>>>15&240,u(2,f-(-8388608&e)|0),a(+(i=((h=(i=+k()*y[c+20672>>3]-1)*i)*h*(.288457581109214*i-.36092606229713164)+((.480898481472577*i-.7213474675006291)*h+(1.4426950408774342*i+(y[c+20680>>3]+ +(e>>23)))))*+n)),f=0|t(1),t(0),!(1079967744==(0|(f&=2147450880))|f>>>0<1079967744)){if(i>127.99999995700433)return Mu(s,w(15845632502852868e13));if(i<=-150)return Mu(s,w(2524354896707238e-44))}i-=(d=(h=y[2203])+i)-h,i=(y[2204]*i+y[2205])*i*i+y[2206]*i+1,a(+d),t(1),f=s+(c=0|t(0))|0,e=c=v[(s=17368+((31&c)<<3)|0)>>2],f=v[s+4>>2]+(f<<15)|0,u(0,0|e),u(1,0|(e>>>0<e>>>0?f+1:f)),l=w(i*+o())}return l}function Ar(r,n,f,i,e,t,u){r|=0,n=w(n),f=w(f),i|=0,e|=0,t|=0,u=w(u);var o=w(0),a=w(0),c=0,b=w(0),k=w(0),s=w(0),h=w(0),d=w(0),y=w(0);h=p[r+68>>2],s=w(-p[r+80>>2]),c=v[r+12>>2],k=p[c+108>>2],o=p[c+104>>2],b=p[c+96>>2],a=p[c+92>>2],l[r+88|0]||Tr(r),h=w(s-h);r:{switch(v[v[r+4>>2]+56>>2]-1|0){case 0:c=r+112|0,o=f,a=w(n-p[r+100>>2]);break r;case 1:b=w(w(g(w(w(a*k)-w(o*b))))/w(w(a*a)+w(o*o))),k=w(a*b),b=w(b*w(-o)),h=w(w(Rr(o,a)*w(57.2957763671875))+h)}s=w(f-p[c+112>>2]),d=w(n-p[c+100>>2]),y=w(w(s*a)-w(o*d)),a=w(w(a*k)-w(o*b)),o=w(y/a),c=r- -64|0,a=w(w(w(w(d*k)-w(b*s))/a)-p[r+60>>2])}s=w(o-p[c>>2]),o=w(w(Rr(s,a)*w(57.2957763671875))+h),(o=(k=p[r+72>>2])<w(0)?w(o+w(180)):o)>w(180)?o=w(o+w(-360)):o<w(-180)&&(o=w(o+w(360))),b=p[r+76>>2];r:if(i|e){c=i,n=(i=v[v[r+4>>2]+56>>2]-3>>>0<2)?w(n-p[r+100>>2]):a,a=w(n*n),n=i?w(f-p[r+112>>2]):s;n:{if(!((f=w(T(w(a+w(n*n)))))<(n=w(k*p[v[r+4>>2]+24>>2]))&&c)){if(!e|!(n>w(9999999747378752e-20)))break r;if(n<f)break n;break r}if(!(n>w(9999999747378752e-20)))break r}n=w(w(w(w(f/n)+w(-1))*u)+w(1)),k=w(k*n),b=w(b*(t?n:w(1)))}kr(r,p[r+60>>2],p[r+64>>2],w(w(o*u)+p[r+68>>2]),k,b,p[r+80>>2],p[r+84>>2])}function Tr(r){var n,f=w(0),i=w(0),t=w(0),u=w(0),o=w(0),a=w(0),c=w(0),b=w(0),k=w(0),s=0,l=w(0);return e[r+88|0]=1,(n=v[r+12>>2])?(f=p[n+112>>2],o=p[n+100>>2],i=p[n+108>>2],u=p[n+92>>2],c=p[n+104>>2],t=p[n+96>>2],v[r+80>>2]=0,a=w(p[r+112>>2]-f),f=w(w(1)/w(w(u*i)-w(c*t))),o=w(p[r+100>>2]-o),p[r+64>>2]=w(w(u*a)*f)-w(f*w(c*o)),p[r+60>>2]=w(w(i*o)*f)-w(f*w(t*a)),i=w(i*f),o=p[r+92>>2],a=w(-f),k=w(t*a),b=p[r+104>>2],t=w(w(i*o)+w(k*b)),f=w(u*f),a=w(c*a),u=w(w(f*b)+w(a*o)),c=w(T(w(w(t*t)+w(u*u)))),p[r+72>>2]=c,o=p[r+108>>2],b=a,a=p[r+96>>2],f=w(w(f*o)+w(b*a)),i=w(w(i*a)+w(k*o)),c>w(9999999747378752e-20)?(o=w(w(t*f)-w(u*i)),p[r+76>>2]=o/c,s=r,l=w(Rr(w(w(t*i)+w(u*f)),o)*w(57.2957763671875)),p[s+84>>2]=l,s=r,l=w(Rr(u,t)*w(57.2957763671875)),void(p[s+68>>2]=l)):(v[r+84>>2]=0,v[r+72>>2]=0,p[r+76>>2]=T(w(w(i*i)+w(f*f))),s=r,l=w(w(Rr(f,i)*w(-57.2957763671875))+w(90)),void(p[s+68>>2]=l))):(p[r+60>>2]=p[r+100>>2],p[r+64>>2]=p[r+112>>2],f=p[r+104>>2],i=p[r+92>>2],v[r+80>>2]=0,p[r+72>>2]=T(w(w(i*i)+w(f*f))),s=r,l=w(Rr(f,i)*w(57.2957763671875)),p[s+68>>2]=l,t=p[r+96>>2],u=p[r+108>>2],p[r+76>>2]=T(w(w(t*t)+w(u*u))),s=r,l=w(Rr(w(w(i*t)+w(f*u)),w(w(i*u)-w(f*t)))*w(57.2957763671875)),void(p[s+84>>2]=l))}function $r(r){var n,f,i=0,e=0,t=0,u=0,o=0,a=0,c=0,b=0;if(V=n=V-32|0,v[n+28>>2]=0,v[n+20>>2]=0,v[n+24>>2]=0,v[n+16>>2]=17112,v[n+12>>2]=0,v[n+4>>2]=0,v[n+8>>2]=0,v[n>>2]=17112,v[r+12>>2]&&(re(n+16|0,r+8|0),v[r+12>>2]=0),v[r+28>>2]&&(re(n,r+24|0),v[r+28>>2]=0),i=v[n+20>>2])for(a=v[n+28>>2];e=a+m(u,12)|0,t=v[e+8>>2],o=v[e+4>>2],v[5485]=v[e>>2],v[5242]=o,v[5486]=t,(e=v[r+68>>2])&&(v[5484]=e,q()),(0|i)!=(0|(u=u+1|0)););if(f=v[n+4>>2])for(u=0,c=v[n+12>>2];;){if(i=m(u,12)+c|0,e=v[i>>2],o=v[i+4>>2],b=v[i+8>>2],V=a=V-16|0,v[a+12>>2]=e,v[e+8>>2]){t=0;r:if(i=v[r+76>>2]){n:{for(;;){if((0|e)==v[i+4>>2])break n;if(!(i=v[i+12>>2]))break}t=0;break r}t=v[i+8>>2]}v[5242]=o,v[5484]=t,v[5485]=e,v[5486]=b,N(),4==(0|o)&&(i=r+72|0,(t=v[e+12>>2])&&(o=v[e+8>>2])&&La[0|t](o),v[e+8>>2]=0,v[e+12>>2]=0,pf(i,a+12|0))}if(V=a+16|0,(0|f)==(0|(u=u+1|0)))break}xu(n),xu(n+16|0),V=n+32|0}function Ir(r,n,f){var i,t,u,o=w(0),a=0,c=0;if(V=t=V-16|0,i=ra(r),v[i+8>>2]=9292,v[i+4>>2]=n,v[i>>2]=9924,r=0,v[i+20>>2]=0,v[i+12>>2]=0,v[i+16>>2]=0,a=i,c=Tu(f,la(v[n+40>>2])),v[a+24>>2]=c,p[i+28>>2]=p[n+60>>2],p[i+32>>2]=p[n+64>>2],p[i+36>>2]=p[n+68>>2],o=p[n+72>>2],v[i+124>>2]=8796,v[i+120>>2]=0,v[i+112>>2]=0,v[i+116>>2]=0,v[i+108>>2]=8796,v[i+104>>2]=0,v[i+96>>2]=0,v[i+100>>2]=0,v[i+92>>2]=8796,v[i+88>>2]=0,v[i+80>>2]=0,v[i+84>>2]=0,v[i+76>>2]=8796,v[i+72>>2]=0,v[(n=i- -64|0)>>2]=0,v[n+4>>2]=0,v[i+60>>2]=8796,v[i+56>>2]=0,v[i+48>>2]=0,v[i+52>>2]=0,v[i+44>>2]=8796,p[i+40>>2]=o,e[i+133|0]=0,e[i+134|0]=0,e[i+135|0]=0,e[i+136|0]=0,e[i+137|0]=0,e[i+138|0]=0,e[i+139|0]=0,e[i+140|0]=0,v[i+128>>2]=0,v[i+132>>2]=0,Oa(u=i+8|0,v[v[i+4>>2]+28>>2]),n=v[i+4>>2],v[n+28>>2])for(;a=t,c=Eu(f,la(v[v[n+36>>2]+(r<<2)>>2])),v[a+12>>2]=c,_n(u,t+12|0),r=r+1|0,n=v[i+4>>2],r>>>0<d[n+28>>2];);return v[t+8>>2]=0,An(i+124|0,10,t+8|0),V=t+16|0,i}function Cr(r,n){r|=0,n=w(n);var f,i=0,e=0,t=w(0),u=w(0),o=0,a=w(0),c=0,b=w(0);if(f=v[r+44>>2])for(n=w(p[r+104>>2]*n);;){c=v[r+52>>2]+(o<<2)|0;r:if(i=v[c>>2]){if(p[i+60>>2]=p[i+64>>2],u=p[i+80>>2],p[i+76>>2]=u,a=p[i+88>>2],t=w(n*a),(b=p[i+68>>2])>w(0)){if(t=w(b-t),p[i+68>>2]=t,t>w(0))break r;v[i+68>>2]=0,t=w(-t)}n:{if(e=v[i+20>>2]){if(!((u=w(u-p[e+68>>2]))>=w(0)))break n;if(v[e+68>>2]=0,p[e+72>>2]=(a!=w(0)?w(w(n+w(u/a))*p[e+88>>2]):w(0))+p[e+72>>2],p[i+72>>2]=t+p[i+72>>2],Rn(r,o,e,1),!(i=v[e+24>>2]))break r;for(;p[e+96>>2]=n+p[e+96>>2],e=i,i=v[i+24>>2];);break r}if(!(v[i+24>>2]|!(u>=p[i+84>>2]))){v[c>>2]=0,rt(v[r+72>>2],i),Wi(r,i);break r}}if(v[i+24>>2]&&Pn(r,i,n)&&(e=v[i+24>>2],v[i+24>>2]=0,e))for(v[e+28>>2]=0;rt(v[r+72>>2],e),e=v[e+24>>2];);p[i+72>>2]=t+p[i+72>>2]}if((0|f)==(0|(o=o+1|0)))break}mr(v[r+72>>2])}function Pr(r,n,f,i){var e,t,o=0,a=0,c=0,b=0,s=w(0),h=0,d=0,y=0,g=0,F=0;if(V=e=V-16|0,s=p[r+36>>2],r=i<<1,v[f+52>>2]=r,o=v[n+4>>2],v[n+4>>2]=o+1,t=f+36|0,l[0|o]){if(Oa(t,m(i,18)),Oa(o=f+20|0,m(i,6)),!((0|i)<=0))for(;;){if(h=zn(n,1),v[e+12>>2]=h,Ln(o,e+12|0),d=0,(0|h)>0)for(;g=e,F=zn(n,1),v[g+12>>2]=F,Ln(o,f=e+12|0),r=v[n+4>>2],v[n+4>>2]=r+1,a=l[0|r],v[n+4>>2]=r+2,c=l[r+1|0],v[n+4>>2]=r+3,b=l[r+2|0],v[n+4>>2]=r+4,p[e+12>>2]=s*(u(2,l[r+3|0]|(b|c<<8|a<<16)<<8),k()),Mn(t,f),r=v[n+4>>2],v[n+4>>2]=r+1,a=l[0|r],v[n+4>>2]=r+2,c=l[r+1|0],v[n+4>>2]=r+3,b=l[r+2|0],v[n+4>>2]=r+4,p[e+12>>2]=s*(u(2,l[r+3|0]|(b|c<<8|a<<16)<<8),k()),Mn(t,f),r=v[n+4>>2],v[n+4>>2]=r+1,a=l[0|r],v[n+4>>2]=r+2,c=l[r+1|0],v[n+4>>2]=r+3,b=l[r+2|0],v[n+4>>2]=r+4,v[e+12>>2]=l[r+3|0]|(b|c<<8|a<<16)<<8,Mn(t,f),(0|(d=d+1|0))!=(0|h););if((0|(y=y+1|0))==(0|i))break}}else Yr(n,r,s,t);V=e+16|0}function Er(r){return v[r+208>>2]=8796,v[r+204>>2]=0,v[r+196>>2]=0,v[r+200>>2]=0,v[r+192>>2]=8796,v[r+188>>2]=0,v[r+180>>2]=0,v[r+184>>2]=0,v[r+176>>2]=9884,v[r+172>>2]=0,v[r+164>>2]=0,v[r+168>>2]=0,v[r+160>>2]=8796,v[r+156>>2]=0,v[r+148>>2]=0,v[r+152>>2]=0,v[r+144>>2]=8796,v[r+140>>2]=0,v[r+132>>2]=0,v[r+136>>2]=0,v[r+128>>2]=8796,v[r+124>>2]=0,v[r+116>>2]=0,v[r+120>>2]=0,v[r+112>>2]=10504,v[r+108>>2]=10552,v[r+104>>2]=0,v[r+96>>2]=0,v[r+100>>2]=0,v[r+92>>2]=10488,v[r+88>>2]=10536,v[r+84>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+72>>2]=8764,v[r+68>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,v[r+56>>2]=10520,v[r+52>>2]=0,v[r+44>>2]=0,v[r+48>>2]=0,v[r+40>>2]=8764,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+24>>2]=10504,v[r+20>>2]=0,v[r+12>>2]=0,v[r+16>>2]=0,v[r+8>>2]=10488,v[r+4>>2]=11128,v[r>>2]=10472,v[r+220>>2]=0,v[r+224>>2]=0,v[r+212>>2]=0,v[r+216>>2]=0,Oa(r+144|0,128),Oa(r+160|0,128),Ea(r+176|0,128),Oa(r+192|0,128),r}function Or(r,n){var f=0,i=0,e=0,o=0,a=0,c=0;if(b(n),!((f=(o=t(2))<<1)&&(b(r),255!=(0|(e=(c=t(2))>>>23&255))&&(2147483647&(b(n),t(2)))>>>0<2139095041)))return r=w(r*n),w(r/r);if((i=c<<1)>>>0<=f>>>0)return(0|i)==(0|f)?w(r*w(0)):r;if(a=o>>>23&255,e)i=8388607&c|8388608;else{if(e=0,(0|(i=c<<9))>=0)for(;e=e-1|0,(0|(i<<=1))>=0;);i=c<<1-e}if(a)f=8388607&o|8388608;else{if(a=0,(0|(f=o<<9))>=0)for(;a=a-1|0,(0|(f<<=1))>=0;);f=o<<1-a}if(o=f,(0|e)>(0|a)){for(;;){if(!((0|(f=i-o|0))<0||(i=f)))return w(r*w(0));if(i<<=1,!((0|a)<(0|(e=e-1|0))))break}e=a}if(!((0|(f=i-o|0))<0||(i=f)))return w(r*w(0));if(i>>>0>8388607)f=i;else for(;e=e-1|0,o=i>>>0<4194304,i=f=i<<1,o;);return u(2,-2147483648&c|((0|e)>0?f-8388608|e<<23:f>>>1-e)),k()}function Rr(r,n){var f,i,e=0,o=0,a=w(0);if(!((2147483647&(b(r),t(2)))>>>0<2139095041&(2147483647&(b(n),t(2)))>>>0<=2139095040))return w(r+n);if(b(n),1065353216==(0|(e=t(2))))return Wr(r);i=e>>>30&2,b(r),f=i|(o=t(2))>>>31;r:{n:{f:{if(!(o&=2147483647)){i:switch(f-2|0){case 0:return w(3.1415927410125732);case 1:break i;default:break f}return w(-3.1415927410125732)}if(2139095040!=(0|(e&=2147483647))){if(!e|!(2139095040!=(0|o)&o>>>0<=e+218103808>>>0))break r;switch(i&&(a=w(0),e>>>0>o+218103808>>>0)||(a=Wr(w(g(w(r/n))))),r=a,0|f){case 1:return w(-r);case 2:return w(w(3.1415927410125732)-w(r+w(8.742277657347586e-8)));case 0:break f}return w(w(r+w(8.742277657347586e-8))+w(-3.1415927410125732))}if(2139095040==(0|o))break n;r=p[17764+(f<<2)>>2]}return r}return p[17748+(f<<2)>>2]}return u(2,-2147483648&(b(r),t(2))|1070141403),k()}function Sr(r){var n,f=0,i=0;if(V=n=V-48|0,f=v[r+76>>2])for(;i=v[f+12>>2],La[v[v[f>>2]+4>>2]](f),f=i;);if(v[r+44>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+28>>2]=0,v[r+12>>2]=0,(f=v[r+56>>2])&&La[v[v[f>>2]+4>>2]](f),(f=v[r+52>>2])&&La[v[v[f>>2]+4>>2]](f),(f=v[r+48>>2])&&La[v[v[f>>2]+4>>2]](f),(f=v[r+40>>2])&&La[v[v[f>>2]+4>>2]](f),(f=v[r+64>>2])&&ar(Do(f)),v[r+128>>2]&&(f=v[r+124>>2],v[n+36>>2]=0,v[n+40>>2]=0,v[n+28>>2]=0,v[n+32>>2]=0,e[n+24|0]=0,v[n+36>>2]=f,v[n+16>>2]=17160,v[n+44>>2]=n+16,e[n+12|0]=1,f))for(;e[n+12|0]=0,v[n+44>>2]=f,!(i=v[f+12>>2])|!l[f+8|0]||(f=v[f+16>>2],La[v[v[i>>2]+4>>2]](i),f&&La[v[v[f>>2]+4>>2]](f)),e[n+12|0]=1,f=v[v[n+44>>2]+20>>2];);return Xu(r+120|0),qu(r+104|0),ro(r+72|0),xu(r+24|0),xu(r+8|0),V=n+48|0,r}function Wr(r){var n,f,i=0,e=w(0),o=w(0),a=w(0);if(b(r),(n=2147483647&(f=t(2)))>>>0>=1283457024)return(2147483647&(b(r),t(2)))>>>0>2139095040?r:(u(2,-2147483648&(b(r),t(2))|1070141402),k());r:{n:{if(n>>>0<=1054867455){if(i=-1,n>>>0>=964689920)break n;break r}if(r=w(g(r)),n>>>0<=1066926079){if(n>>>0<=1060110335){r=w(w(w(r+r)+w(-1))/w(r+w(2))),i=0;break n}r=w(w(r+w(-1))/w(r+w(1))),i=1}else n>>>0<=1075576831?(r=w(w(r+w(-1.5))/w(w(r*w(1.5))+w(1))),i=2):(r=w(w(-1)/r),i=3)}if(o=w(r*r),e=w(o*o),a=w(e*w(w(e*w(-.106480173766613))+w(-.19999158382415771))),e=w(o*w(w(e*w(w(e*w(.06168760731816292))+w(.14253635704517365)))+w(.333333283662796))),n>>>0<=1054867455)return w(r-w(r*w(a+e)));r=w(p[17792+(i<<=2)>>2]-w(w(w(r*w(a+e))-p[i+17808>>2])-r)),r=(0|f)<0?w(-r):r}return r}function Gr(r,n){var f,i,e=0,t=0,u=0,o=0,a=w(0);if(V=i=V-16|0,f=Fi(r,n),v[f+40>>2]=9496,v[f+24>>2]=8796,v[f+20>>2]=0,v[f>>2]=9468,v[f+44>>2]=0,v[f+48>>2]=0,v[f+36>>2]=0,v[f+28>>2]=0,v[f+32>>2]=0,v[f+52>>2]=0,v[f+56>>2]=0,Oa(r=f+24|0,n),Ga(f+40|0,n),v[i>>2]=0,An(r,n,i),(0|n)>0)for(r=0;v[i+12>>2]=0,v[i+4>>2]=0,v[i+8>>2]=0,v[i>>2]=8796,V=u=V-16|0,(0|(e=v[f+44>>2]))!=v[f+48>>2]?(v[f+44>>2]=e+1,Xn(v[f+52>>2]+(e<<4)|0,i)):(o=Xn(u,i),a=w(w(d[f+44>>2])*w(1.75)),t=(e=w(g(a))<w(2147483648)?~~a:-2147483648)>>>0<=8?8:e,v[f+48>>2]=t,e=v[5316],t=0|La[v[v[e>>2]+16>>2]](e,v[f+52>>2],t<<4,8610,117),v[f+52>>2]=t,e=v[f+44>>2],v[f+44>>2]=e+1,Xn(t+(e<<4)|0,o),Lo(o)),V=u+16|0,Lo(i),(0|n)!=(0|(r=r+1|0)););return V=i+16|0,f}function Ur(r){var n,f,i=w(0),e=0,u=0;V=n=V-16|0,b(r);r:if((f=2147483647&(e=t(2)))>>>0<=1061752794){if(i=w(1),f>>>0<964689920)break r;i=be(+r)}else if(f>>>0<=1081824209){if(f>>>0>=1075235812){i=w(-be(+r+((0|e)<0?3.141592653589793:-3.141592653589793)));break r}if(u=+r,(0|e)<0){i=Di(u+1.5707963267948966);break r}i=Di(1.5707963267948966-u)}else if(f>>>0<=1088565717){if(f>>>0>=1085271520){i=be(+r+((0|e)<0?6.283185307179586:-6.283185307179586));break r}if((0|e)<0){i=Di(-4.71238898038469-+r);break r}i=Di(+r-4.71238898038469)}else if(i=w(r-r),!(f>>>0>=2139095040)){switch(3&ir(r,n+8|0)){case 0:i=be(y[n+8>>3]);break r;case 1:i=Di(-y[n+8>>3]);break r;case 2:i=w(-be(y[n+8>>3]));break r}i=Di(y[n+8>>3])}return V=n+16|0,i}function jr(r){var n=0,f=0,i=0,t=0,u=0;if(n=v[12+(r|=0)>>2])for(;ni(v[v[r+20>>2]+(f<<2)>>2]),(0|n)!=(0|(f=f+1|0)););if(t=v[r+60>>2])for(u=v[r+68>>2],f=0;n=v[u+(f<<2)>>2],i=v[n+4>>2],v[n+24>>2]=v[i+44>>2],e[n+28|0]=l[i+48|0],e[n+29|0]=l[i+49|0],p[n+32>>2]=p[i+52>>2],p[n+36>>2]=p[i+56>>2],(0|t)!=(0|(f=f+1|0)););if(t=v[r+76>>2])for(u=v[r+84>>2],f=0;n=v[u+(f<<2)>>2],i=v[n+4>>2],p[n+28>>2]=p[i+44>>2],p[n+32>>2]=p[i+48>>2],p[n+36>>2]=p[i+52>>2],p[n+40>>2]=p[i+56>>2],(0|t)!=(0|(f=f+1|0)););if(i=v[r+92>>2])for(t=v[r+100>>2],f=0;r=v[t+(f<<2)>>2],n=v[r+4>>2],p[r+28>>2]=p[n+60>>2],p[r+32>>2]=p[n+64>>2],p[r+36>>2]=p[n+68>>2],p[r+40>>2]=p[n+72>>2],(0|i)!=(0|(f=f+1|0)););}function Hr(r){var n,f,i=0,e=0;V=n=V-16|0,b(r);r:if((f=2147483647&(e=t(2)))>>>0<=1061752794){if(f>>>0<964689920)break r;r=Di(+r)}else if(f>>>0<=1081824209){if(i=+r,f>>>0<=1075235811){if((0|e)<0){r=w(-be(i+1.5707963267948966));break r}r=be(i+-1.5707963267948966);break r}r=Di(-(((0|e)>=0?-3.141592653589793:3.141592653589793)+i))}else if(f>>>0<=1088565717){if(f>>>0<=1085271519){if(i=+r,(0|e)<0){r=be(i+4.71238898038469);break r}r=w(-be(i+-4.71238898038469));break r}r=Di(+r+((0|e)<0?6.283185307179586:-6.283185307179586))}else if(f>>>0>=2139095040)r=w(r-r);else{switch(3&ir(r,n+8|0)){case 0:r=Di(y[n+8>>3]);break r;case 1:r=be(y[n+8>>3]);break r;case 2:r=Di(-y[n+8>>3]);break r}r=w(-be(y[n+8>>3]))}return V=n+16|0,r}function Lr(r,n){n|=0;var f=0,i=0,e=0,t=0,u=0,o=0,a=0;if((0|(t=v[136+(r|=0)>>2]))!=(0|n)){r:if(n)if(t){if(!(f=v[t+24>>2]))break r;for(a=n+16|0;;){if(u=v[t+32>>2],d[4+(u+(i<<4)|0)>>2]<=o>>>0){n:{f:{for(;;){if((0|(i=i+1|0))==(0|f))break f;if(v[4+(u+(i<<4)|0)>>2])break}e=i;break n}e=f}if(f=f>>>0<=i>>>0,o=0,i=e,f)break r}if(e=v[12+(u+(i<<4)|0)>>2]+m(o,20)|0,f=v[e>>2],u=v[v[r+36>>2]+(f<<2)>>2],v[u+60>>2]==v[e+16>>2]&&(e=tf(a,f,e+4|0))&&$e(u,e),o=o+1|0,!(i>>>0<(f=v[t+24>>2])>>>0))break}}else if(e=v[r+28>>2])for(;i=v[v[r+36>>2]+(f<<2)>>2],t=ba(v[i+4>>2]),v[t+4>>2]&&(t=Mt(n,f,t))&&$e(i,t),(0|e)!=(0|(f=f+1|0)););v[r+136>>2]=n,nr(r)}}function Mr(r,n){var f,i,e=0,t=0,u=0,o=0,a=w(0);if(V=i=V-16|0,f=Qo(r),v[f+20>>2]=9540,v[f+4>>2]=8796,v[f>>2]=9512,v[f+32>>2]=0,v[f+24>>2]=0,v[f+28>>2]=0,v[f+16>>2]=0,v[f+8>>2]=0,v[f+12>>2]=0,Oa(r=f+4|0,n),Ga(f+20|0,n),v[i>>2]=0,An(r,n,i),(0|n)>0)for(r=0;v[i+12>>2]=0,v[i+4>>2]=0,v[i+8>>2]=0,v[i>>2]=8764,V=u=V-16|0,(0|(e=v[f+24>>2]))!=v[f+28>>2]?(v[f+24>>2]=e+1,Qn(v[f+32>>2]+(e<<4)|0,i)):(o=Qn(u,i),a=w(w(d[f+24>>2])*w(1.75)),t=(e=w(g(a))<w(2147483648)?~~a:-2147483648)>>>0<=8?8:e,v[f+28>>2]=t,e=v[5316],t=0|La[v[v[e>>2]+16>>2]](e,v[f+32>>2],t<<4,8610,117),v[f+32>>2]=t,e=v[f+24>>2],v[f+24>>2]=e+1,Qn(t+(e<<4)|0,o),Io(o)),V=u+16|0,Io(i),(0|n)!=(0|(r=r+1|0)););return V=i+16|0,f}function _r(r,n,f){var i,e=0,t=0,o=0,a=0,c=0,b=0,s=0,h=0,d=0,p=0,y=0,m=0,w=0,g=0,F=0;switch(i=v[r+4>>2],v[r+4>>2]=i+1,l[0|i]-1|0){case 0:return void Pt(f,n);case 1:v[r+4>>2]=i+2,e=l[i+1|0],v[r+4>>2]=i+3,t=l[i+2|0],v[r+4>>2]=i+4,o=l[i+3|0],v[r+4>>2]=i+5,a=l[i+4|0],v[r+4>>2]=i+6,c=l[i+5|0],v[r+4>>2]=i+7,b=l[i+6|0],v[r+4>>2]=i+8,s=l[i+7|0],v[r+4>>2]=i+9,h=l[i+8|0],v[r+4>>2]=i+10,d=l[i+9|0],v[r+4>>2]=i+11,p=l[i+10|0],v[r+4>>2]=i+12,y=l[i+11|0],v[r+4>>2]=i+13,m=l[i+12|0],v[r+4>>2]=i+14,w=l[i+13|0],v[r+4>>2]=i+15,g=l[i+14|0],v[r+4>>2]=i+16,F=l[i+15|0],v[r+4>>2]=i+17,Kr(f,n,(u(2,(t<<8|e<<16|o)<<8|a),k()),(u(2,(b<<8|c<<16|s)<<8|h),k()),(u(2,(p<<8|d<<16|y)<<8|m),k()),(u(2,l[i+16|0]|(g<<8|w<<16|F)<<8),k()))}}function zr(r){return v[r>>2]=10568,Ut(r+4|0),v[r+52>>2]=0,v[r+56>>2]=0,v[r+48>>2]=10600,v[r+44>>2]=0,v[r+36>>2]=0,v[r+40>>2]=0,v[r+32>>2]=10584,v[r+28>>2]=0,v[r+20>>2]=0,v[r+24>>2]=0,v[r+16>>2]=9708,v[r+60>>2]=0,v[r+64>>2]=0,v[r+132>>2]=10680,v[r+128>>2]=0,v[r+120>>2]=0,v[r+124>>2]=0,v[r+116>>2]=10664,v[r+112>>2]=0,v[r+104>>2]=0,v[r+108>>2]=0,v[r+100>>2]=10648,v[r+96>>2]=0,v[r+88>>2]=0,v[r+92>>2]=0,v[r+84>>2]=10632,v[r+80>>2]=0,v[r+72>>2]=0,v[r+76>>2]=0,v[r+68>>2]=10616,v[r+160>>2]=0,v[r+152>>2]=0,v[r+156>>2]=0,v[r+144>>2]=0,v[r+148>>2]=0,v[r+136>>2]=0,v[r+140>>2]=0,Ut(r+164|0),Ut(r+176|0),v[r+192>>2]=0,v[r+196>>2]=0,v[r+188>>2]=10696,v[r+200>>2]=0,v[r+204>>2]=0,Ut(r+208|0),Ut(r+220|0),r}function xr(r,n){var f;return V=f=V-16|0,r=Et(r,n),v[r+52>>2]=0,v[r+56>>2]=0,v[r+48>>2]=1065353216,v[r+40>>2]=0,v[r+44>>2]=1065353216,v[r+32>>2]=0,v[r+36>>2]=0,v[r+20>>2]=10148,v[r>>2]=10124,v[r+24>>2]=0,v[r+28>>2]=0,v[r+100>>2]=8796,v[r+84>>2]=8796,v[r+60>>2]=0,v[r+64>>2]=0,v[r+68>>2]=0,v[r+72>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+112>>2]=0,v[r+104>>2]=0,v[r+108>>2]=0,v[r+96>>2]=0,v[r+88>>2]=0,v[r+92>>2]=0,Ut(r+116|0),v[r+136>>2]=0,v[r+140>>2]=0,v[r+128>>2]=0,v[r+132>>2]=0,v[r+156>>2]=1065353216,v[r+160>>2]=1065353216,v[r+148>>2]=1065353216,v[r+152>>2]=1065353216,v[r+144>>2]=9404,mi(r+144|0),v[f+12>>2]=0,An(r+84|0,8,f+12|0),v[f+8>>2]=0,An(r+100|0,8,f+8|0),V=f+16|0,r}function Jr(r,n){return r=Rf(r,n),v[r+76>>2]=0,v[r+80>>2]=0,v[r+64>>2]=9868,v[r>>2]=9844,v[r+68>>2]=0,v[r+72>>2]=0,v[r+152>>2]=9884,v[r+136>>2]=9884,v[r+120>>2]=8796,v[r+104>>2]=8796,v[r+84>>2]=0,v[r+88>>2]=0,v[r+92>>2]=0,v[r+96>>2]=0,v[r+100>>2]=0,v[r+164>>2]=0,v[r+156>>2]=0,v[r+160>>2]=0,v[r+148>>2]=0,v[r+140>>2]=0,v[r+144>>2]=0,v[r+132>>2]=0,v[r+124>>2]=0,v[r+128>>2]=0,v[r+116>>2]=0,v[r+108>>2]=0,v[r+112>>2]=0,Ut(r+168|0),v[r+196>>2]=0,v[r+200>>2]=0,v[r+188>>2]=0,v[r+192>>2]=0,v[r+180>>2]=0,v[r+184>>2]=0,v[r+216>>2]=1065353216,v[r+220>>2]=1065353216,v[r+208>>2]=1065353216,v[r+212>>2]=1065353216,v[r+204>>2]=9404,mi(r+204|0),v[r+232>>2]=0,e[r+228|0]=0,v[r+224>>2]=0,r}function Kr(r,n,f,i,e,t){r|=0,n|=0,f=w(f),i=w(i),e=w(e),t=w(t);var u,o=w(0),a=w(0),c=w(0),b=w(0),k=0;if(u=v[r+16>>2],r=m(n,19),v[u+(r<<2)>>2]=1073741824,(n=r+1|0)>>>0<(r=r+19|0)>>>0)for(o=w(w(e-w(f+f))*w(.029999999329447746)),c=w(w(w(w(f-e)*w(3))+w(1))*w(.006000000052154064)),e=w(w(o+o)+c),a=w(w(t-w(i+i))*w(.029999999329447746)),b=w(w(w(w(i-t)*w(3))+w(1))*w(.006000000052154064)),t=w(w(a+a)+b),a=i=w(w(b*w(.1666666716337204))+w(w(i*w(.30000001192092896))+a)),o=f=w(w(c*w(.1666666716337204))+w(w(f*w(.30000001192092896))+o));p[(k=(n<<2)+u|0)>>2]=f,p[k+4>>2]=i,a=w(t+a),i=w(a+i),o=w(o+e),f=w(f+o),e=w(c+e),t=w(b+t),r>>>0>(n=n+2|0)>>>0;);}function Br(r){r|=0;var n,f=w(0),i=w(0),e=w(0),t=w(0),u=w(0),o=w(0),a=w(0),c=w(0),b=w(0),k=w(0),s=w(0),l=w(0),h=w(0);e=p[r+52>>2],f=p[r+44>>2],u=p[r+76>>2],c=p[r+60>>2],b=p[r+68>>2],i=Hr(o=w(p[r+40>>2]*w(.01745329238474369))),t=p[r+56>>2],a=p[r+48>>2],k=p[r+80>>2],s=p[r+64>>2],o=Ur(o),l=p[r+32>>2],h=p[r+72>>2],u=w(f*w(e/u)),e=w(w(f*w(e*w(-.5)))+w(u*c)),n=v[r+96>>2],f=w(a*w(t*w(-.5))),t=w(a*w(t/k)),f=w(f+w(s*t)),a=p[r+36>>2],c=w(w(o*f)+a),u=w(w(b*u)+e),b=w(i*u),p[n+28>>2]=c+b,k=w(l+w(e*o)),t=w(w(h*t)+f),s=w(i*t),p[n+8>>2]=k-s,e=w(e*i),p[n+4>>2]=c+e,i=w(f*i),p[n>>2]=k-i,f=w(l+w(u*o)),p[n+24>>2]=f-i,i=w(a+w(t*o)),p[n+20>>2]=i+b,p[n+16>>2]=f-s,p[n+12>>2]=i+e}function Nr(r){r|=0;var n,f,i=0,t=0;return Jr(n=ht(236),ha(r)),i=v[r+68>>2],(f=v[n+72>>2])&&(!(t=v[n+68>>2])|(0|i)==(0|t)||La[0|f](t)),v[n+72>>2]=0,v[n+68>>2]=i,p[n+180>>2]=p[r+180>>2],p[n+184>>2]=p[r+184>>2],p[n+188>>2]=p[r+188>>2],p[n+192>>2]=p[r+192>>2],e[n+228|0]=l[r+228|0],v[n+232>>2]=v[r+232>>2],p[n+76>>2]=p[r+76>>2],p[n+80>>2]=p[r+80>>2],p[n+84>>2]=p[r+84>>2],p[n+88>>2]=p[r+88>>2],p[n+92>>2]=p[r+92>>2],p[n+96>>2]=p[r+96>>2],sf(n+168|0,r+168|0),p[n+208>>2]=p[r+208>>2],p[n+212>>2]=p[r+212>>2],p[n+216>>2]=p[r+216>>2],p[n+220>>2]=p[r+220>>2],mi(n+204|0),v[n+56>>2]=v[r+56>>2],Zn(n,(i=v[r+100>>2])||r),hr(n),0|n}function qr(r,n,f){var i,e,t=0,u=0,o=w(0),a=w(0),c=0,b=w(0),k=w(0),s=w(0);V=i=V-16|0,e=v[r+60>>2],o=p[n+56>>2],b=p[n+52>>2],a=w(o-b),k=Or(p[n+76>>2],a);r:if(e){for(;;){if(u=v[v[r+68>>2]+(t<<2)>>2],(s=p[u+8>>2])<k)break r;if(o<s||(c=v[r+72>>2],Tt(i,5,n,u),Xr(c+4|0,i)),(0|e)==(0|(t=t+1|0)))break}t=e}r:{n:{if(l[n+36|0]){if(a==w(0))break n;if(Or(p[n+72>>2],a)<k)break n;break r}if(!(f>=o)|!(p[n+60>>2]<o))break r}u=v[r+72>>2],Tt(i,3,n,0),Xr(u+4|0,i)}if(t>>>0<e>>>0)for(;u=v[v[r+68>>2]+(t<<2)>>2],p[u+8>>2]<b||(c=v[r+72>>2],Tt(i,5,n,u),Xr(c+4|0,i)),(0|e)!=(0|(t=t+1|0)););V=i+16|0}function Dr(){C(21849,21850,21851,0,11288,1664,11291,0,11291,0,4540,11293,1665),R(21849,2163,1,16912,11293,1666,1667,0),R(21849,1181,1,16912,11293,1666,1668,0),R(21849,1100,2,16916,11360,1669,1670,0),R(21849,1118,1,16912,11293,1666,1671,0),R(21849,6790,2,16924,11360,1672,1673,0),R(21849,3916,5,16944,13956,1674,1675,0),R(21849,1134,5,16976,13956,1676,1677,0),R(21849,6852,3,16996,13272,1678,1679,0),R(21849,6819,2,17008,13624,1680,1681,0),R(21849,3895,2,16320,13624,1682,1683,0),R(21849,6769,1,15112,11288,1684,1685,0),R(21849,5211,1,17016,11288,1686,1687,0),R(21849,1079,1,15884,11288,1384,1688,0),R(21849,1797,1,17020,11288,1689,1690,0)}function Vr(r,n,f,i,e,u,o,a,c,k,s,l){var h=w(0),d=w(0),y=w(0),m=w(0),g=0,F=w(0),A=w(0),T=w(0),$=w(0);r:{if(!(r<w(9999999747378752e-21))&2143289344!=(0|(b(r),t(2)))){if(g=(k=v[k+12>>2])+(s<<2)|0,d=w(r*r),F=w(d*r),$=w(c*F),c=w(w(1)-r),y=w(c*r),h=w(y*w(3)),A=w(h*r),m=w(c*c),T=w(c*m),c=w(c*h),h=w($+w(w(o*A)+w(w(f*T)+w(c*e)))),p[g+4>>2]=h,a=w(w(a*F)+w(w(u*A)+w(w(n*T)+w(c*i)))),p[g>>2]=a,!l)break r;+r<.001?r=Rr(w(e-f),w(i-n)):(r=w(y*e),f=w(h-w(w(o*d)+w(w(f*m)+w(r+r)))),r=w(y*i),r=Rr(f,w(a-w(w(u*d)+w(w(n*m)+w(r+r))))))}else k=v[k+12>>2],p[(l=k+(s<<2)|0)>>2]=n,p[l+4>>2]=f,r=Rr(w(e-f),w(i-n));p[8+((s<<2)+k|0)>>2]=r}}function Zr(r,n,f,i){var t,u=0,o=0,a=0,c=0,b=0;if(V=t=V-16|0,v[r>>2]=1032,v[r+4>>2]=1048,v[r+8>>2]=v[f+4>>2],u=v[f+8>>2],v[r+16>>2]=0,v[r+12>>2]=u,u&&(o=v[5316],c=r,b=0|La[v[v[o>>2]+12>>2]](o,u<<2,8610,221),v[c+16>>2]=b,o=v[r+8>>2]))for(u=0;v[(a=u<<2)+v[r+16>>2]>>2]=v[v[f+12>>2]+a>>2],(0|o)!=(0|(u=u+1|0)););if(p[r+32>>2]=i,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=8620,zf(r+36|0,n),v[f+4>>2]>0)for(u=r+20|0,n=0;o=v[v[f+12>>2]+(n<<2)>>2],c=t,b=0|La[v[v[o>>2]+16>>2]](o),v[c+12>>2]=b,e[t+11|0]=1,Nn(u,t+12|0,t+11|0),(0|(n=n+1|0))<v[f+4>>2];);return V=t+16|0,r}function Yr(r,n,f,i){var e,t=0,o=0,a=0,c=0,b=0,s=0;V=e=V-16|0,v[e+12>>2]=0,An(i,n,e+12|0);r:if(f==w(1)){if(!((0|n)<=0))for(t=v[r+4>>2],a=v[i+12>>2],i=0;v[r+4>>2]=t+1,c=l[0|t],v[r+4>>2]=t+2,b=l[t+1|0],v[r+4>>2]=t+3,s=l[t+2|0],o=t+4|0,v[r+4>>2]=o,v[a+(i<<2)>>2]=l[t+3|0]|(s|b<<8|c<<16)<<8,t=o,(0|(i=i+1|0))!=(0|n););}else{if((0|n)<=0)break r;for(t=v[r+4>>2],a=v[i+12>>2],i=0;v[r+4>>2]=t+1,c=l[0|t],v[r+4>>2]=t+2,b=l[t+1|0],v[r+4>>2]=t+3,s=l[t+2|0],o=t+4|0,v[r+4>>2]=o,p[a+(i<<2)>>2]=(u(2,l[t+3|0]|(s|b<<8|c<<16)<<8),k()*f),t=o,(0|(i=i+1|0))!=(0|n););}V=e+16|0}function Xr(r,n){var f,i=0,e=w(0),t=0;V=f=V-16|0,(0|(i=v[r+4>>2]))!=v[r+8>>2]?(v[r+4>>2]=i+1,r=v[r+12>>2]+(i<<4)|0,v[r>>2]=8684,i=v[n+8>>2],v[r+4>>2]=v[n+4>>2],v[r+8>>2]=i,v[r+12>>2]=v[n+12>>2]):(v[f>>2]=8684,v[f+12>>2]=v[n+12>>2],t=v[n+8>>2],v[f+4>>2]=v[n+4>>2],v[f+8>>2]=t,e=w(w(i>>>0)*w(1.75)),n=(n=w(g(e))<w(2147483648)?~~e:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,i=v[5316],n=0|La[v[v[i>>2]+16>>2]](i,v[r+12>>2],n<<4,8610,117),v[r+12>>2]=n,i=r,r=v[r+4>>2],v[i+4>>2]=r+1,v[(r=n+(r<<4)|0)>>2]=8684,n=v[f+8>>2],v[r+4>>2]=v[f+4>>2],v[r+8>>2]=n,v[r+12>>2]=v[f+12>>2]),V=f+16|0}function Qr(r,n,f,i){var e,t=0,u=0,o=0,a=0,c=0;V=e=V-32|0,au(e+20|0,n),n=v[e+24>>2],t=v[e+20>>2];r:if(!(n>>>0>=(u=v[t+4>>2])>>>0))for(c=e+4|0;;){if(o=v[e+28>>2],a=v[t+12>>2],o>>>0>=d[4+(a+(n<<4)|0)>>2]){n:{f:{for(;;){if((0|u)==(0|(n=n+1|0)))break f;if(v[4+((n<<4)+a|0)>>2])break}t=n;break n}t=u}if(v[e+24>>2]=t,u=n>>>0>=u>>>0,o=0,n=t,u)break r}if(n=v[12+((n<<4)+a|0)>>2],v[e+28>>2]=o+1,n=n+m(o,20)|0,v[e>>2]=v[n>>2],t=zf(c,n+4|0),n=v[n+16>>2],v[e+16>>2]=n,v[e>>2]==(0|f)&&Kn(r,n,i),Oe(t),n=v[e+24>>2],t=v[e+20>>2],!(n>>>0<(u=v[t+4>>2])>>>0))break}V=e+32|0}function rn(r,n,f,i,e){var t,u=w(0),o=w(0),a=w(0),c=w(0),b=w(0),k=w(0),s=w(0),l=w(0);b=p[n+100>>2],k=p[n+92>>2],s=p[n+96>>2],t=(i<<2)+f|0,a=p[n+112>>2],r=v[r+96>>2],u=p[r+24>>2],c=p[n+104>>2],l=p[n+108>>2],o=p[r+28>>2],p[t+4>>2]=a+w(w(u*c)+w(l*o)),p[t>>2]=b+w(w(u*k)+w(s*o)),i=((n=i+e|0)<<2)+f|0,u=p[r>>2],o=p[r+4>>2],p[i+4>>2]=a+w(w(c*u)+w(l*o)),p[i>>2]=b+w(w(u*k)+w(s*o)),i=((n=n+e|0)<<2)+f|0,u=p[r+8>>2],o=p[r+12>>2],p[i+4>>2]=a+w(w(c*u)+w(l*o)),p[i>>2]=b+w(w(u*k)+w(s*o)),n=(n+e<<2)+f|0,u=a,a=p[r+16>>2],o=w(c*a),c=p[r+20>>2],p[n+4>>2]=u+w(o+w(l*c)),p[n>>2]=b+w(w(a*k)+w(s*c))}function nn(r){var n,f,i=w(0),e=w(0);if(b(r),(n=2147483647&(f=t(2)))>>>0>=1065353216)return w(1065353216==(0|n)?(0|f)>=0?0:3.141592502593994:w(0)/w(r-r));r:{if(n>>>0<=1056964607){if(i=w(1.570796251296997),n>>>0<847249409)break r;return w(w(w(w(7.549789415861596e-8)-w(r*Xi(w(r*r))))-r)+w(1.570796251296997))}if((0|f)<0)return r=w(w(r+w(1))*w(.5)),i=w(T(r)),r=w(w(1.570796251296997)-w(i+w(w(i*Xi(r))+w(-7.549789415861596e-8)))),w(r+r);i=w(w(w(1)-r)*w(.5)),u(2,-4096&(b(e=w(T(i))),t(2))),r=k(),r=w(w(w(e*Xi(i))+w(w(i-w(r*r))/w(e+r)))+r),i=w(r+r)}return i}function fn(r){var n=0,f=0,i=0,e=0;if(v[4+(r|=0)>>2]=8732,v[r>>2]=8716,n=v[r+44>>2])for(;;){if(i=v[v[r+52>>2]+(e<<2)>>2]){if(n=v[i+24>>2])for(;f=v[n+24>>2],La[v[v[n>>2]+4>>2]](n),n=f;);if(n=v[i+20>>2])for(;f=v[n+20>>2],La[v[v[n>>2]+4>>2]](n),n=f;);La[v[v[i>>2]+4>>2]](i),n=v[r+44>>2]}if(!((e=e+1|0)>>>0<n>>>0))break}return(n=v[r+72>>2])&&La[v[v[n>>2]+4>>2]](n),Co(r+76|0),Ho(r+56|0),Oo(r+40|0),gt(r+20|0),v[r+4>>2]=8748,(n=v[r+12>>2])&&(f=v[r+8>>2])&&La[0|n](f),0|r}function en(r){var n,f,i=0,e=0,t=0,u=w(0),o=w(0),a=w(0),c=0;n=v[r+12>>2],u=p[n>>2],e=v[r+4>>2],o=w(w(p[((f=e-2|0)<<2)+n>>2]*p[n+4>>2])-w(p[((e<<2)+n|0)-4>>2]*u));r:{if(i=e-3|0){for(r=0;a=w(u*p[12+((t=r<<2)+n|0)>>2]),u=p[((r=r+2|0)<<2)+n>>2],o=w(o+w(a-w(p[(4|t)+n>>2]*u))),r>>>0<i>>>0;);if(e>>>0<2|o<w(0))break r;e=e>>>1|0}else if(e=1,o<w(0))break r;for(r=0;o=p[(i=(t=r<<2)+n|0)>>2],u=p[(t=(4|t)+n|0)>>2],c=i,i=(f-r<<2)+n|0,p[c>>2]=p[i>>2],p[t>>2]=p[i+4>>2],p[i+4>>2]=u,p[i>>2]=o,e>>>0>(r=r+2|0)>>>0;);}}function tn(r){var n,f=0,i=0,e=0,t=0,u=0,o=0;V=n=V-32|0,v[(r|=0)>>2]=10848;r:if(f=v[r+24>>2])for(o=n+16|0;;){if(u=v[r+32>>2],d[4+(u+(i<<4)|0)>>2]<=t>>>0){n:{f:{for(;;){if((0|(i=i+1|0))==(0|f))break f;if(v[4+((i<<4)+u|0)>>2])break}e=i;break n}e=f}if(f=f>>>0<=i>>>0,t=0,i=e,f)break r}if(e=v[12+((i<<4)+u|0)>>2]+m(t,20)|0,v[n+12>>2]=v[e>>2],f=zf(o,e+4|0),e=v[e+16>>2],v[n+28>>2]=e,Ve(e),Oe(f),t=t+1|0,!(i>>>0<(f=v[r+24>>2])>>>0))break}return bo(r+52|0),Go(r+36|0),v[r+16>>2]=10832,co(r+20|0),Oe(r+4|0),V=n+32|0,0|r}function un(r,n,f){r|=0,n|=0,f|=0;var i=0,e=0,t=w(0),u=0,o=0,a=0;i=v[r+4>>2],v[r+4>>2]=n,(e=v[r+8>>2])>>>0<n>>>0&&(t=w(w(n>>>0)*w(1.75)),u=w(g(t))<w(2147483648)?~~t:-2147483648,n=(n=e?u:n)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5316],o=r,a=0|La[v[v[e>>2]+16>>2]](e,v[r+12>>2],m(n,12),8610,89),v[o+12>>2]=a,n=v[r+4>>2]);r:if(n>>>0<=i>>>0){if(n>>>0>=i>>>0)break r;for(;f=v[r+12>>2]+m(n,12)|0,La[v[v[f>>2]>>2]](f),(0|i)!=(0|(n=n+1|0)););}else for(;zf(v[r+12>>2]+m(i,12)|0,f),(i=i+1|0)>>>0<d[r+4>>2];);}function on(r,n){var f,i=0,e=w(0),t=0;V=f=V-16|0,(0|(i=v[r+4>>2]))!=v[r+8>>2]?(v[r+4>>2]=i+1,r=v[r+12>>2]+m(i,12)|0,v[r+8>>2]=v[n+8>>2],i=v[n+4>>2],v[r>>2]=v[n>>2],v[r+4>>2]=i):(v[f+8>>2]=v[n+8>>2],t=v[n+4>>2],v[f>>2]=v[n>>2],v[f+4>>2]=t,e=w(w(i>>>0)*w(1.75)),n=(n=w(g(e))<w(2147483648)?~~e:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,i=v[5316],n=0|La[v[v[i>>2]+16>>2]](i,v[r+12>>2],m(n,12),8610,117),v[r+12>>2]=n,i=r,r=v[r+4>>2],v[i+4>>2]=r+1,r=n+m(r,12)|0,v[r+8>>2]=v[f+8>>2],n=v[f+4>>2],v[r>>2]=v[f>>2],v[r+4>>2]=n),V=f+16|0}function an(r,n,f,i,t){var u=0,o=w(0),a=0;return(u=v[r+28>>2])?(a=u-1|0,u=v[v[r+36>>2]+(a<<2)>>2],v[r+28>>2]=a):ln(u=ht(172)),v[u+40>>2]=0,v[u+44>>2]=0,e[u+37|0]=0,e[u+36|0]=i,v[u+16>>2]=f,v[u+32>>2]=n,v[u+48>>2]=0,v[u+52>>2]=0,o=p[f+32>>2],v[u+104>>2]=1065353216,v[u+84>>2]=2139095039,v[u+88>>2]=1065353216,v[u+76>>2]=-1082130432,v[u+80>>2]=-1082130432,v[u+68>>2]=0,v[u+72>>2]=0,v[u+60>>2]=-1082130432,v[u+64>>2]=-1082130432,p[u+56>>2]=o,v[u+92>>2]=1065353216,v[u+96>>2]=0,o=t?Jn(v[r+16>>2],v[t+16>>2],f):w(0),p[u+100>>2]=o,u}function cn(r,n,f){r|=0,n|=0,f|=0;var i=0,e=0,t=w(0),u=0,o=0,a=0;if(e=v[r+4>>2],v[r+4>>2]=n,(i=v[r+8>>2])>>>0<n>>>0&&(t=w(w(n>>>0)*w(1.75)),u=w(g(t))<w(2147483648)?~~t:-2147483648,n=(n=i?u:n)>>>0<=8?8:n,v[r+8>>2]=n,i=v[5316],o=r,a=0|La[v[v[i>>2]+16>>2]](i,v[r+12>>2],m(n,20),8610,89),v[o+12>>2]=a,n=v[r+4>>2]),n>>>0>e>>>0)for(;i=v[f+4>>2],n=v[r+12>>2]+m(e,20)|0,v[n>>2]=v[f>>2],v[n+4>>2]=i,v[n+16>>2]=v[f+16>>2],i=v[f+12>>2],v[n+8>>2]=v[f+8>>2],v[n+12>>2]=i,(e=e+1|0)>>>0<d[r+4>>2];);}function bn(r,n){return v[r+16>>2]=n,v[r+8>>2]=0,v[r+12>>2]=0,v[r+68>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,v[r+56>>2]=8844,v[r+52>>2]=0,v[r+44>>2]=0,v[r+48>>2]=0,v[r+40>>2]=8780,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+24>>2]=8780,v[r+20>>2]=8828,v[r+4>>2]=8732,v[r>>2]=8716,n=ht(32),e[n+28|0]=0,v[n+24>>2]=r+20,v[n+20>>2]=r,v[n+16>>2]=0,v[n+8>>2]=0,v[n+12>>2]=0,v[n+4>>2]=8812,v[n>>2]=8700,v[r+80>>2]=0,v[r+84>>2]=0,v[r+76>>2]=8620,v[r+72>>2]=n,e[r+88|0]=0,v[r+104>>2]=1065353216,v[r+96>>2]=0,v[r+100>>2]=0,v[r+92>>2]=9,r}function kn(r,n,f){var i,t,u,o=0,a=0;if(V=t=V-16|0,i=ra(r),v[i+8>>2]=9292,v[i+4>>2]=n,v[i>>2]=9652,r=0,v[i+20>>2]=0,v[i+12>>2]=0,v[i+16>>2]=0,v[i+24>>2]=v[n+44>>2],e[i+28|0]=l[n+48|0],e[i+29|0]=l[n+49|0],p[i+32>>2]=p[n+52>>2],p[i+36>>2]=p[n+56>>2],n=Eu(f,la(v[n+40>>2])),e[i+44|0]=0,v[i+40>>2]=n,Oa(u=i+8|0,v[v[i+4>>2]+28>>2]),n=v[i+4>>2],v[n+28>>2])for(;o=t,a=Eu(f,la(v[v[n+36>>2]+(r<<2)>>2])),v[o+12>>2]=a,_n(u,t+12|0),r=r+1|0,n=v[i+4>>2],r>>>0<d[n+28>>2];);return V=t+16|0,i}function sn(r,n,f){var i,t,u,o=w(0),a=0,c=0;if(V=t=V-16|0,i=ra(r),v[i+8>>2]=9292,v[i+4>>2]=n,v[i>>2]=10988,r=0,v[i+20>>2]=0,v[i+12>>2]=0,v[i+16>>2]=0,a=i,c=Eu(f,la(v[n+40>>2])),v[a+24>>2]=c,p[i+28>>2]=p[n+44>>2],p[i+32>>2]=p[n+48>>2],p[i+36>>2]=p[n+52>>2],o=p[n+56>>2],e[i+44|0]=0,p[i+40>>2]=o,Oa(u=i+8|0,v[v[i+4>>2]+28>>2]),n=v[i+4>>2],v[n+28>>2])for(;a=t,c=Eu(f,la(v[v[n+36>>2]+(r<<2)>>2])),v[a+12>>2]=c,_n(u,t+12|0),r=r+1|0,n=v[i+4>>2],r>>>0<d[n+28>>2];);return V=t+16|0,i}function vn(r,n,f,i,e){r|=0,n|=0,f|=0,i|=0,e=w(e);var t=0,u=0,o=w(0),a=w(0);r:if(t=Vf(r,n)){for(;t=v[(u=t)+20>>2];);if(t=an(r,n,f,i,u),v[u+20>>2]=t,!(e<=w(0)))break r;if((a=w(p[u+56>>2]-p[u+52>>2]))!=w(0)){o=p[u+72>>2],l[u+36|0]?(o=w(o/a),n=w(g(o))<w(2147483648)?~~o:-2147483648,e=w(w(a*w(n+1|0))+e)):e=w((o<a?a:o)+e),e=w(e-Jn(v[r+16>>2],v[u+16>>2],f));break r}e=p[u+72>>2]}else Rn(r,n,t=an(r,n,f,i,0),1),mr(v[r+72>>2]);return p[t+68>>2]=e,0|t}function ln(r){return v[r+16>>2]=0,v[r+20>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=8668,v[r>>2]=8652,v[r+24>>2]=0,v[r+28>>2]=0,s[r+30>>1]=0,s[r+32>>1]=0,s[r+34>>1]=0,s[r+36>>1]=0,Ze(r+40|0,0,48),v[r+92>>2]=0,v[r+96>>2]=0,v[r+88>>2]=1065353216,v[r+100>>2]=0,v[r+104>>2]=0,v[r+168>>2]=0,v[r+164>>2]=9,v[r+160>>2]=0,v[r+152>>2]=0,v[r+156>>2]=0,v[r+148>>2]=8796,v[r+144>>2]=0,v[r+136>>2]=0,v[r+140>>2]=0,v[r+132>>2]=8780,v[r+128>>2]=0,v[r+120>>2]=0,v[r+124>>2]=0,v[r+116>>2]=8764,v[r+108>>2]=0,v[r+112>>2]=2,r}function hn(r){r|=0;var n=0,f=w(0);n=sa(v[r+4>>2]),p[r+20>>2]=p[n+4>>2],p[r+24>>2]=p[n+8>>2],p[r+28>>2]=p[n+12>>2],p[r+32>>2]=p[n+16>>2],mi(r+16|0),l[r+56|0]&&(n=Ca(v[r+4>>2]),p[r+40>>2]=p[n+4>>2],p[r+44>>2]=p[n+8>>2],p[r+48>>2]=p[n+12>>2],p[r+52>>2]=p[n+16>>2],mi(r+36|0)),n=ba(v[r+4>>2]);r:{if(v[n+4>>2]){if(v[r+60>>2]=0,(0|(n=Ui(v[r+12>>2],v[v[r+4>>2]+4>>2],n)))==v[r+60>>2])break r;v[r+60>>2]=n}else{if(!v[r+60>>2])break r;v[r+60>>2]=0}f=p[v[r+12>>2]+160>>2],v[r+76>>2]=0,p[r+68>>2]=f}}function dn(r,n,f){var i,e,t=0,u=0,o=0,a=0;V=i=V-16|0,v[i+8>>2]=0,v[i+12>>2]=0,vf(r,n,i+8|0),e=v[i+12>>2],n=v[i+8>>2];r:{for(;;){if(r=0,(0|n)==(0|e))break r;if(r=l[0|n],n=n+1|0,58==(0|r))break}for(v[i+8>>2]=n,r=n;;){v[(a=(t<<3)+f|0)>>2]=n;n:{for(;;){if(u=r,(0|r)==(0|e)){r=n,o=t;break n}if(r=u+1|0,44==l[0|u])break}if(o=3,v[4+((t<<3)+f|0)>>2]=u-1,rf(a),n=r,3!=(0|(t=t+1|0)))continue}break}v[4+(n=(o<<3)+f|0)>>2]=e,v[n>>2]=r,rf(n),r=o+1|0}return V=i+16|0,r}function pn(r,n,f){r|=0,n|=0,f=w(f);var i,e=w(0),t=w(0),u=0,o=0,a=w(0);if(f=Wu(f),i=v[r+16>>2],r=m(n,19),(e=p[i+(r<<2)>>2])==w(0))return w(f);if(e!=w(1)){if((u=r+1|0)>>>0<(o=r+19|0)>>>0){for(r=u;;){if(n=r,f<=(t=p[(r<<2)+i>>2]))return e=w(0),(0|n)!=(0|u)&&(a=p[(r=(n<<2)+i|0)-4>>2],e=p[r-8>>2]),w(w(a+w(w(w(f-e)*w(p[4+((n<<2)+i|0)>>2]-a))/w(t-e))));if(!(o>>>0>(r=n+2|0)>>>0))break}r=n+1|0}e=p[(r<<2)+i>>2],f=w(e+w(w(w(f-t)*w(w(1)-e))/w(w(1)-t)))}else f=w(0);return w(f)}function yn(r){var n,f,i,e,t,u,o,a,c=0,b=0;if(v[(r|=0)>>2]=10568,jn(n=r+16|0),jn(f=r+32|0),jn(i=r+48|0),v[r+64>>2]=0,jn(e=r+68|0),jn(t=r+84|0),jn(u=r+100|0),jn(o=r+116|0),jn(a=r+132|0),v[r+192>>2])for(;b=v[5316],La[v[v[b>>2]+20>>2]](b,v[v[r+200>>2]+(c<<2)>>2],8610,74),(c=c+1|0)>>>0<d[r+192>>2];);return Oe(r+220|0),Oe(r+208|0),no(r+188|0),Oe(r+176|0),Oe(r+164|0),fo(a),io(o),eo(u),to(t),uo(e),oo(i),Fo(f),Go(n),Oe(r+4|0),0|r}function mn(r,n){var f=0,i=0,e=0,t=0,u=w(0);if(Ea(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;e=v[n+12>>2]+(t<<1)|0,(0|(f=v[r+4>>2]))!=v[r+8>>2]?s[v[r+12>>2]+(f<<1)>>1]=h[e>>1]:(e=h[e>>1],u=w(w(f>>>0)*w(1.75)),f=(f=w(g(u))<w(2147483648)?~~u:-2147483648)>>>0<=8?8:f,v[r+8>>2]=f,i=v[5316],i=0|La[v[v[i>>2]+16>>2]](i,v[r+12>>2],f<<1,8610,117),v[r+12>>2]=i,f=v[r+4>>2],s[i+(f<<1)>>1]=e),v[r+4>>2]=f+1,(t=t+1|0)>>>0<d[n+4>>2];);}function wn(r){var n=0,f=0,i=0,t=0,u=0;if(i=v[124+(r|=0)>>2])for(u=v[r+132>>2];n=v[(f<<2)+u>>2],e[n+88|0]=1,p[n+60>>2]=p[n+32>>2],t=v[n+40>>2],v[n+64>>2]=v[n+36>>2],v[n+68>>2]=t,t=v[n+48>>2],v[n+72>>2]=v[n+44>>2],v[n+76>>2]=t,t=v[n+56>>2],v[n+80>>2]=v[n+52>>2],v[n+84>>2]=t,(0|i)!=(0|(f=f+1|0)););if(f=v[r+108>>2])for(n=0;i=v[v[r+116>>2]+(n<<2)>>2],La[v[v[i>>2]+12>>2]](i),(0|f)!=(0|(n=n+1|0)););}function gn(r){var n,f=0,i=0,t=0,u=0;V=n=V-32|0,v[n+24>>2]=0,f=v[5476],v[n+20>>2]=f,v[n+8>>2]=17096,v[n+28>>2]=n+8;r:{n:{if(f)for(;;){if(v[n+28>>2]=f,t=i=v[v[f+8>>2]+4>>2],i)for(;;){if(v[t+4>>2]==(0|r))break n;if(!(t=v[t+12>>2]))break}if(!(f=v[f+12>>2]))break}e[n+4|0]=1;break r}e[n+4|0]=0;n:{for(;;){if(v[i+4>>2]==(0|r))break n;if(!(i=v[i+12>>2]))break}break r}u=v[i+8>>2]}return V=n+32|0,u}function Fn(r,n,f,i,e,t){var u,o=0;if(u=v[v[f+36>>2]+(v[n+4>>2]<<2)>>2],o=v[u+8>>2],0|La[v[v[o>>2]+16>>2]](o)){o=v[n+20>>2];r:{if(p[o>>2]>i){if(e>>>0>1)break r;n=ba(v[u+4>>2])}else e=v[n+12>>2]-1|0,p[o+(e<<2)>>2]<=i||(e=Kf(n+8|0,i)-1|0),n=v[n+36>>2]+m(e,12)|0;$e(u,n=v[n+4>>2]?Ui(f,v[v[u+4>>2]+4>>2],n):0),t&&Qu(u,v[r+100>>2]+2|0)}(0|(r=v[r+100>>2]))<v[u+64>>2]||Qu(u,r+1|0)}}function An(r,n,f){r|=0,n|=0,f|=0;var i=0,e=0,t=w(0),u=0,o=0,a=0;if(i=v[r+4>>2],v[r+4>>2]=n,(e=v[r+8>>2])>>>0<n>>>0&&(t=w(w(n>>>0)*w(1.75)),u=w(g(t))<w(2147483648)?~~t:-2147483648,n=(n=e?u:n)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5316],o=r,a=0|La[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<2,8610,89),v[o+12>>2]=a,n=v[r+4>>2]),n>>>0>i>>>0)for(r=v[r+12>>2];p[r+(i<<2)>>2]=p[f>>2],(0|(i=i+1|0))!=(0|n););}function Tn(r,n,f){r|=0,n|=0,f|=0;var i=0,e=0,t=w(0),u=0,o=0,a=0;if(i=v[r+4>>2],v[r+4>>2]=n,(e=v[r+8>>2])>>>0<n>>>0&&(t=w(w(n>>>0)*w(1.75)),u=w(g(t))<w(2147483648)?~~t:-2147483648,n=(n=e?u:n)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5316],o=r,a=0|La[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<2,8610,89),v[o+12>>2]=a,n=v[r+4>>2]),n>>>0>i>>>0)for(r=v[r+12>>2];v[r+(i<<2)>>2]=v[f>>2],(0|(i=i+1|0))!=(0|n););}function $n(r,n,f){r|=0,n|=0,f|=0;var i=0,e=0,t=w(0),u=0,o=0,a=0;if(i=v[r+4>>2],v[r+4>>2]=n,(e=v[r+8>>2])>>>0<n>>>0&&(t=w(w(n>>>0)*w(1.75)),u=w(g(t))<w(2147483648)?~~t:-2147483648,n=(n=e?u:n)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5316],o=r,a=0|La[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<1,8610,89),v[o+12>>2]=a,n=v[r+4>>2]),n>>>0>i>>>0)for(r=v[r+12>>2];s[r+(i<<1)>>1]=h[f>>1],(0|(i=i+1|0))!=(0|n););}function In(r,n,f){r|=0,n|=0,f|=0;var i=0,e=0,t=w(0),u=0,o=0,a=0;if(i=v[r+4>>2],v[r+4>>2]=n,(e=v[r+8>>2])>>>0<n>>>0&&(t=w(w(n>>>0)*w(1.75)),u=w(g(t))<w(2147483648)?~~t:-2147483648,n=(n=e?u:n)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5316],o=r,a=0|La[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<2,8610,89),v[o+12>>2]=a,n=v[r+4>>2]),n>>>0>i>>>0)for(;v[v[r+12>>2]+(i<<2)>>2]=v[f>>2],(0|(i=i+1|0))!=(0|n););}function Cn(r,n){var f=0,i=0;r:{if(3&((i=r)^n))f=l[0|n];else{if(3&n)for(;;){if(f=l[0|n],e[0|i]=f,!f)break r;if(i=i+1|0,!(3&(n=n+1|0)))break}if(!(~(f=v[n>>2])&f-16843009&-2139062144))for(;v[i>>2]=f,f=v[n+4>>2],i=i+4|0,n=n+4|0,!(f-16843009&~f&-2139062144););}if(e[0|i]=f,255&f)for(;f=l[n+1|0],e[i+1|0]=f,i=i+1|0,n=n+1|0,f;);}return r}function Pn(r,n,f){var i,e=0,t=w(0),u=w(0),o=0;if(!(i=v[n+24>>2]))return 1;e=Pn(r,i,f),p[i+60>>2]=p[i+64>>2],p[i+76>>2]=p[i+80>>2];r:{if((t=p[n+96>>2])>w(0)&&(u=p[n+100>>2])<=t){if(u!=w(0)&p[i+108>>2]!=w(0))break r;return v[n+24>>2]=v[i+24>>2],(o=v[i+24>>2])&&(v[o+28>>2]=n),p[n+104>>2]=p[i+104>>2],rt(v[r+72>>2],i),e}p[i+72>>2]=w(f*p[i+88>>2])+p[i+72>>2],p[n+96>>2]=t+f,e=0}return e}function En(r,n,f){return v[r+8>>2]=f,v[r+4>>2]=n,v[r>>2]=10912,f=v[f+8>>2],v[r+28>>2]=1065353216,v[r+32>>2]=1065353216,v[r+20>>2]=1065353216,v[r+24>>2]=1065353216,v[r+16>>2]=9404,v[r+12>>2]=f,mi(r+16|0),v[r+40>>2]=0,v[r+44>>2]=0,v[r+36>>2]=9404,v[r+48>>2]=0,v[r+52>>2]=0,mi(r+36|0),n=l[n+64|0],v[r+84>>2]=0,v[r+76>>2]=0,v[r+80>>2]=0,v[r+72>>2]=8796,v[r+68>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,e[r+56|0]=n,hn(r),r}function On(r,n){var f,i=0,e=0,t=w(0);V=f=V-16|0,(0|(i=v[r+4>>2]))!=v[r+8>>2]?(v[r+4>>2]=i+1,zf(v[r+12>>2]+m(i,12)|0,n)):(i=zf(f+4|0,n),t=w(w(d[r+4>>2])*w(1.75)),n=(n=w(g(t))<w(2147483648)?~~t:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5316],n=0|La[v[v[e>>2]+16>>2]](e,v[r+12>>2],m(n,12),8610,117),v[r+12>>2]=n,e=r,r=v[r+4>>2],v[e+4>>2]=r+1,zf(n+m(r,12)|0,i),Oe(i)),V=f+16|0}function Rn(r,n,f,i){var t,u,o=w(0);V=u=V-16|0,t=Vf(r,n),v[v[r+52>>2]+(n<<2)>>2]=f,t&&(i&&(n=v[r+72>>2],Tt(u,1,t,0),Xr(n+4|0,u)),v[f+24>>2]=t,v[t+28>>2]=f,v[f+96>>2]=0,v[t+24>>2]&&(o=p[t+100>>2])>w(0)&&(o=w(p[t+96>>2]/o),p[f+104>>2]=p[f+104>>2]*(o>w(1)?w(1):o)),v[t+152>>2]=0),n=v[r+72>>2],V=r=V-16|0,Tt(r,0,f,0),Xr(n+4|0,r),e[v[n+20>>2]+88|0]=1,V=r+16|0,V=u+16|0}function Sn(r,n,f){var i=0,t=0,u=w(0),o=0,a=0,c=0;if(i=v[r+4>>2],v[r+4>>2]=n,(t=v[r+8>>2])>>>0<n>>>0&&(u=w(w(n>>>0)*w(1.75)),o=w(g(u))<w(2147483648)?~~u:-2147483648,n=(n=t?o:n)>>>0<=8?8:n,v[r+8>>2]=n,t=v[5316],a=r,c=0|La[v[v[t>>2]+16>>2]](t,v[r+12>>2],n,8610,89),v[a+12>>2]=c,n=v[r+4>>2]),n>>>0>i>>>0)for(r=v[r+12>>2];e[r+i|0]=l[0|f],(0|(i=i+1|0))!=(0|n););}function Wn(){B(21680,6248),K(21789,4530,1,1,0),O(22464,3707,1,-128,127),O(22465,3700,1,-128,127),O(22466,3698,1,0,255),O(22467,1492,2,-32768,32767),O(21707,1483,2,0,65535),O(21687,1793,4,-2147483648,2147483647),O(21697,1784,4,0,-1),O(22468,4850,4,-2147483648,2147483647),O(21681,4841,4,0,-1),Gu(22469,2407,-2147483648,2147483647),Gu(22470,2406,0,-1),M(21686,2351,4),M(22471,5826,8),J(21830,4565)}function Gn(r,n,f){var i=0,e=0;r:{if(i=v[r+4>>2])for(;;){if(Ke(i+4|0,n))break r;if(!(i=v[i+20>>2]))break}return i=ht(28),v[i>>2]=8908,Vt(i+4|0,0,0),v[i+20>>2]=0,v[i+24>>2]=0,e=v[n+8>>2],v[i+8>>2]=v[n+4>>2],v[i+12>>2]=e,p[i+16>>2]=p[f>>2],(n=v[r+4>>2])&&(v[n+24>>2]=i,v[i+20>>2]=n),v[r+4>>2]=i,void(v[r+8>>2]=v[r+8>>2]+1)}r=v[n+8>>2],v[i+8>>2]=v[n+4>>2],v[i+12>>2]=r,p[i+16>>2]=p[f>>2]}function Un(r,n){var f=0,i=0,e=0,t=0,u=0;for(r=r+4|0;;){if(r=v[r>>2]){if(!n|!(i=v[r+28>>2]))f=-1,n>>>0>i>>>0||(f=(0|n)!=(0|i));else{e=n,u=0;r:if(f=l[0|i]){for(;;){if((t=l[0|e])&&((0|f)==(0|t)||(0|Ro(f))==(0|Ro(t)))){if(e=e+1|0,f=l[i+1|0],i=i+1|0,f)continue;break r}break}u=f}f=Ro(255&u)-Ro(l[0|e])|0}if(f)continue}break}return r}function jn(r){var n=0,f=0,i=0,e=0,t=0,u=0,o=0,a=0;if((0|(f=v[r+4>>2]))>0)for(e=f;;){if(t=e-1|0,(n=v[v[r+12>>2]+(t<<2)>>2])&&(La[v[v[n>>2]+4>>2]](n),f=v[r+4>>2]),f=f-1|0,v[r+4>>2]=f,f>>>0>(n=t)>>>0)for(;i=v[r+12>>2],o=v[(u=i+(n<<2)|0)>>2],a=i,i=(n=n+1|0)<<2,v[u>>2]=v[a+i>>2],v[i+v[r+12>>2]>>2]=o,(0|n)!=(0|f););if(n=(0|e)>1,e=t,!n)break}}function Hn(r,n){var f,i,e,t=0;if(V=i=V-16|0,f=Qo(r),v[f+24>>2]=9244,v[f+8>>2]=8796,r=0,v[f+4>>2]=0,v[f>>2]=9216,v[f+36>>2]=0,v[f+28>>2]=0,v[f+32>>2]=0,v[f+20>>2]=0,v[f+12>>2]=0,v[f+16>>2]=0,Oa(t=f+8|0,n),Qi(e=f+24|0,n),v[i+4>>2]=0,An(t,n,i+4|0),(0|n)>0)for(;On(e,t=Ut(i+4|0)),Oe(t),(0|n)!=(0|(r=r+1|0)););return V=i+16|0,f}function Ln(r,n){var f=0,i=w(0),e=0;if((0|(f=v[r+4>>2]))==v[r+8>>2])return e=v[n>>2],i=w(w(f>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,f=v[5316],n=0|La[v[v[f>>2]+16>>2]](f,v[r+12>>2],n<<2,8610,117),v[r+12>>2]=n,f=r,r=v[r+4>>2],v[f+4>>2]=r+1,void(v[n+(r<<2)>>2]=e);v[r+4>>2]=f+1,v[v[r+12>>2]+(f<<2)>>2]=v[n>>2]}function Mn(r,n){var f=0,i=w(0),e=w(0);(0|(f=v[r+4>>2]))!=v[r+8>>2]?p[v[r+12>>2]+(f<<2)>>2]=p[n>>2]:(e=p[n>>2],i=w(w(f>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,f=v[5316],n=0|La[v[v[f>>2]+16>>2]](f,v[r+12>>2],n<<2,8610,117),v[r+12>>2]=n,f=v[r+4>>2],p[n+(f<<2)>>2]=e),v[r+4>>2]=f+1}function _n(r,n){var f=0,i=w(0),e=0;(0|(f=v[r+4>>2]))!=v[r+8>>2]?v[v[r+12>>2]+(f<<2)>>2]=v[n>>2]:(e=v[n>>2],i=w(w(f>>>0)*w(1.75)),n=(n=w(g(i))<w(2147483648)?~~i:-2147483648)>>>0<=8?8:n,v[r+8>>2]=n,f=v[5316],n=0|La[v[v[f>>2]+16>>2]](f,v[r+12>>2],n<<2,8610,117),v[r+12>>2]=n,f=v[r+4>>2],v[n+(f<<2)>>2]=e),v[r+4>>2]=f+1}function zn(r,n){var f,i=0,t=0;return f=v[r+4>>2],v[r+4>>2]=f+1,t=127&(i=e[0|f]),(0|i)>=0||(v[r+4>>2]=f+2,t|=(i=e[f+1|0])<<7&16256,(0|i)>=0||(v[r+4>>2]=f+3,t|=(i=e[f+2|0])<<14&2080768,(0|i)>=0||(v[r+4>>2]=f+4,t|=(i=e[f+3|0])<<21&266338304,(0|i)>=0||(v[r+4>>2]=f+5,t=l[f+4|0]<<28|t)))),n?t:0-(1&t)^t>>>1}function xn(r,n){r:if((0|n)>=1024){if(r*=898846567431158e293,n>>>0<2047){n=n-1023|0;break r}r*=898846567431158e293,n=((0|n)>=3069?3069:n)-2046|0}else(0|n)>-1023||(r*=2004168360008973e-307,n>>>0>4294965304?n=n+969|0:(r*=2004168360008973e-307,n=((0|n)<=-2960?-2960:n)+1938|0));return u(0,0),u(1,n+1023<<20),r*+o()}function Jn(r,n,f){r|=0,n|=0,f|=0;var i,e=w(0);V=i=V-16|0,Vt(i+4|0,n,f);r:{n:{f:{if(f=v[r+16>>2])for(;;){if(Ke(f+4|0,i+4|0))break f;if(!(f=v[f+20>>2]))break}r=r+8|0;break n}if(!(f=v[r+16>>2]))break r;for(;;){if(!Ke(f+4|0,i+4|0)){if(f=v[f+20>>2])continue;break r}break}r=f+16|0}e=p[r>>2]}return V=i+16|0,w(e)}function Kn(r,n,f){var i=0,e=0;if(n&&xe(0|La[v[v[n>>2]+8>>2]](n),21356))if(e=v[n+24>>2])for(f=0;;){if(i=f<<2,(f=f+1|0)>>>0<(i=f+v[i+v[n+32>>2]>>2]|0)>>>0){for(;ce(r,v[v[r+20>>2]+(v[v[n+32>>2]+(f<<2)>>2]<<2)>>2]),(0|i)!=(0|(f=f+1|0)););f=i}if(!(f>>>0<e>>>0))break}else ce(r,f)}function Bn(r,n){var f=0,i=0,e=0,t=0,u=0;if(v[r>>2]=12960,v[r+4>>2]=v[n+4>>2],f=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=f,f&&(i=v[5316],t=r,u=0|La[v[v[i>>2]+12>>2]](i,f<<2,8610,221),v[t+12>>2]=u,i=v[r+4>>2]))for(f=0;v[(e=f<<2)+v[r+12>>2]>>2]=v[v[n+12>>2]+e>>2],(0|i)!=(0|(f=f+1|0)););return r}function Nn(r,n,f){var i=0,t=0;r:{if(i=v[r+4>>2])for(t=v[n>>2];;){if(v[i+4>>2]==(0|t))break r;if(!(i=v[i+12>>2]))break}return i=ht(20),v[i+12>>2]=0,v[i+16>>2]=0,v[i>>2]=8636,v[i+4>>2]=v[n>>2],e[i+8|0]=l[0|f],(n=v[r+4>>2])&&(v[n+16>>2]=i,v[i+12>>2]=n),v[r+4>>2]=i,void(v[r+8>>2]=v[r+8>>2]+1)}v[i+4>>2]=t,e[i+8|0]=l[0|f]}function qn(r,n){var f=0,i=0,e=0,t=0;if(v[r>>2]=9244,v[r+4>>2]=v[n+4>>2],f=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=f,f&&(i=v[5316],e=r,t=0|La[v[v[i>>2]+12>>2]](i,m(f,12),8610,221),v[e+12>>2]=t,v[r+4>>2]))for(f=0;zf((i=m(f,12))+v[r+12>>2]|0,i+v[n+12>>2]|0),(f=f+1|0)>>>0<d[r+4>>2];);return r}function Dn(r,n){var f=0,i=0,e=0,t=0,u=0;if(v[r>>2]=10440,v[r+4>>2]=v[n+4>>2],f=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=f,f&&(i=v[5316],t=r,u=0|La[v[v[i>>2]+12>>2]](i,f<<2,8610,221),v[t+12>>2]=u,i=v[r+4>>2]))for(f=0;v[(e=f<<2)+v[r+12>>2]>>2]=v[v[n+12>>2]+e>>2],(0|i)!=(0|(f=f+1|0)););}function Vn(r,n){var f=0,i=0,e=0,t=0;for(v[n>>2]=0;;){r:{n:{f:{if(((i=l[r+e|0])-58&255)>>>0<=245){if(i-65>>>0<6|i-97>>>0<6)break f;return void(v[n>>2]=0)}f<<=4,t=-48;break n}if(f<<=4,v[n>>2]=f,t=-55,!((i-65&255)>>>0<6)&&(t=-87,(i-97&255)>>>0>5))break r}f|=(i<<24>>24)+t,v[n>>2]=f}if(4==(0|(e=e+1|0)))break}}function Zn(r,n){n|=0;var f=0;v[100+(r|=0)>>2]=n,n&&(v[r+24>>2]=0,ee(r+20|0,n+20|0),v[r+40>>2]=0,te(r+36|0,n+36|0),f=v[n+52>>2],v[r+124>>2]=0,v[r+52>>2]=f,te(r+120|0,n+120|0),v[r+140>>2]=0,mn(r+136|0,n+136|0),f=v[n+224>>2],v[r+156>>2]=0,v[r+224>>2]=f,mn(r+152|0,n+152|0),p[r+196>>2]=p[n+196>>2],p[r+200>>2]=p[n+200>>2])}function Yn(r,n,f,i,e){var t,u,o,a,c=0,b=0;if(v[r+4>>2]=0,v[r>>2]=17032,Ut(r+8|0),o=Ut(r+20|0),t=Jt(16),v[r+4>>2]=t,u=Jt((a=xf(n,0,24,0),Z?-1:a)),n)for(b=m(n,24)+u|0,c=u;v[c+20>>2]=0,(0|b)!=(0|(c=c+24|0)););return v[t+8>>2]=n,v[t>>2]=u,v[t+12>>2]=i,v[t+4>>2]=f,sf(o,e),r}function Xn(r,n){var f=0,i=0,e=0,t=0;if(v[r>>2]=8796,v[r+4>>2]=v[n+4>>2],f=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=f,f&&(i=v[5316],f=0|La[v[v[i>>2]+12>>2]](i,f<<2,8610,221),v[r+12>>2]=f,i=v[r+4>>2]))for(t=v[n+12>>2],n=0;p[f+(e=n<<2)>>2]=p[t+e>>2],(0|i)!=(0|(n=n+1|0)););return r}function Qn(r,n){var f=0,i=0,e=0,t=0;if(v[r>>2]=8764,v[r+4>>2]=v[n+4>>2],f=v[n+8>>2],v[r+12>>2]=0,v[r+8>>2]=f,f&&(i=v[5316],f=0|La[v[v[i>>2]+12>>2]](i,f<<2,8610,221),v[r+12>>2]=f,i=v[r+4>>2]))for(t=v[n+12>>2],n=0;v[f+(e=n<<2)>>2]=v[t+e>>2],(0|i)!=(0|(n=n+1|0)););return r}function rf(r){var n=0,f=0,i=0,e=0;f=v[r+4>>2];r:{n:if(n=v[r>>2],_o(l[0|n])){for(;;){if(n>>>0>=f>>>0)break n;if(i=n+1|0,v[r>>2]=i,e=l[n+1|0],n=i,!_o(e))break}break r}i=n}if((0|f)!=(0|i)){for(;f=(n=f)-1|0,v[r+4>>2]=f,!(f>>>0<i>>>0||13!=l[0|f]););v[r+4>>2]=n}}function nf(r,n,f,i){return v[r+4>>2]=n,v[r>>2]=10928,zf(r+8|0,f),v[r+36>>2]=1065353216,v[r+40>>2]=1065353216,v[r+28>>2]=1065353216,v[r+32>>2]=1065353216,v[r+24>>2]=9404,v[r+20>>2]=i,mi(r+24|0),v[r+48>>2]=0,v[r+52>>2]=0,v[r+44>>2]=9404,v[r+56>>2]=0,v[r+60>>2]=0,mi(r+44|0),e[r+64|0]=0,Ut(r+68|0),v[r+80>>2]=0,r}function ff(r,n,f,i){var e,t=0,u=0,o=0;return V=e=V-16|0,o=1,(t=Vf(r,n))?p[t+80>>2]!=w(-1)?Wi(r,t):(v[v[r+52>>2]+(n<<2)>>2]=v[t+24>>2],u=v[r+72>>2],o=0,Tt(e,1,t,0),Xr(u+4|0,e),rt(v[r+72>>2],t),Wi(r,t),t=v[t+24>>2]):t=0,Rn(r,u=n,n=an(r,n,f,i,t),o),mr(v[r+72>>2]),V=e+16|0,n}function ef(r,n){n|=0;var f=0,i=0;if(!(d[44+(r|=0)>>2]<=n>>>0)&&(f=v[v[r+52>>2]+(n<<2)>>2])){if(rt(v[r+72>>2],f),Wi(r,f),n=v[f+24>>2])for(i=f;rt(v[r+72>>2],n),v[i+24>>2]=0,v[i+28>>2]=0,i=n,n=v[n+24>>2];);v[v[r+52>>2]+(v[f+32>>2]<<2)>>2]=0,mr(v[r+72>>2])}}function tf(r,n,f){var i=0,e=0,t=0;if(!(d[r+8>>2]<=n>>>0)&&(e=v[r+16>>2]+(n<<4)|0,v[e+4>>2])){r:{for(;;){if(se(4+(v[e+12>>2]+m(i,20)|0)|0,f))break r;if(!((i=i+1|0)>>>0<d[e+4>>2]))break}return 0}(0|i)<0||(t=v[16+(v[12+(v[r+16>>2]+(n<<4)|0)>>2]+m(i,20)|0)>>2])}return t}function uf(r,n,f,i,e,t){r|=0,n=w(n),f=w(f),i=w(i),e=w(e),t|=0;var u=0;r=v[r+112>>2],t?(p[r+28>>2]=f,p[r+24>>2]=n,p[r+20>>2]=e,p[r+16>>2]=n,p[r+4>>2]=f,p[r>>2]=i,u=r+8|0,t=3):(p[r+28>>2]=f,p[r+24>>2]=i,p[r+20>>2]=f,p[r+16>>2]=n,p[r+12>>2]=e,p[r+8>>2]=n,u=r,t=1),p[u>>2]=i,p[r+(t<<2)>>2]=e}function of(r,n,f,i){var e,t,u=w(0),o=w(0),a=w(0);return f=v[f+12>>2],i=v[i+12>>2],e=f+(v[i+((r+1|0)%(0|n)<<2)>>2]<<3)|0,t=f+(v[i+(r<<2)>>2]<<3)|0,u=p[t+4>>2],r=f+(v[i+(((r+n|0)-1|0)%(0|n)<<2)>>2]<<3)|0,o=p[r+4>>2],a=p[e+4>>2],!(w(w(p[e>>2]*w(u-o))+w(w(p[r>>2]*w(a-u))+w(p[t>>2]*w(o-a))))>=w(0))}function af(r,n,f,i){return v[r+4>>2]=n,v[r>>2]=9308,zf(r+8|0,f),v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=i,v[r+32>>2]=0,v[r+36>>2]=0,v[r+48>>2]=0,v[r+52>>2]=0,v[r+40>>2]=1065353216,v[r+44>>2]=1065353216,e[r+53|0]=0,e[r+54|0]=0,e[r+55|0]=0,e[r+56|0]=0,e[r+57|0]=0,e[r+58|0]=0,e[r+59|0]=0,e[r+60|0]=0,r}function cf(r){var n=0,f=0,i=0;r:{n:if(3&(n=r)){if(!l[0|r])return 0;for(;;){if(!(3&(n=n+1|0)))break n;if(!l[0|n])break}break r}for(;f=n,n=n+4|0,!(~(i=v[f>>2])&i-16843009&-2139062144););for(;f=(n=f)+1|0,l[0|n];);}return n-r|0}function bf(r,n){var f,i,e=0,t=0,u=0,o=0,a=0;if(V=f=V-16|0,i=zn(r,1),s[f+14>>1]=0,$n(n,i,f+14|0),(0|i)>0)for(e=v[r+4>>2],a=v[n+12>>2];v[r+4>>2]=e+1,u=(t<<1)+a|0,o=l[0|e]<<8,s[u>>1]=o,n=e+2|0,v[r+4>>2]=n,s[u>>1]=l[e+1|0]|o,e=n,(0|(t=t+1|0))!=(0|i););V=f+16|0}function kf(r,n,f){var i=0,e=0,t=w(0),u=w(0),o=w(0);return e=xf(v[5618],v[5619],1284865837,1481765933),i=Z,i=(e=e+1|0)?i:i+1|0,v[5618]=e,v[5619]=i,t=w(w(i>>>1|0)*w(4.656612873077393e-10)),o=w(f-r),u=w(n-r),t<=w(o/u)?w(w(T(w(o*w(u*t))))+r):w(n-w(T(w(w(n-f)*w(u*w(w(1)-t))))))}function sf(r,n){var f=0,i=0;if((0|r)!=(0|n)){if((f=v[r+8>>2])&&(i=v[5316],La[v[v[i>>2]+20>>2]](i,f,8610,106)),!v[n+8>>2])return v[r+4>>2]=0,void(v[r+8>>2]=0);v[r+4>>2]=v[n+4>>2],f=r,r=v[5316],r=0|La[v[v[r>>2]+12>>2]](r,v[n+4>>2]+1|0,8610,113),v[f+8>>2]=r,Me(r,v[n+8>>2],v[n+4>>2]+1|0)}}function vf(r,n,f){var i=0,e=0;if((0|(i=v[r>>2]))==(0|n))return 0;v[f>>2]=i,e=n;r:if((0|(i=v[r>>2]))!=(0|n)){for(;;){if(e=i,10==l[0|i])break r;if(i=i+1|0,v[r>>2]=i,(0|n)==(0|i))break}e=n}return v[f+4>>2]=e,rf(f),(0|(f=n))!=(0|(n=v[r>>2]))&&(v[r>>2]=n+1),1}function lf(r,n,f){var i,t,u=0,o=0;if(V=i=V-272|0,t=Cn(i+16|0,n),f){u=cf(n),n=cf(n=u+t|0)+n|0;r:if(u=255-u|0)for(;;){if(!(o=l[0|f]))break r;if(e[0|n]=o,n=n+1|0,f=f+1|0,!(u=u-1|0))break}e[0|n]=0}sf(n=r+24|0,r=$t(i+4|0,t,0)),Oe(r),V=i+272|0}function hf(r){var n=0,f=0,i=0;if(v[44+(r|=0)>>2]=0,f=v[r+28>>2]){for(i=r+40|0;_n(i,v[r+36>>2]+(n<<2)|0),(0|f)!=(0|(n=n+1|0)););if(f=v[r+28>>2])for(n=0;hn(v[v[r+36>>2]+(n<<2)>>2]),(0|f)!=(0|(n=n+1|0)););}}function df(r){var n=0,f=0,i=0;if(v[(r|=0)>>2]=8924,v[r+36>>2]&&(f=v[r+8>>2]))for(;i=v[r+36>>2],La[v[v[i>>2]+12>>2]](i,v[v[v[r+16>>2]+(n<<2)>>2]+8>>2]),(0|f)!=(0|(n=n+1|0)););return jn(n=r+4|0),jn(f=r+20|0),Ao(f),$o(n),0|r}function pf(r,n){var f=0,i=0,e=0;r:if(f=v[r+4>>2]){for(i=r+4|0,n=v[n>>2];;){if((0|n)!=v[f+4>>2]){if(f=v[f+12>>2])continue;break r}break}e=(n=v[f+16>>2])?n+12|0:i,i=v[f+12>>2],v[e>>2]=i,i&&(v[i+16>>2]=n),La[v[v[f>>2]+4>>2]](f),v[r+8>>2]=v[r+8>>2]-1}}function yf(r){var n=0,f=0;if(v[(r|=0)>>2]=9244,n=v[r+4>>2])for(;n=v[r+12>>2]+m(~f+n|0,12)|0,La[v[v[n>>2]>>2]](n),(n=v[r+4>>2])>>>0>(f=f+1|0)>>>0;);return v[r+4>>2]=0,(n=v[r+12>>2])&&(f=v[5316],La[v[v[f>>2]+20>>2]](f,n,8610,230)),0|r}function mf(r,n,f,i,e){n|=0,f|=0,i|=0,e|=0;var t,u,o=0;return V=t=V-32|0,n=((o=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&o?v[v[n>>2]+r>>2]:r,o=n,u=f,n=Sf(t+20|0,i+4|0,v[i>>2],0),f=Sf(t+8|0,e+4|0,v[e>>2],0),r=0|La[0|r](o,u,n,f),Oe(f),Oe(n),V=t+32|0,0|r}function wf(r,n){var f=0,i=0;if((0|(f=v[r+8>>2]))!=(0|n)){if(f&&(i=v[5316],La[v[v[i>>2]+20>>2]](i,f,8610,122)),!n)return v[r+4>>2]=0,void(v[r+8>>2]=0);f=cf(n),v[r+4>>2]=f,i=v[5316],f=0|La[v[v[i>>2]+12>>2]](i,f+1|0,8610,129),v[r+8>>2]=f,Me(f,n,v[r+4>>2]+1|0)}}function gf(r){var n=0,f=0;if(v[(r|=0)>>2]=10880,n=v[r+4>>2])for(;Oe(4+(v[r+12>>2]+m(~f+n|0,20)|0)|0),(f=f+1|0)>>>0<(n=v[r+4>>2])>>>0;);return v[r+4>>2]=0,(n=v[r+12>>2])&&(f=v[5316],La[v[v[f>>2]+20>>2]](f,n,8610,230)),0|r}function Ff(r,n){var f=0;if(v[r>>2]=n,n=v[r+4>>2])for(;n=v[r+12>>2]+(~f+n<<4)|0,La[v[v[n>>2]>>2]](n),(n=v[r+4>>2])>>>0>(f=f+1|0)>>>0;);return v[r+4>>2]=0,(n=v[r+12>>2])&&(f=v[5316],La[v[v[f>>2]+20>>2]](f,n,8610,230)),r}function Af(r,n){var f;return V=f=V-16|0,r=Qo(r),v[r+20>>2]=8844,v[r+4>>2]=8796,v[r>>2]=9588,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[f+12>>2]=0,An(r+4|0,n,f+12|0),v[f+8>>2]=0,In(r+20|0,n,f+8|0),V=f+16|0,r}function Tf(r,n){var f,i,e=0,t=0;V=f=V-16|0,v[f+12>>2]=n;r:{if(i=v[r+8>>2]){if(t=v[r+16>>2],v[t>>2]==(0|n))break r;for(;(0|i)!=(0|(e=e+1|0))&v[(e<<2)+t>>2]!=(0|n););if(e>>>0<i>>>0)break r}_n(r+4|0,f+12|0)}V=f+16|0}function $f(r,n){var f,i=w(0);return V=f=V-16|0,i=w(-1),cf(r)>>>1>>>0<=n>>>0||(r=(n<<1)+r|0,e[f+13|0]=l[0|r],r=l[r+1|0],e[f+15|0]=0,e[f+14|0]=r,r=gr(f+13|0,f+8|0,16,-1),l[v[f+8>>2]]||(i=w(w(0|r)/w(255)))),V=f+16|0,i}function If(r,n,f,i){return r=ra(r),v[r+16>>2]=9292,v[r+12>>2]=i,v[r+8>>2]=f,v[r+4>>2]=n,v[r>>2]=9260,Ze(r+20|0,0,69),s[r+116>>1]=0,v[r+108>>2]=1065353216,v[r+112>>2]=0,v[r+100>>2]=0,v[r+104>>2]=0,v[r+92>>2]=1065353216,v[r+96>>2]=0,ni(r),r}function Cf(r,n,f,i,e){var t=w(0),u=w(0),o=w(0),a=0,c=w(0);i=v[i+12>>2]+(e<<2)|0,n=v[n+12>>2]+(f<<2)|0,u=p[n+12>>2],o=p[n+8>>2],t=Rr(w(u-p[n+4>>2]),w(o-p[n>>2])),p[i+8>>2]=t,a=i,c=w(u+w(r*Hr(t))),p[a+4>>2]=c,a=i,c=w(o+w(r*Ur(t))),p[a>>2]=c}function Pf(r,n,f,i,e){r|=0,n|=0,f|=0,i|=0,e=w(e);var t,u=0;V=t=V-32|0,n=((u=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=Sf(t+20|0,f+4|0,v[f>>2],0),f=Sf(t+8|0,i+4|0,v[i>>2],0),La[0|r](u,n,f,e),Oe(f),Oe(n),V=t+32|0}function Ef(r){var n=0,f=0;for(n=v[r+4>>2];n&&(f=v[n>>2],Ef(n),ar(n),n=f););return(n=v[r+16>>2])&&(f=v[5316],La[v[v[f>>2]+20>>2]](f,n,8610,164)),(n=v[r+28>>2])&&(f=v[5316],La[v[v[f>>2]+20>>2]](f,n,8610,168)),r}function Of(r,n){return v[r>>2]=10848,zf(r+4|0,n),v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=10864,v[r+16>>2]=10832,v[r- -64>>2]=0,v[r+56>>2]=0,v[r+60>>2]=0,v[r+52>>2]=10896,v[r+48>>2]=0,v[r+40>>2]=0,v[r+44>>2]=0,v[r+36>>2]=9708,r}function Rf(r,n){return r=Et(r,n),v[r+36>>2]=8796,v[r+20>>2]=10816,v[r>>2]=11208,v[r+40>>2]=0,v[r+44>>2]=0,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+48>>2]=0,v[r+52>>2]=0,v[r+56>>2]=r,n=v[5402],v[5402]=n+1,v[r+60>>2]=n<<11&134215680,r}function Sf(r,n,f,i){return v[r>>2]=10944,n?(v[r+4>>2]=f,i?(v[r+8>>2]=n,r):(i=v[5316],f=0|La[v[v[i>>2]+12>>2]](i,f+1|0,8610,48),v[r+8>>2]=f,e[f+v[r+4>>2]|0]=0,Me(v[r+8>>2],n,v[r+4>>2]),r)):(v[r+4>>2]=0,v[r+8>>2]=0,r)}function Wf(r,n,f){var i=0,e=0,t=0;if(!f)return 0;r:if(i=l[0|r]){for(;;){if(!((0|(e=l[0|n]))!=(0|i)|!e)&&(f=f-1|0)){if(n=n+1|0,i=l[r+1|0],r=r+1|0,i)continue;break r}break}t=i}return(255&t)-l[0|n]|0}function Gf(r,n){var f,i,e,t,u,o=0;return o=(f=v[r+4>>2])+(i=v[n+4>>2])|0,v[r+4>>2]=o,u=v[n+8>>2],e=v[5316],t=v[r+8>>2],o=0|La[v[v[e>>2]+16>>2]](e,t,o+1|0,8610,150),v[r+8>>2]=o,Me(o+f|0,(0|u)==(0|t)?o:v[n+8>>2],i+1|0),r}function Uf(r,n,f,i){var e=w(0),t=w(0),u=w(0),o=0,a=w(0);n=v[n+12>>2],t=p[n+4>>2],f=v[f+12>>2]+(i<<2)|0,u=p[n>>2],e=Rr(w(p[n+12>>2]-t),w(p[n+8>>2]-u)),p[f+8>>2]=e,o=f,a=w(t+w(r*Hr(e))),p[o+4>>2]=a,o=f,a=w(u+w(r*Ur(e))),p[o>>2]=a}function jf(r,n,f,i){n|=0,f|=0,i|=0;var e,t=0;V=e=V-32|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=Sf(e+20|0,f+4|0,v[f>>2],0),f=Sf(e+8|0,i+4|0,v[i>>2],0),La[0|r](t,n,f),Oe(f),Oe(n),V=e+32|0}function Hf(r){var n=0;r:{n:if(n=Un(r,4381)){if(r=v[n+16>>2])return!vi(r,5008);r=1;f:switch(v[n+8>>2]){case 0:case 2:return 0;case 1:break r;case 3:break f;default:break n}return p[n+24>>2]!=w(0)}r=0}return r}function Lf(r,n,f){var i=0,e=0,t=0,u=0,o=0;if(i=(v[r+4>>2]/(0|f)|0)-2|0){for(o=v[r+12>>2],r=0,e=i;e=(r=(u=p[(m(e=1+(t=e>>>1|0)|0,f)<<2)+o>>2]<=n)?e:r)+(i=u?i:t)|0,(0|r)!=(0|i););f=m(r+1|0,f)}return f}function Mf(r){var n,f=0;return v[64+(r|=0)>>2]=9868,v[r>>2]=9844,Oe(r+168|0),jo(r+152|0),jo(r+136|0),Lo(r+120|0),Lo(r+104|0),v[r+64>>2]=8748,(n=v[r+72>>2])&&(f=v[r+68>>2])&&La[0|n](f),0|Ft(r)}function _f(r,n,f,i,e,t,u,o,a,c){r|=0,n|=0,f=w(f),i=w(i),e=w(e),t=w(t),u=w(u),o=w(o),a=w(a),c=w(c),r=v[r+32>>2]+(n<<5)|0,p[r>>2]=f,p[r+4>>2]=i,p[r+8>>2]=e,p[r+12>>2]=t,p[r+16>>2]=u,p[r+20>>2]=o,p[r+24>>2]=a,p[r+28>>2]=c}function zf(r,n){var f=0;return v[r>>2]=10944,v[n+8>>2]?(v[r+4>>2]=v[n+4>>2],f=v[5316],f=0|La[v[v[f>>2]+12>>2]](f,v[n+4>>2]+1|0,8610,67),v[r+8>>2]=f,Me(f,v[n+8>>2],v[n+4>>2]+1|0),r):(v[r+4>>2]=0,v[r+8>>2]=0,r)}function xf(r,n,f,i){var e,t,u,o,a=0,c=0;return o=m(a=f>>>16|0,c=r>>>16|0),a=(65535&(c=((u=m(e=65535&f,t=65535&r))>>>16|0)+m(c,e)|0))+m(a,t)|0,Z=(m(n,f)+o|0)+m(r,i)+(c>>>16)+(a>>>16)|0,65535&u|a<<16}function Jf(r){var n,f=0,i=0;if(v[(r|=0)>>2]=10392,n=v[r+44>>2])for(i=r+4|0;Tf(i,v[v[r+52>>2]+(f<<2)>>2]),(0|n)!=(0|(f=f+1|0)););return v[r+44>>2]=0,yo(r+40|0),Vu(r+24|0),yt(r+4|0),0|r}function Kf(r,n){var f,i=0,e=0,t=0,u=0;if(!(i=v[r+4>>2]-2|0))return 1;for(f=v[r+12>>2],r=0,e=i;e=(r=(u=p[((e=1+(t=e>>>1|0)|0)<<2)+f>>2]<=n)?e:r)+(i=u?i:t)|0,(0|r)!=(0|i););return r+1|0}function Bf(r){var n,f,i,e,t;return v[(r|=0)>>2]=10248,jn(n=r+8|0),jn(f=r+24|0),jn(i=r+56|0),jn(e=r+72|0),jn(t=r+88|0),Uo(r+120|0),zu(r+104|0),Ku(t),Bu(e),Nu(i),ko(r+40|0),ko(f),Uo(n),0|r}function Nf(r,n){var f=0;if(v[r>>2]=0,v[r+4>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+16>>2]=0,v[r+20>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,n){for(;n=(f=n)+1|0,(l[0|f]-1&255)>>>0<32;);ur(r,f)}return r}function qf(r,n){var f,i;return V=f=V-16|0,r=Fi(r,n),v[r+20>>2]=8796,v[r>>2]=11100,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,Oa(i=r+20|0,n=m(n,3)),v[f+12>>2]=0,An(i,n,f+12|0),V=f+16|0,r}function Df(r,n,f,i){n|=0,f|=0,i|=0;var e,t=0;return V=e=V-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=Sf(e+4|0,i+4|0,v[i>>2],0),r=0|La[0|r](t,f,n),Oe(n),V=e+16|0,0|r}function Vf(r,n){var f,i=0,e=0;if(V=f=V-16|0,d[r+44>>2]<=n>>>0)for(i=r+40|0;v[f+12>>2]=0,_n(i,f+12|0),d[r+44>>2]<=n>>>0;);else e=v[v[r+52>>2]+(n<<2)>>2];return V=f+16|0,e}function Zf(r,n){var f,i;return V=f=V-16|0,r=Fi(r,n),v[r+20>>2]=8796,v[r>>2]=11148,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,Oa(i=r+20|0,n<<=3),v[f+12>>2]=0,An(i,n,f+12|0),V=f+16|0,r}function Yf(r,n,f){var i;if(i=Un(r,4946)){if(v[i+8>>2]==v[2438]&&!vi(v[i+16>>2],6292))return void Pt(n,f);Kr(n,f,Dt(r,4946,w(0)),Dt(r,6954,w(0)),Dt(r,6936,w(1)),Dt(r,6924,w(1)))}}function Xf(r,n,f){n|=0,f|=0;var i,e=0;return V=i=V-16|0,n=((e=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&e?v[v[n>>2]+r>>2]:r,e=n,n=Sf(i+4|0,f+4|0,v[f>>2],0),r=0|La[0|r](e,n),Oe(n),V=i+16|0,0|r}function Qf(r){var n,f,i=0;if(i=v[72+(r|=0)>>2],f=l[i+28|0],e[i+28|0]=1,n=v[r+44>>2]){for(i=0;ef(r,i),(0|n)!=(0|(i=i+1|0)););i=v[r+72>>2]}v[r+44>>2]=0,e[i+28|0]=f,mr(i)}function ri(r,n){n|=0;var f=0,i=0;r:{n:if(v[88+(r|=0)>>2]){for(;;){if(!se(ka(i=v[v[r+96>>2]+(f<<2)>>2]),n)){if((f=f+1|0)>>>0<d[r+88>>2])continue;break n}break}break r}i=0}return 0|i}function ni(r){var n;n=v[4+(r|=0)>>2],p[r+32>>2]=p[n+28>>2],p[r+36>>2]=p[n+32>>2],p[r+40>>2]=p[n+36>>2],p[r+44>>2]=p[n+40>>2],p[r+48>>2]=p[n+44>>2],p[r+52>>2]=p[n+48>>2],p[r+56>>2]=p[n+52>>2]}function fi(r,n,f,i){n|=0,f|=0,i|=0;var e,t=0;V=e=V-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=Sf(e+4|0,i+4|0,v[i>>2],0),La[0|r](t,f,n),Oe(n),V=e+16|0}function ii(r,n){return v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=9032,v[r>>2]=9016,zf(r+16|0,n),Ut(r+28|0),v[r+64>>2]=0,v[r+56>>2]=1,v[r+60>>2]=0,v[r+48>>2]=1,v[r+52>>2]=1,v[r+40>>2]=6,v[r+44>>2]=1,r}function ei(r){var n,f=0;return v[4+(r|=0)>>2]=8668,v[r>>2]=8652,Lo(r+148|0),Oo(r+132|0),Io(r+116|0),v[r+4>>2]=8748,(n=v[r+12>>2])&&(f=v[r+8>>2])&&La[0|n](f),0|r}function ti(){var r,n=0;V=r=V-16|0,l[21040]||(v[5257]=0,v[5258]=0,v[5256]=1048,v[5259]=0,e[21040]=1),l[21092]||(Zr(21044,n=$t(r+4|0,6886,0),21024,w(0)),Oe(n),e[21092]=1),V=r+16|0}function ui(r,n){return r=pt(r,n),v[r+24>>2]=9708,v[r>>2]=9688,v[r+52>>2]=1065353216,v[r+56>>2]=0,e[r+50|0]=0,s[r+48>>1]=0,v[r+44>>2]=1,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,v[r+40>>2]=0,r}function oi(r,n,f,i){var e;return V=e=V-16|0,r=Fi(r,n),v[r+20>>2]=8796,v[r>>2]=i,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,v[e+12>>2]=0,An(r+20|0,m(n,f),e+12|0),V=e+16|0,r}function ai(r,n){n|=0;var f,i=0;if(f=v[24+(r|=0)>>2])for(;;){if(se(v[v[r+32>>2]+(i<<2)>>2]+8|0,n))return v[v[r+32>>2]+(i<<2)>>2];if((0|f)==(0|(i=i+1|0)))break}return 0}function ci(r,n){var f;return V=f=V-16|0,r=Fi(r,n),v[r+24>>2]=8796,v[r+20>>2]=0,v[r>>2]=9376,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[f+12>>2]=0,An(r+24|0,m(n,5),f+12|0),V=f+16|0,r}function bi(r,n){var f;return V=f=V-16|0,r=Fi(r,n),v[r+20>>2]=8796,v[r>>2]=10028,v[r+24>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+36>>2]=0,v[f+12>>2]=0,An(r+20|0,n<<1,f+12|0),V=f+16|0,r}function ki(r,n){var f;return V=f=V-16|0,r=Fi(r,n),v[r+24>>2]=8796,v[r+20>>2]=0,v[r>>2]=10164,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[f+12>>2]=0,An(r+24|0,n<<1,f+12|0),V=f+16|0,r}function si(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e=w(e),t|=0,u|=0,o|=0,r=v[r+32>>2]+m(n,24)|0,p[r>>2]=f,p[r+4>>2]=i,p[r+8>>2]=e,p[r+12>>2]=0|t,p[r+16>>2]=u>>>0,p[r+20>>2]=o>>>0}function vi(r,n){var f=0,i=0;r:if(!(!(f=l[0|r])|(0|(i=l[0|n]))!=(0|f)))for(;;){if(i=l[n+1|0],!(f=l[r+1|0]))break r;if(n=n+1|0,r=r+1|0,(0|f)!=(0|i))break}return f-i|0}function li(r,n){var f=0,i=0;r:if(v[r+4>>2]){for(;;){if(i=v[v[r+12>>2]+(f<<2)>>2],!se(la(v[i+4>>2]),n)){if((f=f+1|0)>>>0<d[r+4>>2])continue;break r}break}return i}return 0}function hi(r,n,f){n|=0,f|=0;var i,e=0;V=i=V-16|0,n=((e=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&e?v[v[n>>2]+r>>2]:r,e=n,n=Sf(i+4|0,f+4|0,v[f>>2],0),La[0|r](e,n),Oe(n),V=i+16|0}function di(r,n){n|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),f=0|La[0|f](n),n=X((r=v[f+4>>2])+4|0),v[n>>2]=r,Me(n+4|0,v[f+8>>2],r),0|n}function pi(r){var n,f=0,i=0;if(n=v[r+4>>2])for(;f=v[v[r+12>>2]+(i<<2)>>2],l[f+117|0]&&(l[f+116|0]&&pi(f+16|0),e[f+116|0]=0),(0|n)!=(0|(i=i+1|0)););}function yi(r,n,f,i){var e;return r|=0,n|=0,f|=0,i|=0,V=e=V-32|0,v[e+28>>2]=n,n=Sf(e+16|0,f+4|0,v[f>>2],0),v[e+12>>2]=i,r=0|La[0|r](e+28|0,n,e+12|0),Oe(n),V=e+32|0,0|r}function mi(r){r|=0;var n=0,f=w(0);return n=r,f=Wu(p[r+4>>2]),p[n+4>>2]=f,n=r,f=Wu(p[r+8>>2]),p[n+8>>2]=f,n=r,f=Wu(p[r+12>>2]),p[n+12>>2]=f,n=r,f=Wu(p[r+16>>2]),p[n+16>>2]=f,0|r}function wi(r,n,f){return v[r+36>>2]=0,v[r+32>>2]=0,v[r+24>>2]=0,v[r+28>>2]=0,v[r+20>>2]=9080,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[r+4>>2]=9064,v[r>>2]=8924,tr(r,n,f,8610,0),r}function gi(r){var n,f=0;return v[(r|=0)>>2]=10344,jn(n=r+8|0),v[r+12>>2]=0,l[r+40|0]&&(f=v[r+4>>2])&&La[v[v[f>>2]+4>>2]](f),Oe(r+24|0),lo(n),0|r}function Fi(r,n){var f;return V=f=V-16|0,r=Qo(r),v[r+4>>2]=8796,v[r>>2]=9440,v[r+16>>2]=0,v[r+8>>2]=0,v[r+12>>2]=0,v[f+12>>2]=0,An(r+4|0,m(n,19)-19|0,f+12|0),V=f+16|0,r}function Ai(r,n){var f=0,i=0;r:if(v[r+4>>2]){for(;;){if(!se(ha(i=v[v[r+12>>2]+(f<<2)>>2]),n)){if((f=f+1|0)>>>0<d[r+4>>2])continue;break r}break}return i}return 0}function Ti(r,n){var f=0,i=0;r:if(v[r+4>>2]){for(;;){if(!se(la(i=v[v[r+12>>2]+(f<<2)>>2]),n)){if((f=f+1|0)>>>0<d[r+4>>2])continue;break r}break}return i}return 0}function $i(r,n,f,i){var e,t;n|=0,f|=0,i|=0,V=e=V-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,v[e+12>>2]=i,La[0|r](n,f,e+12|0),V=e+16|0}function Ii(r,n){var f,i,e;V=f=V-16|0,n=(i=+G(v[n>>2],21664,f+12|0))<4294967296&i>=0?~~i>>>0:0,e=v[f+12>>2],Sf(r,n+4|0,v[n>>2],0),W(0|e),V=f+16|0}function Ci(r,n,f,i,e,t,u,o){r|=0,n|=0,f|=0,i=w(i),e=w(e),t=w(t),u=w(u),o=w(o);var a=0;a=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(a=v[a+v[n>>2]>>2]),La[0|a](n,f,i,e,t,u,o)}function Pi(r){var n,f;return(r=(n=v[5243])+(f=r+7&-8)|0)>>>0<=n>>>0&&f||r>>>0>Ma()<<16>>>0&&!(0|x(0|r))?(v[5488]=48,-1):(v[5243]=r,n)}function Ei(r){var n,f;return n=Jt(32),f=v[r+4>>2],Yn(n,v[f+8>>2],v[f+4>>2],v[f+12>>2],r+20|0),sf(n+8|0,r+8|0),r=v[r+4>>2],Me(v[v[n+4>>2]>>2],v[r>>2],m(v[r+8>>2],24)),n}function Oi(r){var n=0,f=0,i=0,e=0,t=0;if(n=10,i=1,r)for(;i=xf((f=1&r)?n:1,f?e:0,i,t),t=Z,f=r>>>0>1,n=xf(n,e,n,e),e=Z,r=r>>>1|0,f;);return Z=t,i}function Ri(r){var n=0,f=0;if(v[(r|=0)>>2]=17208,n=v[r+4>>2])for(;f=v[n+28>>2],La[v[v[n>>2]+4>>2]](n),n=f;);return v[r+4>>2]=0,v[r+8>>2]=0,0|r}function Si(r){var n=0,f=0;return(n=zn(r,1))?(f=v[5316],f=0|La[v[v[f>>2]+8>>2]](f,n,8610,381),n=n-1|0,f=Me(f,v[r+4>>2],n),v[r+4>>2]=n+v[r+4>>2],e[n+f|0]=0,f):0}function Wi(r,n){var f,i=0,e=0;if(V=f=V-16|0,i=v[n+20>>2])for(;e=v[r+72>>2],Tt(f,4,i,0),Xr(e+4|0,f),i=v[i+20>>2];);v[n+20>>2]=0,V=f+16|0}function Gi(r,n){n|=0;var f=0;f=0;r:if(r=v[24+(r|=0)>>2]){for(;;){if(f=r,v[r+4>>2]==(0|n))break r;if(!(r=v[r+12>>2]))break}f=0}return 0|!!(0|f)}function Ui(r,n,f){n|=0,f|=0;var i=0;if(!(i=v[136+(r|=0)>>2])||!(i=Mt(i,n,f))){if(!v[v[r+4>>2]+64>>2])return 0;i=Mt(v[v[r+4>>2]+64>>2],n,f)}return 0|i}function ji(r,n){var f;n|=0,!(f=v[224+(r|=0)>>2])|v[f+64>>2]!=v[n+4>>2]||(v[r+224>>2]=0,v[r+228>>2]=0,v[r+196>>2]=0,v[r+164>>2]=0,v[r+180>>2]=0,v[r+132>>2]=0)}function Hi(r,n){var f=0,i=0;(0|(f=v[r+8>>2]))!=(0|n)&&(f&&(i=v[5316],La[v[v[i>>2]+20>>2]](i,f,8610,86)),n?f=cf(n):(n=0,f=0),v[r+8>>2]=n,v[r+4>>2]=f)}function Li(r,n){var f,i;return n|=0,V=f=V-16|0,i=f+4|0,La[v[(r|=0)>>2]](i,n),n=X((r=v[f+8>>2])+4|0),v[n>>2]=r,Me(n+4|0,v[f+12>>2],r),Oe(i),V=f+16|0,0|n}function Mi(r){var n,f=0;return v[(r|=0)>>2]=10712,jn(n=r+8|0),l[r+28|0]&&(f=v[r+4>>2])&&La[v[v[f>>2]+4>>2]](f),Oe(r+32|0),lo(n),0|r}function _i(r,n){var f=0;return(f=v[r+8>>2])?(n=f-1|0,f=v[v[r+16>>2]+(n<<2)>>2],v[r+8>>2]=n,f):(r=ht(16),v[r+12>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=n,r)}function zi(r,n){var f,i=0;if(f=v[r+4>>2])for(;;){if(se(la(v[v[v[r+12>>2]+(i<<2)>>2]+4>>2]),n))return i;if((0|f)==(0|(i=i+1|0)))break}return-1}function xi(r,n,f,i){var e;r|=0,n|=0,f|=0,i=w(i),V=e=V-16|0,n=ri(v[r+4>>2],n),f=ri(v[r+4>>2],f),p[e+12>>2]=i,Vt(e,n,f),Gn(r+12|0,e,e+12|0),V=e+16|0}function Ji(r,n,f,i,e,t,u){r|=0,n|=0,f=w(f),i=w(i),e=w(e),t=w(t),u=w(u),r=v[r+36>>2]+m(n,20)|0,p[r>>2]=f,p[r+16>>2]=u,p[r+12>>2]=t,p[r+8>>2]=e,p[r+4>>2]=i}function Ki(r,n){var f=0;if(v[r>>2]=n,n=v[r+4>>2])for(;f=v[n+12>>2],La[v[v[n>>2]+4>>2]](n),n=f;);return v[r+4>>2]=0,v[r+8>>2]=0,r}function Bi(r,n,f,i,e,t,u){r|=0,n|=0,f=w(f),i=w(i),e=w(e),t=w(t),u=w(u),r=v[r+32>>2]+m(n,20)|0,p[r>>2]=f,p[r+16>>2]=u,p[r+12>>2]=t,p[r+8>>2]=e,p[r+4>>2]=i}function Ni(r,n){var f=0;if(v[r>>2]=n,n=v[r+4>>2])for(;f=v[n+20>>2],La[v[v[n>>2]+4>>2]](n),n=f;);return v[r+4>>2]=0,v[r+8>>2]=0,r}function qi(r){var n,f;return v[(r|=0)>>2]=11128,jn(n=r+4|0),jn(f=r+20|0),wt(r+104|0),mt(r+84|0),Io(r+68|0),Yu(r+52|0),Io(r+36|0),wo(f),mo(n),0|r}function Di(r){var n,f;return w((f=(n=r*r)*r)*n*n*(2718311493989822e-21*n-.00019839334836096632)+(f*(.008333329385889463*n-.16666666641626524)+r))}function Vi(r,n,f){var i=0;for(vf(r,n,f),r=v[f+4>>2],n=v[f>>2];;){if((0|r)==(0|n))return;if(i=l[0|n],n=n+1|0,58==(0|i))break}v[f>>2]=n,rf(f)}function Zi(r,n){var f,i=0;if(f=v[r+4>>2])for(;;){if(se(la(v[v[r+12>>2]+(i<<2)>>2]),n))return i;if((0|f)==(0|(i=i+1|0)))break}return-1}function Yi(r){var n,f=0;return v[(r|=0)>>2]=17032,f=v[r+4>>2],(!(n=v[f>>2])||(ar(n),f=v[r+4>>2]))&&ar(f),Oe(r+20|0),Oe(r+8|0),0|r}function Xi(r){return w(w(w(w(r*w(w(r*w(-.008656363002955914))+w(-.04274342209100723)))+w(.16666586697101593))*r)/w(w(r*w(-.7066296339035034))+w(1)))}function Qi(r,n){var f=0,i=0,e=0;d[r+8>>2]<n>>>0&&(v[r+8>>2]=n,f=v[5316],i=r,e=0|La[v[v[f>>2]+16>>2]](f,v[r+12>>2],m(n,12),8610,105),v[i+12>>2]=e)}function re(r,n){var f=0;if(Qi(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;on(r,v[n+12>>2]+m(f,12)|0),(f=f+1|0)>>>0<d[n+4>>2];);}function ne(r,n,f){var i,e,t;V=i=V-16|0,n=v[n>>2],v[i+8>>2]=v[f>>2],f=r,r=0|_(21687,i+8|0),e=f,t=0|U(0|n,0|r),v[e>>2]=t,E(0|r),V=i+16|0}function fe(r,n,f){var i=0,e=0,t=0;d[r+8>>2]<n>>>0&&(v[r+8>>2]=n,i=v[5316],e=r,t=0|La[v[v[i>>2]+16>>2]](i,v[r+12>>2],n<<f,8610,105),v[e+12>>2]=t)}function ie(r){var n=0,f=0;return n=0,(f=r>>>23&255)>>>0<127||(n=2,f>>>0>150||(n=0,(f=1<<150-f)-1&r||(n=r&f?1:2))),n}function ee(r,n){var f=0;if(Oa(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;Ln(r,v[n+12>>2]+(f<<2)|0),(f=f+1|0)>>>0<d[n+4>>2];);}function te(r,n){var f=0;if(Oa(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;Mn(r,v[n+12>>2]+(f<<2)|0),(f=f+1|0)>>>0<d[n+4>>2];);}function ue(r,n){var f=0;if(Oa(r,v[n+4>>2]+v[r+4>>2]|0),v[n+4>>2])for(;_n(r,v[n+12>>2]+(f<<2)|0),(f=f+1|0)>>>0<d[n+4>>2];);}function oe(r,n){return r=Rf(r,n),v[r+64>>2]=8796,v[r>>2]=9900,v[r+68>>2]=0,v[r+72>>2]=0,s[r+74>>1]=0,s[r+76>>1]=0,s[r+78>>1]=0,s[r+80>>1]=0,r}function ae(r,n){var f=0,i=0,e=0;d[r+8>>2]<n>>>0&&(v[r+8>>2]=n,f=v[5316],i=r,e=0|La[v[v[f>>2]+16>>2]](f,v[r+12>>2],n,8610,105),v[i+12>>2]=e)}function ce(r,n){var f,i=0;V=f=V-16|0,l[n+116|0]||((i=v[n+12>>2])&&ce(r,i),e[n+116|0]=1,v[f+12>>2]=n,_n(r+104|0,f+12|0)),V=f+16|0}function be(r){var n;return w((r*=r)*(n=r*r)*(2439044879627741e-20*r-.001388676377460993)+.04166662332373906*n+-.499999997251031*r+1)}function ke(r,n,f,i,e){r|=0,n|=0,f|=0,i=w(i),e=w(e);var t=0;t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),La[0|t](n,f,i,e)}function se(r,n){var f,i;return(0|(f=v[r+8>>2]))==(0|(i=v[n+8>>2]))?1:!(r=!i|!f|v[r+4>>2]!=v[n+4>>2]?1:vi(f,i))}function ve(r,n){var f,i=0;for(i=cf(r)+1|0,f=255&n;n=0,i&&(0|f)!=l[0|(n=(i=i-1|0)+r|0)];);return n}function le(r,n,f){return v[r+12>>2]=0,v[r+16>>2]=0,p[r+8>>2]=n,v[r+4>>2]=f,v[r>>2]=9556,Ut(r+20|0),v[r+32>>2]=1065353216,v[r+36>>2]=0,r}function he(r,n,f,i,e){p[i>>2]=p[r+100>>2]+w(w(n*p[r+92>>2])+w(p[r+96>>2]*f)),p[e>>2]=p[r+112>>2]+w(w(n*p[r+104>>2])+w(p[r+108>>2]*f))}function de(r,n){r|=0,v[24+(n|=0)>>2]=0,ee(n+20|0,r+20|0),v[n+40>>2]=0,te(n+36|0,r+36|0),v[n+52>>2]=v[r+52>>2],v[n+56>>2]=v[r+56>>2]}function pe(r,n,f){var i;r|=0,n=w(n),f=w(f),V=i=V-16|0,p[i+12>>2]=n,p[i+8>>2]=f,La[v[v[r>>2]+16>>2]](r,i+12|0,i+8|0),V=i+16|0}function ye(r,n,f){var i;return r|=0,n=w(n),f=w(f),V=i=V-16|0,p[i+12>>2]=n,p[i+8>>2]=f,r=0|La[0|r](i+12|0,i+8|0),V=i+16|0,0|r}function me(r,n){return v[r>>2]=9572,zf(r+4|0,n),v[r+16>>2]=0,v[r+20>>2]=0,Ut(r+24|0),Ut(r+36|0),v[r+48>>2]=1065353216,v[r+52>>2]=0,r}function we(r,n,f){r|=0,n|=0,f=w(f);var i=0;return i=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),w(w(La[0|i](n,f)))}function ge(r,n,f){n|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),v[La[0|i](n,f)>>2]}function Fe(r,n,f){n|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),w(w(La[0|i](n,f)))}function Ae(r,n,f,i){n|=0,f|=0,i|=0;var e=0;e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),La[0|e](n,f,i)}function Te(r,n,f){n|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),0|La[0|i](n,f)}function $e(r,n){r|=0,n|=0;var f=w(0);v[r+60>>2]!=(0|n)&&(v[r+60>>2]=n,f=p[v[r+12>>2]+160>>2],v[r+76>>2]=0,p[r+68>>2]=f)}function Ie(r,n){n|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),w(w(La[0|f](n)))}function Ce(r){v[224+(r|=0)>>2]&&(v[r+224>>2]=0,v[r+228>>2]=0,v[r+196>>2]=0,v[r+164>>2]=0,v[r+180>>2]=0,v[r+132>>2]=0)}function Pe(r,n,f){var i;r|=0,n|=0,V=i=V-16|0,f=Sf(i+4|0,4+(f|=0)|0,v[f>>2],0),sf(v[r>>2]+n|0,f),Oe(f),V=i+16|0}function Ee(r,n){var f;return r|=0,V=f=V-16|0,n=Sf(f+4|0,4+(n|=0)|0,v[n>>2],0),r=0|La[0|r](n),Oe(n),V=f+16|0,0|r}function Oe(r){var n,f=0;return v[(r|=0)>>2]=10944,(n=v[r+8>>2])&&(f=v[5316],La[v[v[f>>2]+20>>2]](f,n,8610,187)),0|r}function Re(r,n,f,i){var e;n|=0,f|=0,i|=0,V=e=V-16|0,r=v[(r|=0)>>2],v[e+12>>2]=i,La[0|r](n,f,e+12|0),V=e+16|0}function Se(r,n,f){r|=0,n|=0,f=w(f);var i=0;i=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),La[0|i](n,f)}function We(r,n){var f=0;return v[r+4>>2]=0,v[r>>2]=n,(n=v[r+12>>2])&&(f=v[5316],La[v[v[f>>2]+20>>2]](f,n,8610,230)),r}function Ge(r,n,f){n|=0,f|=0;var i=0;i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),La[0|i](n,f)}function Ue(r,n){var f;return n|=0,f=v[(r|=0)>>2]+n|0,n=X((r=v[f+4>>2])+4|0),v[n>>2]=r,Me(n+4|0,v[f+8>>2],r),0|n}function je(r,n){n|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),0|La[0|f](n)}function He(r,n,f,i){var e;V=e=V-16|0,sf(r+32|0,Gf(r=zf(e+4|0,f),i)),Oe(r),n&&ar(Ef(n)),V=e+16|0}function Le(r,n,f){var i;return r|=0,n=w(n),f|=0,V=i=V-16|0,p[i+12>>2]=n,r=0|La[0|r](i+12|0,f),V=i+16|0,0|r}function Me(r,n,f){var i=0;if(f)for(i=r;e[0|i]=l[0|n],i=i+1|0,n=n+1|0,f=f-1|0;);return r}function _e(r,n,f,i){r|=0,n|=0,f=w(f),i|=0,p[v[r+16>>2]+(n<<2)>>2]=f,r=v[r+32>>2]+(n<<4)|0,v[r+4>>2]=0,ue(r,i)}function ze(r,n,f,i,e){r|=0,n|=0,f=w(f),i=w(i),e=w(e),r=v[r+32>>2]+m(n,12)|0,p[r>>2]=f,p[r+8>>2]=e,p[r+4>>2]=i}function xe(r,n){var f=0;for(n=v[n+4>>2];(f=vi(v[r+4>>2],n))&&(r=v[r+8>>2]););return!f}function Je(r,n,f,i,t,u){return v[r+4>>2]=n,v[r>>2]=9772,zf(r+8|0,f),v[r+20>>2]=i,zf(r+24|0,t),e[r+36|0]=u,r}function Ke(r,n){return se(v[r+4>>2]+36|0,v[n+4>>2]+36|0)?se(v[r+8>>2]+36|0,v[n+8>>2]+36|0):0}function Be(r,n){n|=0;var f=0;f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),La[0|f](n)}function Ne(r,n){var f;return r|=0,n|=0,V=f=V-16|0,v[f+12>>2]=n,r=0|La[0|r](f+12|0),V=f+16|0,0|r}function qe(r,n){return v[r+8>>2]=0,v[r+4>>2]=n,v[r+16>>2]=0,v[r+20>>2]=0,v[r+12>>2]=8892,v[r>>2]=8860,r}function De(r){kr(r|=0,p[r+32>>2],p[r+36>>2],p[r+40>>2],p[r+44>>2],p[r+48>>2],p[r+52>>2],p[r+56>>2])}function Ve(r){r&&(v[r+16>>2]=v[r+16>>2]-1,v[r+16>>2]||La[v[v[r>>2]+4>>2]](r))}function Ze(r,n,f){var i=0;if(f)for(i=r;e[0|i]=n,i=i+1|0,f=f-1|0;);return r}function Ye(r,n,f,i){r|=0,n|=0,f=w(f),i|=0,p[v[r+20>>2]+(n<<2)>>2]=f,sf(v[r+36>>2]+m(n,12)|0,i)}function Xe(r,n,f,i,e,t,u){t|=0,u|=0,lr(r|=0,n|=0,f|=0,i|=0,v[12+(e|=0)>>2],t,u)}function Qe(r){var n;return v[(r|=0)>>2]=1032,jn(n=r+4|0),Oe(r+36|0),Co(r+20|0),Po(n),0|r}function rt(r,n){var f;V=f=V-16|0,Tt(f,2,n,0),Xr(r+4|0,f),e[v[r+20>>2]+88|0]=1,V=f+16|0}function nt(r,n,f){n|=0,f|=0,p[(n<<=2)+v[16+(r|=0)>>2]>>2]=p[f+8>>2],v[n+v[r+32>>2]>>2]=f}function ft(r,n,f,i){r|=0,n|=0,f=w(f),i=w(i),r=v[r+32>>2]+(n<<3)|0,p[r>>2]=f,p[r+4>>2]=i}function it(r,n,f,i){r|=0,n|=0,f=w(f),i=w(i),r=v[r+36>>2]+(n<<3)|0,p[r>>2]=f,p[r+4>>2]=i}function et(r,n,f,i){p[v[r+36>>2]+(n<<2)>>2]=f,r=v[r+52>>2]+(n<<4)|0,v[r+4>>2]=0,te(r,i)}function tt(r,n){return r=Et(r,n),v[r+28>>2]=0,v[r+20>>2]=0,v[r+24>>2]=0,v[r>>2]=10084,r}function ut(r){var n;return n=ht(16),v[n+12>>2]=0,v[n+4>>2]=0,v[n+8>>2]=0,v[n>>2]=r,n}function ot(r){var n;return v[(r|=0)>>2]=9588,jn(n=r+20|0),Ho(n),Lo(r+4|0),0|r}function at(r,n){var f;f=Jt(4),v[f>>2]=n,$(21795,0|r,2,16280,11360,1657,0|f,0,0)}function ct(r,n){var f;f=Jt(4),v[f>>2]=n,$(1999,0|r,2,13600,11360,1613,0|f,0,0)}function bt(r,n){var f;f=Jt(4),v[f>>2]=n,$(1999,0|r,2,13608,11360,1614,0|f,0,0)}function kt(r,n){var f;f=Jt(4),v[f>>2]=n,$(1944,0|r,2,13752,11360,1617,0|f,0,0)}function st(r,n){var f;f=Jt(4),v[f>>2]=n,$(5364,0|r,3,14520,13272,1625,0|f,0,0)}function vt(r,n){return r=pt(r,n),v[r+24>>2]=9708,v[r>>2]=11024,Ze(r+28|0,0,58),r}function lt(r,n){return r=pt(r,n),v[r+24>>2]=9708,v[r>>2]=9960,Ze(r+28|0,0,48),r}function ht(r){var n;return n=v[5316],0|La[v[v[n>>2]+12>>2]](n,r,8610,40)}function dt(r){var n;return(-1>>>(n=31&r)&-2)<<n|(-1<<(r=0-r&31)&-2)>>>r}function pt(r,n){return v[r>>2]=9420,zf(r+4|0,n),e[r+20|0]=0,v[r+16>>2]=0,r}function yt(r){var n;return v[(r|=0)>>2]=10408,jn(n=r+4|0),yo(n),0|r}function mt(r){var n;return v[(r|=0)>>2]=10536,jn(n=r+4|0),mo(n),0|r}function wt(r){var n;return v[(r|=0)>>2]=10552,jn(n=r+4|0),wo(n),0|r}function gt(r){var n;return v[(r|=0)>>2]=8828,jn(n=r+4|0),Oo(n),0|r}function Ft(r){return v[(r|=0)>>2]=11208,Lo(r+36|0),go(r+20|0),0|ru(r)}function At(r,n,f){return r|=0,n=w(n),f=w(f),p[r+4>>2]=f,p[r>>2]=n,0|r}function Tt(r,n,f,i){v[r+12>>2]=i,v[r+8>>2]=f,v[r+4>>2]=n,v[r>>2]=8684}function $t(r,n,f){return Sf(r,n,n?cf(n):0,f)}function It(r,n,f){n|=0,f|=0,v[v[12+(r|=0)>>2]+(n<<2)>>2]=v[f>>2]}function Ct(r){var n;n=v[5316],La[v[v[n>>2]+20>>2]](n,r,8610,62)}function Pt(r,n){n|=0,v[v[16+(r|=0)>>2]+m(n,76)>>2]=1065353216}function Et(r,n){return v[r>>2]=9144,zf(r+4|0,n),v[r+16>>2]=0,r}function Ot(r,n,f,i){n|=0,f|=0,i|=0,La[v[(r|=0)>>2]](n,f,i)}function Rt(r){var n;return p[12+(n=V-16|0)>>2]=r,p[n+12>>2]}function St(r,n){return v[r>>2]=9168,v[r+4>>2]=n,v[r>>2]=9096,r}function Wt(r){return v[4+(0|La[v[v[(r|=0)>>2]+8>>2]](r))>>2]}function Gt(r,n){return r=Rf(r,n),v[r+64>>2]=0,v[r>>2]=9348,r}function Ut(r){return v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=10944,r}function jt(r,n){return r=St(r,n),v[r+8>>2]=n,v[r>>2]=17048,r}function Ht(r,n,f,i){vr(16+(r|=0)|0,n|=0,f|=0,i|=0)}function Lt(r){return v[4+(0|La[v[v[(r|=0)>>2]>>2]](r))>>2]}function Mt(r,n,f){return 0|tf(16+(r|=0)|0,n|=0,f|=0)}function _t(r){return v[(r|=0)>>2]=10028,Lo(r+20|0),0|nu(r)}function zt(r){return v[(r|=0)>>2]=11100,Lo(r+20|0),0|nu(r)}function xt(r,n,f){return n|=0,f|=0,0|La[0|(r|=0)](n,f)}function Jt(r){return(r=X(r>>>0<=1?1:r))||(L(),c()),r}function Kt(r,n){var f;f=bi(r,n),v[f>>2]=10056}function Bt(r,n,f){return(r=Un(r,n))&&(f=v[r+20>>2]),f}function Nt(r,n,f){v[r+8>>2]=f,v[r+4>>2]=n,v[r>>2]=10108}function qt(r,n,f){return(r=Un(r,n))&&(f=v[r+16>>2]),f}function Dt(r,n,f){return(r=Un(r,n))&&(f=p[r+24>>2]),f}function Vt(r,n,f){v[r+8>>2]=f,v[r+4>>2]=n,v[r>>2]=8876}function Zt(r,n,f){r|=0,n|=0,f=w(f),p[v[r>>2]+n>>2]=f}function Yt(r,n){return n|=0,v[12+(r|=0)>>2]+(n<<2)|0}function Xt(r,n){r|=0,n=w(n),p[r+160>>2]=p[r+160>>2]+n}function Qt(r,n){return n|=0,v[12+(r|=0)>>2]+(n<<4)|0}function ru(r){return v[(r|=0)>>2]=9144,Oe(r+4|0),0|r}function nu(r){return v[(r|=0)>>2]=9440,Lo(r+4|0),0|r}function fu(r,n){v[r+8>>2]=0,v[r+4>>2]=n,v[r>>2]=10108}function iu(r){return v[(r|=0)>>2]=8700,Mo(r+4|0),0|r}function eu(r){return v[(r|=0)>>2]=9420,Oe(r+4|0),0|r}function tu(r,n){return n|=0,w(p[v[(r|=0)>>2]+n>>2])}function uu(r,n,f){n|=0,f|=0,v[v[(r|=0)>>2]+n>>2]=f}function ou(r,n,f){n|=0,f|=0,e[v[(r|=0)>>2]+n|0]=f}function au(r,n){v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=n+20}function cu(r,n){return n|=0,0|La[v[(r|=0)>>2]](n)}function bu(r){(r|=0)&&La[v[v[r>>2]+16>>2]](r)}function ku(r,n){return 0|Ai(100+(r|=0)|0,n|=0)}function su(r,n){return 0|Ai(116+(r|=0)|0,n|=0)}function vu(r,n){return 0|Ai(132+(r|=0)|0,n|=0)}function lu(r){return r?31-F(r-1^r)|0:32}function hu(r){return r<w(0)?w(-1):r>w(0)?w(1):w(0)}function du(r,n){return n|=0,v[v[(r|=0)>>2]+n>>2]}function pu(r,n){n|=0,Lr(r|=0,wu(v[r+4>>2],n))}function yu(r,n){return 0|Ti(16+(r|=0)|0,n|=0)}function mu(r,n){return 0|Zi(32+(r|=0)|0,n|=0)}function wu(r,n){return 0|Ai(48+(r|=0)|0,n|=0)}function gu(r,n){return n|=0,l[v[(r|=0)>>2]+n|0]}function Fu(r,n){return 0|Zi(16+(r|=0)|0,n|=0)}function Au(r,n){return 0|Ai(68+(r|=0)|0,n|=0)}function Tu(r,n){return 0|li(24+(r|=0)|0,n|=0)}function $u(r,n){return 0|Ti(32+(r|=0)|0,n|=0)}function Iu(r){(r|=0)&&La[v[v[r>>2]+4>>2]](r)}function Cu(r,n){return r=qf(r,n),v[r>>2]=10220,r}function Pu(r){return 16777216+(r<<1)>>>0<16777217}function Eu(r,n){return 0|li(8+(r|=0)|0,n|=0)}function Ou(r,n){return r=qf(r,n),v[r>>2]=10192,r}function Ru(r,n){return r=Rf(r,n),v[r>>2]=9324,r}function Su(r,n){return!vi(v[r+4>>2],v[n+4>>2])}function Wu(r){return r<w(0)?w(0):r>w(1)?w(1):r}function Gu(r,n,f,i){z(0|r,0|n,8,0,0|f,-1,0|i)}function Uu(r,n){return gr(r,n,10,-2147483648)}function ju(r){return v[v[4+(r|=0)>>2]+16>>2]}function Hu(r){return 0!=v[224+(r|=0)>>2]|0}function Lu(r){v[r+4>>2]=v[5613],v[5613]=r}function Mu(r,n){return w(Rt(r?w(-n):n)*n)}function _u(r){return 0|Ki(r|=0,17240)}function zu(r){return 0|We(r|=0,10328)}function xu(r){return 0|We(r|=0,17112)}function Ju(r){return w(p[60+(r|=0)>>2])}function Ku(r){return 0|We(r|=0,10312)}function Bu(r){return 0|We(r|=0,10296)}function Nu(r){return 0|We(r|=0,10280)}function qu(r){return 0|We(r|=0,11308)}function Du(r){return 0|We(r|=0,11644)}function Vu(r){return 0|We(r|=0,10424)}function Zu(r){return 0|We(r|=0,12960)}function Yu(r){return 0|We(r|=0,10520)}function Xu(r){return 0|Ni(r|=0,17144)}function Qu(r,n){n|=0,v[64+(r|=0)>>2]=n}function ro(r){return 0|Ki(r|=0,17128)}function no(r){return 0|We(r|=0,10696)}function fo(r){return 0|We(r|=0,10680)}function io(r){return 0|We(r|=0,10664)}function eo(r){return 0|We(r|=0,10648)}function to(r){return 0|We(r|=0,10632)}function uo(r){return 0|We(r|=0,10616)}function oo(r){return 0|We(r|=0,10600)}function ao(r){return 0|Ff(r|=0,13024)}function co(r){return 0|Ff(r|=0,10864)}function bo(r){return 0|We(r|=0,10896)}function ko(r){return 0|We(r|=0,10264)}function so(r,n){n|=0,v[60+(r|=0)>>2]=n}function vo(r){return 0|Ki(r|=0,17272)}function lo(r){return 0|We(r|=0,10376)}function ho(r){return 0|Ki(r|=0,17288)}function po(r){return 0|Ni(r|=0,17304)}function yo(r){return 0|We(r|=0,10440)}function mo(r){return 0|We(r|=0,10488)}function wo(r){return 0|We(r|=0,10504)}function go(r){return 0|We(r|=0,10816)}function Fo(r){return 0|We(r|=0,10584)}function Ao(r){return 0|We(r|=0,9080)}function To(r,n){n|=0,e[44+(r|=0)|0]=n}function $o(r){return 0|We(r|=0,9064)}function Io(r){return 0|We(r|=0,8764)}function Co(r){return 0|Ki(r|=0,8620)}function Po(r){return 0|We(r|=0,1048)}function Eo(r){return 0|Ni(r|=0,8892)}function Oo(r){return 0|We(r|=0,8780)}function Ro(r){return r-65>>>0<26?32|r:r}function So(r){return 0|Ff(r|=0,9496)}function Wo(r){return 0|Ff(r|=0,9540)}function Go(r){return 0|We(r|=0,9708)}function Uo(r){return 0|We(r|=0,9292)}function jo(r){return 0|We(r|=0,9884)}function Ho(r){return 0|We(r|=0,8844)}function Lo(r){return 0|We(r|=0,8796)}function Mo(r){return 0|Ff(r|=0,8812)}function _o(r){return 32==(0|r)|r-9>>>0<5}function zo(r){return 0|La[0|(r|=0)]()}function xo(r,n){return oi(r,n,5,11048)}function Jo(r){return v[24+(r|=0)>>2]}function Ko(r){return v[12+(r|=0)>>2]}function Bo(r){return v[16+(r|=0)>>2]}function No(r,n){return oi(r,n,6,9728)}function qo(r){return v[20+(r|=0)>>2]}function Do(r){return yf(r+16|0),Du(r)}function Vo(r,n){return oi(r,n,3,9984)}function Zo(r){v[r+16>>2]=v[r+16>>2]+1}function Yo(r){return v[60+(r|=0)>>2]}function Xo(r){return l[44+(r|=0)|0]}function Qo(r){return v[r>>2]=10960,r}function ra(r){return v[r>>2]=11176,r}function na(r){return v[8+(r|=0)>>2]}function fa(r){return v[4+(r|=0)>>2]}function ia(r){return 104+(r|=0)|0}function ea(r){return 100+(r|=0)|0}function ta(r){return 20+(r|=0)|0}function ua(r){return 40+(r|=0)|0}function oa(r){return 16+(r|=0)|0}function aa(r){return 84+(r|=0)|0}function ca(r){return 72+(r|=0)|0}function ba(r){return 68+(r|=0)|0}function ka(r){return 36+(r|=0)|0}function sa(r){return 24+(r|=0)|0}function va(r){(r|=0)&&ar(r)}function la(r){return 8+(r|=0)|0}function ha(r){return 4+(r|=0)|0}function da(r){return _i(r,8764)}function pa(r){return _i(r,8796)}function ya(r){jr(r|=0),hf(r)}function ma(r){return 0|r}function wa(r){Ct(zt(r|=0))}function ga(r){Ct(Ft(r|=0))}function Fa(r){return r+204|0}function Aa(r){return r+140|0}function Ta(r,n){v[r+16>>2]=n}function $a(r){return r+144|0}function Ia(){}function Ca(r){return r+44|0}function Pa(r,n){e[r+20|0]=n}function Ea(r,n){fe(r,n,1)}function Oa(r,n){fe(r,n,2)}function Ra(r){ar(r|=0)}function Sa(r){e[r+64|0]=1}function Wa(r){Ct(r|=0)}function Ga(r,n){fe(r,n,4)}function Ua(){c()}function ja(){}var Ha,La=(Ha=[null,Qe,function(r){Qe(r|=0),Ct(r)},Po,function(r){Ct(Po(r|=0))},Co,function(r){Ct(Co(r|=0))},ma,Wa,function(){},function(){Po(21024)},function(){Qe(21044)},ei,function(r){ei(r|=0),Ct(r)},function(r){return 0|ei((r|=0)-4|0)},function(r){ei(r=(r|=0)-4|0),Ct(r)},Wa,iu,function(r){iu(r|=0),Ct(r)},fn,function(r){fn(r|=0),Ct(r)},function(r){return 0|fn((r|=0)-4|0)},function(r){fn(r=(r|=0)-4|0),Ct(r)},function(r){var n,f=0;return v[(r|=0)>>2]=8748,(n=v[r+8>>2])&&(f=v[r+4>>2])&&La[0|n](f),0|r},function(r){var n,f=0;v[(r|=0)>>2]=8748,(n=v[r+8>>2])&&(f=v[r+4>>2])&&La[0|n](f),ar(r)},Io,function(r){Ct(Io(r|=0))},Oo,function(r){Ct(Oo(r|=0))},Lo,function(r){Ct(Lo(r|=0))},Mo,function(r){Ct(Mo(r|=0))},gt,function(r){Ct(gt(r|=0))},Ho,function(r){Ct(Ho(r|=0))},function(r){return v[(r|=0)>>2]=8860,Eo(r+12|0),0|r},function(r){v[(r|=0)>>2]=8860,Eo(r+12|0),Ct(r)},Wa,Eo,function(r){Ct(Eo(r|=0))},function(r){return v[(r|=0)>>2]=8908,0|r},function(r){v[(r|=0)>>2]=8908,Ct(r)},df,function(r){df(r|=0),Ct(r)},function(r){var n,f=0;return v[4+(r|=0)>>2]=9032,v[r>>2]=9016,Oe(r+28|0),Oe(r+16|0),v[r+4>>2]=8748,(n=v[r+12>>2])&&(f=v[r+8>>2])&&La[0|n](f),0|r},function(r){var n,f=0;v[4+(r|=0)>>2]=9032,v[r>>2]=9016,Oe(r+28|0),Oe(r+16|0),v[r+4>>2]=8748,(n=v[r+12>>2])&&(f=v[r+8>>2])&&La[0|n](f),Ct(r)},function(r){var n,f;return v[(r|=0)>>2]=9032,v[(n=r-4|0)>>2]=9016,Oe(r+24|0),Oe(r+12|0),v[r>>2]=8748,(f=v[r+8>>2])&&(r=v[r+4>>2])&&La[0|f](r),0|n},function(r){var n,f;v[(r|=0)>>2]=9032,v[(n=r-4|0)>>2]=9016,Oe(r+24|0),Oe(r+12|0),v[r>>2]=8748,(f=v[r+8>>2])&&(r=v[r+4>>2])&&La[0|f](r),Ct(n)},function(r){return v[(r|=0)>>2]=9048,Io(r+96|0),Io(r+80|0),Oe(r+8|0),0|r},function(r){v[(r|=0)>>2]=9048,Io(r+96|0),Io(r+80|0),Oe(r+8|0),Ct(r)},$o,function(r){Ct($o(r|=0))},Ao,function(r){Ct(Ao(r|=0))},ja,ma,Wa,function(){return 21096},function(r,n,f,i){return n|=0,f|=0,i|=0,(r=ai(v[4+(r|=0)>>2],i))?(n=xr(ht(164),f),(i=v[n+28>>2])&&(!(f=v[n+24>>2])|(0|r)==(0|f)||La[0|i](f)),v[n+28>>2]=0,v[n+24>>2]=r,uf(n,p[r+36>>2],p[r+40>>2],p[r+44>>2],p[r+48>>2],l[r+72|0]),p[n+60>>2]=p[r+52>>2],p[n+64>>2]=p[r+56>>2],p[n+68>>2]=v[r+28>>2],p[n+72>>2]=v[r+32>>2],p[n+76>>2]=v[r+60>>2],p[n+80>>2]=v[r+64>>2],0|n):0},function(r,n,f,i){return n|=0,f|=0,i|=0,(n=ai(v[4+(r|=0)>>2],i))?(r=Jr(ht(236),f),(i=v[r+72>>2])&&(!(f=v[r+68>>2])|(0|n)==(0|f)||La[0|i](f)),v[r+72>>2]=0,v[r+68>>2]=n,p[r+180>>2]=p[n+36>>2],p[r+184>>2]=p[n+40>>2],p[r+188>>2]=p[n+44>>2],p[r+192>>2]=p[n+48>>2],e[r+228|0]=l[n+72|0],v[r+232>>2]=v[n+76>>2],p[r+76>>2]=p[n+52>>2],p[r+80>>2]=p[n+56>>2],p[r+84>>2]=v[n+28>>2],p[r+88>>2]=v[n+32>>2],p[r+92>>2]=v[n+60>>2],p[r+96>>2]=v[n+64>>2],0|r):0},function(r,n,f){return f|=0,0|Ru(ht(64),f)},function(r,n,f){return f|=0,0|oe(ht(84),f)},function(r,n,f){return f|=0,0|tt(ht(32),f)},function(r,n,f){return f|=0,0|Gt(ht(68),f)},Ia,ja,ru,Ua,function(){return 21108},function(){L(),c()},ja,Ua,function(){return 21120},ja,function(r){return v[(r|=0)>>2]=9216,yf(r+24|0),Lo(r+8|0),0|r},function(r){v[(r|=0)>>2]=9216,yf(r+24|0),Lo(r+8|0),Ct(r)},function(){return 21132},function(r,n,f,i,e,t,u,o){r|=0,n|=0,i=w(i),e|=0,u|=0,o|=0;var a=0;r:{e=v[v[n+36>>2]+(v[r+4>>2]<<2)>>2],a=v[e+8>>2];n:if(0|La[v[v[a>>2]+16>>2]](a)){if(1==(0|o)){if(u)break n;break r}if(o=v[r+20>>2],p[o>>2]>i){if(u>>>0>1)break n;break r}u=v[r+12>>2]-1|0,p[o+(u<<2)>>2]<=i||(u=Lf(r+8|0,i,1)-1|0),o=v[r+36>>2],$e(e,r=v[4+(o+m(u,12)|0)>>2]?Ui(n,v[r+4>>2],o+m(u,12)|0):0)}return}u=0,!(o=v[e+4>>2]+68|0)|!v[o+4>>2]||(u=Ui(n,v[r+4>>2],o)),$e(e,u)},function(r){return v[4+(r|=0)>>2]+67108864|0},yf,function(r){Ct(yf(r|=0))},ja,function(r){return v[(r|=0)>>2]=9260,Uo(r+16|0),0|r},function(r){v[(r|=0)>>2]=9260,Uo(r+16|0),Ct(r)},function(){return 21144},De,function(r){return l[117+(r|=0)|0]},function(r,n){n|=0,e[117+(r|=0)|0]=n},Uo,function(r){Ct(Uo(r|=0))},function(r){return v[(r|=0)>>2]=9308,Oe(r+8|0),0|r},function(r){v[(r|=0)>>2]=9308,Oe(r+8|0),Ct(r)},ja,Ft,ga,function(){return 21156},function(r){var n;return r|=0,Ru(n=ht(64),ha(r)),de(r,n),0|n},ja,ga,function(){return 21168},function(r){var n;return r|=0,Gt(n=ht(68),ha(r)),de(r,n),v[n+64>>2]=v[r+64>>2],0|n},ja,function(r){return v[(r|=0)>>2]=9376,Lo(r+24|0),0|nu(r)},function(r){v[(r|=0)>>2]=9376,Lo(r+24|0),Ct(nu(r))},function(){return 21180},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=w(0),c=w(0),b=w(0),k=0,s=0,l=w(0);V=e=V-32|0,n=v[v[n+36>>2]+(v[r+20>>2]<<2)>>2],o=v[n+8>>2];r:if(0|La[v[v[o>>2]+16>>2]](o))if(o=v[r+36>>2],p[o>>2]>i){n:switch(0|u){case 0:r=v[n+4>>2],p[n+20>>2]=p[r+28>>2],p[n+24>>2]=p[r+32>>2],p[n+28>>2]=p[r+36>>2],p[n+32>>2]=p[r+40>>2],mi(n+16|0);break r;case 1:break n;default:break r}r=v[n+4>>2],v[e+12>>2]=9404,u=v[r+40>>2],v[e+24>>2]=v[r+36>>2],v[e+28>>2]=u,u=v[r+32>>2],v[e+16>>2]=v[r+28>>2],v[e+20>>2]=u,f=p[e+24>>2],i=p[e+16>>2],a=p[e+28>>2],c=p[n+24>>2],p[n+24>>2]=w(w(p[e+20>>2]-c)*t)+c,c=f,f=p[n+28>>2],p[n+28>>2]=w(w(c-f)*t)+f,f=p[n+32>>2],p[n+32>>2]=w(w(a-f)*t)+f,f=p[n+20>>2],p[n+20>>2]=w(w(i-f)*t)+f,mi(n+16|0)}else o=o+(v[r+28>>2]<<2)|0,p[o-20>>2]<=i?(i=p[o-4>>2],f=p[o-8>>2],c=p[o-12>>2],a=p[o-16>>2]):(o=(s=(k=Lf(r+24|0,i,5))<<2)+v[r+36>>2]|0,b=p[o-16>>2],c=p[o-12>>2],f=p[o-8>>2],l=p[o-4>>2],a=i,i=p[o>>2],a=pn(r,((k>>>0)/5|0)-1|0,w(w(1)-w(w(a-i)/w(p[o-20>>2]-i)))),r=v[r+36>>2]+s|0,i=w(l+w(a*w(p[r+16>>2]-l))),f=w(f+w(a*w(p[r+12>>2]-f))),c=w(c+w(a*w(p[r+8>>2]-c))),a=w(b+w(a*w(p[r+4>>2]-b)))),r=n+16|0,t!=w(1)?(u||(u=sa(v[n+4>>2]),p[n+20>>2]=p[u+4>>2],p[n+24>>2]=p[u+8>>2],p[n+28>>2]=p[u+12>>2],p[n+32>>2]=p[u+16>>2],mi(r)),b=p[n+32>>2],p[n+32>>2]=b+w(w(i-b)*t),i=p[n+28>>2],p[n+28>>2]=i+w(w(f-i)*t),f=p[n+24>>2],p[n+24>>2]=f+w(w(c-f)*t),f=p[n+20>>2],p[n+20>>2]=f+w(w(a-f)*t),mi(r)):(p[n+32>>2]=i,p[n+28>>2]=f,p[n+24>>2]=c,p[n+20>>2]=a,mi(r));V=e+32|0},function(r){return v[20+(r|=0)>>2]+83886080|0},Wa,ja,ja,eu,function(r){eu(r|=0),Ct(r)},function(){return 21204},ja,nu,Ua,function(){return 21216},ja,function(r){return v[(r|=0)>>2]=9468,So(r+40|0),Lo(r+24|0),0|nu(r)},function(r){v[(r|=0)>>2]=9468,So(r+40|0),Lo(r+24|0),Ct(nu(r))},function(){return 21228},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a,c=0,b=0,k=0,s=w(0),l=0;V=a=V-16|0,n=v[v[n+36>>2]+(v[r+20>>2]<<2)>>2],e=v[n+8>>2];r:if(0|La[v[v[e>>2]+16>>2]](e)&&(o=v[n+60>>2])&&!(!xe(0|La[v[v[o>>2]+8>>2]](o),21596)|v[o+56>>2]!=v[r+56>>2])){k=v[n+76>>2]?u:0,u=n+72|0,e=v[v[r+52>>2]+4>>2];n:if(p[v[r+36>>2]>>2]>i){f:switch(0|k){case 0:v[n+76>>2]=0;break r;case 2:case 3:break r;case 1:break f;default:break n}if(t==w(1)){v[n+76>>2]=0;break r}if(v[a+12>>2]=0,An(u,e,a+12|0),!v[o+24>>2]){if(!e)break r;for(n=v[n+84>>2],u=v[o+48>>2],r=0;f=p[(c=(o=r<<2)+n|0)>>2],p[c>>2]=w(w(p[u+o>>2]-f)*t)+f,(0|e)!=(0|(r=r+1|0)););break r}if(!e)break r;for(f=w(w(1)-t),n=v[n+84>>2],r=0;p[(u=n+(r<<2)|0)>>2]=f*p[u>>2],(0|e)!=(0|(r=r+1|0)););break r}if(v[a+8>>2]=0,An(u,e,a+8|0),u=v[r+28>>2]-1|0,p[v[r+36>>2]+(u<<2)>>2]<=i){if(r=v[r+52>>2],t==w(1)){if(3==(0|k)){if(v[o+24>>2]){if(!e)break r;for(n=v[n+84>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(c=(o=r<<2)+n|0)>>2]=p[u+o>>2]+p[c>>2],(0|e)!=(0|(r=r+1|0)););break r}if(!e)break r;for(c=v[n+84>>2],o=v[o+48>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(b=(n=r<<2)+c|0)>>2]=w(p[n+u>>2]-p[n+o>>2])+p[b>>2],(0|e)!=(0|(r=r+1|0)););break r}Me(v[n+84>>2],v[12+(r+(u<<4)|0)>>2],e<<2);break r}n:switch(0|k){case 0:if(v[o+24>>2]){if(!e)break r;for(n=v[n+84>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(o=r<<2)+n>>2]=p[u+o>>2]*t,(0|e)!=(0|(r=r+1|0)););break r}if(!e)break r;for(c=v[n+84>>2],o=v[o+48>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;f=p[(n=r<<2)+o>>2],p[n+c>>2]=w(w(p[n+u>>2]-f)*t)+f,(0|e)!=(0|(r=r+1|0)););break r;case 1:case 2:if(!e)break r;for(n=v[n+84>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;f=p[(c=(o=r<<2)+n|0)>>2],p[c>>2]=w(w(p[u+o>>2]-f)*t)+f,(0|e)!=(0|(r=r+1|0)););break r;case 3:break n;default:break r}if(v[o+24>>2]){if(!e)break r;for(n=v[n+84>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(c=(o=r<<2)+n|0)>>2]=w(p[u+o>>2]*t)+p[c>>2],(0|e)!=(0|(r=r+1|0)););break r}if(!e)break r;for(c=v[n+84>>2],o=v[o+48>>2],u=v[12+(r+(u<<4)|0)>>2],r=0;p[(b=(n=r<<2)+c|0)>>2]=w(w(p[n+u>>2]-p[n+o>>2])*t)+p[b>>2],(0|e)!=(0|(r=r+1|0)););}else if(c=Kf(r+24|0,i),u=v[r+52>>2],l=r,b=c-1|0,r=v[r+36>>2],f=p[r+(c<<2)>>2],f=pn(l,b,w(w(1)-w(w(i-f)/w(p[r+(b<<2)>>2]-f)))),t!=w(1)){n:switch(0|k){case 0:if(v[o+24>>2]){if(!e)break r;for(o=v[n+84>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;i=p[(n=r<<2)+u>>2],p[n+o>>2]=w(w(w(p[n+c>>2]-i)*f)+i)*t,(0|e)!=(0|(r=r+1|0)););break r}if(!e)break r;for(k=v[n+84>>2],o=v[o+48>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],n=0;i=p[(r=n<<2)+u>>2],s=w(w(w(p[r+c>>2]-i)*f)+i),i=p[r+o>>2],p[r+k>>2]=w(w(s-i)*t)+i,(0|e)!=(0|(n=n+1|0)););break r;case 1:case 2:if(!e)break r;for(o=v[n+84>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;b=(n=r<<2)+o|0,i=p[n+u>>2],s=w(w(w(p[n+c>>2]-i)*f)+i),i=p[b>>2],p[b>>2]=w(w(s-i)*t)+i,(0|e)!=(0|(r=r+1|0)););break r;case 3:break n;default:break r}if(v[o+24>>2]){if(!e)break r;for(o=v[n+84>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;b=(n=r<<2)+o|0,i=p[n+u>>2],p[b>>2]=w(w(w(w(p[n+c>>2]-i)*f)+i)*t)+p[b>>2],(0|e)!=(0|(r=r+1|0)););}else if(e)for(k=v[n+84>>2],o=v[o+48>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],n=0;b=(r=n<<2)+k|0,i=p[r+u>>2],p[b>>2]=w(w(w(w(w(p[r+c>>2]-i)*f)+i)-p[r+o>>2])*t)+p[b>>2],(0|e)!=(0|(n=n+1|0)););}else{if(3!=(0|k)){if(!e)break r;for(o=v[n+84>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;i=p[(n=r<<2)+u>>2],p[n+o>>2]=w(w(p[n+c>>2]-i)*f)+i,(0|e)!=(0|(r=r+1|0)););break r}if(v[o+24>>2]){if(!e)break r;for(o=v[n+84>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],r=0;b=(n=r<<2)+o|0,i=p[n+u>>2],p[b>>2]=p[b>>2]+w(w(w(p[n+c>>2]-i)*f)+i),(0|e)!=(0|(r=r+1|0)););break r}if(!e)break r;for(k=v[n+84>>2],o=v[o+48>>2],c=v[12+(u+(c<<4)|0)>>2],u=v[12+(u+(b<<4)|0)>>2],n=0;b=(r=n<<2)+k|0,i=p[r+u>>2],p[b>>2]=w(w(w(w(p[r+c>>2]-i)*f)+i)-p[r+o>>2])+p[b>>2],(0|e)!=(0|(n=n+1|0)););}}V=a+16|0},function(r){return 100663296+(v[v[56+(r|=0)>>2]+60>>2]+v[r+20>>2]|0)|0},So,function(r){Ct(So(r|=0))},ja,function(r){return v[(r|=0)>>2]=9512,Wo(r+20|0),Lo(r+4|0),0|r},function(r){v[(r|=0)>>2]=9512,Wo(r+20|0),Lo(r+4|0),Ct(r)},function(){return 21240},function(r,n,f,i,e,t,u,o){r|=0,n|=0,i=w(i),e|=0,e=n+40|0;r:if(1!=(0|(o|=0))|(u|=0)){if(o=v[r+16>>2],p[o>>2]>i){if(u>>>0>1)break r;if(u=0,v[n+44>>2]=0,Oa(e,v[n+28>>2]),!(r=v[n+28>>2]))break r;for(;_n(e,v[n+36>>2]+(u<<2)|0),(0|r)!=(0|(u=u+1|0)););}else if(u=v[r+8>>2]-1|0,p[o+(u<<2)>>2]<=i||(u=Kf(r+4|0,i)-1|0),r=v[r+32>>2]+(u<<4)|0,o=v[r+4>>2])for(u=0;v[(e=u<<2)+v[n+52>>2]>>2]=v[v[n+36>>2]+(v[e+v[r+12>>2]>>2]<<2)>>2],(0|o)!=(0|(u=u+1|0)););else if(u=0,v[n+44>>2]=0,r=v[n+28>>2])for(;_n(e,v[n+36>>2]+(u<<2)|0),(0|r)!=(0|(u=u+1|0)););}else{if(u=0,v[n+44>>2]=0,Oa(e,v[n+28>>2]),!(r=v[n+28>>2]))break r;for(;_n(e,v[n+36>>2]+(u<<2)|0),(0|r)!=(0|(u=u+1|0)););}},function(){return 134217728},Wo,function(r){Ct(Wo(r|=0))},function(r){return v[(r|=0)>>2]=9556,Oe(r+20|0),0|r},function(r){v[(r|=0)>>2]=9556,Oe(r+20|0),Ct(r)},function(r){return v[(r|=0)>>2]=9572,Oe(r+36|0),Oe(r+24|0),Oe(r+4|0),0|r},function(r){v[(r|=0)>>2]=9572,Oe(r+36|0),Oe(r+24|0),Oe(r+4|0),Ct(r)},ja,ot,function(r){ot(r|=0),Ct(r)},function(){return 21252},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=0,c=0;r:if(e){if(a=v[r+8>>2],f>i)La[v[v[r>>2]+12>>2]](r,n,f,w(34028234663852886e22),e,t,u,o),o=v[r+16>>2],f=w(-1);else if(o=v[r+16>>2],p[(o+(a<<2)|0)-4>>2]<=f)break r;if(!((t=p[o>>2])>i)){o=0;n:if(!(f<t))for(n=(u=Kf(r+4|0,f))&u>>31,c=v[r+16>>2],f=p[c+(u<<2)>>2];;){if((0|(o=u))<=0){o=n;break n}if(f!=p[((u=o-1|0)<<2)+c>>2])break}if(!(o>>>0>=a>>>0))for(;;){if(!(p[(n=o<<2)+v[r+16>>2]>>2]<=i))break r;if(_n(e,n+v[r+32>>2]|0),(0|a)==(0|(o=o+1|0)))break}}}},function(){return 117440512},ma,Ua,ja,function(r){return v[(r|=0)>>2]=9652,Uo(r+8|0),0|r},function(r){v[(r|=0)>>2]=9652,Uo(r+8|0),Ct(r)},function(){return 21268},function(r){var n=0,f=0,i=0;switch(v[12+(r|=0)>>2]-1|0){case 0:return n=v[r+40>>2],void Ar(v[v[r+20>>2]>>2],p[n+100>>2],p[n+112>>2],l[r+28|0],l[r+29|0],l[v[r+4>>2]+50|0],p[r+32>>2]);case 1:n=v[r+20>>2],f=v[n>>2],i=v[n+4>>2],n=v[r+40>>2],cr(f,i,p[n+100>>2],p[n+112>>2],v[r+24>>2],l[r+29|0],p[r+36>>2],p[r+32>>2])}},Xo,To,ju,ja,function(r){return v[(r|=0)>>2]=9688,Go(r+24|0),0|eu(r)},function(r){v[(r|=0)>>2]=9688,Go(r+24|0),Ct(eu(r))},function(){return 21280},Go,function(r){Ct(Go(r|=0))},ja,function(r){return v[(r|=0)>>2]=9728,Lo(r+20|0),0|nu(r)},function(r){v[(r|=0)>>2]=9728,Lo(r+20|0),Ct(nu(r))},function(){return 21292},function(r,n,f,i,t,u,o,a){r|=0,n|=0,f=w(f),i=w(i),t|=0,u=w(u),o|=0,a|=0;var c=0,b=w(0),k=w(0);r:{n:{f:{n=v[v[n+68>>2]+(v[r+36>>2]<<2)>>2];i:if(0|La[v[v[n>>2]+16>>2]](n)){if(t=v[r+32>>2],p[t>>2]>i){e:switch(0|o){case 0:r=v[n+4>>2],p[n+32>>2]=p[r+52>>2],p[n+36>>2]=p[r+56>>2];break f;case 1:break e;default:break i}r=v[n+4>>2],f=p[n+32>>2],p[n+32>>2]=w(w(p[r+52>>2]-f)*u)+f,f=p[n+36>>2],p[n+36>>2]=w(w(p[r+56>>2]-f)*u)+f;break f}if(c=v[r+24>>2],p[(t+(c<<2)|0)-24>>2]<=i){if(!o){if(r=t+(c<<2)|0,t=v[n+4>>2],f=p[t+52>>2],p[n+32>>2]=w(w(p[r-20>>2]-f)*u)+f,f=p[t+56>>2],p[n+36>>2]=w(w(p[r-16>>2]-f)*u)+f,1==(0|a))break n;break r}if(r=t+(c<<2)|0,f=p[n+32>>2],p[n+32>>2]=w(w(p[r-20>>2]-f)*u)+f,f=p[n+36>>2],p[n+36>>2]=w(w(p[r-16>>2]-f)*u)+f,a)break i;break r}if(t=Lf(r+20|0,i,6),c=v[r+32>>2]+(t<<2)|0,f=p[c-16>>2],b=p[c-20>>2],k=i,i=p[c>>2],i=pn(r,((0|t)/6|0)-1|0,w(w(1)-w(w(k-i)/w(p[c-24>>2]-i)))),!o){if(c=v[r+32>>2],k=w(w(w(p[4+(o=c+(t<<2)|0)>>2]-b)*i)+b),t=v[n+4>>2],b=p[t+52>>2],p[n+32>>2]=w(w(k-b)*u)+b,i=w(w(w(p[o+8>>2]-f)*i)+f),f=p[t+56>>2],p[n+36>>2]=w(w(i-f)*u)+f,1==(0|a))break n;return f=p[(c+(v[r+24>>2]<<2)|0)-12>>2],r=w(g(f))<w(2147483648)?~~f:-2147483648,v[n+24>>2]=r,e[n+28|0]=p[o-8>>2]!=w(0),void(e[n+29|0]=p[o-4>>2]!=w(0))}r=v[r+32>>2]+(t<<2)|0,k=w(w(w(p[r+4>>2]-b)*i)+b),b=p[n+32>>2],p[n+32>>2]=w(w(k-b)*u)+b,i=w(w(w(p[r+8>>2]-f)*i)+f),f=p[n+36>>2],p[n+36>>2]=w(w(i-f)*u)+f,a||(f=p[r-12>>2],t=w(g(f))<w(2147483648)?~~f:-2147483648,v[n+24>>2]=t,e[n+28|0]=p[r-8>>2]!=w(0),e[n+29|0]=p[r-4>>2]!=w(0))}return}return v[n+24>>2]=v[r+44>>2],e[n+28|0]=l[r+48|0],void(e[n+29|0]=l[r+49|0])}return v[n+24>>2]=v[t+44>>2],e[n+28|0]=l[t+48|0],void(e[n+29|0]=l[t+49|0])}f=p[r-12>>2],t=w(g(f))<w(2147483648)?~~f:-2147483648,v[n+24>>2]=t,e[n+28|0]=p[r-8>>2]!=w(0),e[n+29|0]=p[r-4>>2]!=w(0)},function(r){return v[36+(r|=0)>>2]+150994944|0},function(r){return v[(r|=0)>>2]=9772,Oe(r+24|0),Oe(r+8|0),0|r},function(r){v[(r|=0)>>2]=9772,Oe(r+24|0),Oe(r+8|0),Ct(r)},ja,ja,ja,function(){return 21320},function(r,n){return r|=0,(n=w(n))<=w(.5)?w(w(Fr(w(n+n),w(v[r+4>>2]))*w(.5))):(n=w(n+w(-1)),r=v[r+4>>2],w(w(w(Fr(w(n+n),w(0|r))/w(1&r?2:-2))+w(1))))},function(r,n,f,i){return r|=0,n=w(n),f=w(f),i=w(i),w(w(w(w(f-n)*w(La[v[v[r>>2]+4>>2]](r,i)))+n))},ma,Ra,function(){return 21332},function(r,n){return r|=0,n=w(n),r=v[r+4>>2],w(w(w(Fr(w(n+w(-1)),w(0|r))*w(1&r?1:-1))+w(1)))},Ra,ja,Mf,function(r){Mf(r|=0),Ct(r)},function(){return 21344},function(r){var n,f,i=0,t=0;return v[100+(r|=0)>>2]?0|Nr(r):(Jr(n=ht(236),ha(r)),i=v[r+68>>2],(f=v[n+72>>2])&&(!(t=v[n+68>>2])|(0|i)==(0|t)||La[0|f](t)),v[n+72>>2]=0,v[n+68>>2]=i,p[n+180>>2]=p[r+180>>2],p[n+184>>2]=p[r+184>>2],p[n+188>>2]=p[r+188>>2],p[n+192>>2]=p[r+192>>2],e[n+228|0]=l[r+228|0],v[n+232>>2]=v[r+232>>2],p[n+76>>2]=p[r+76>>2],p[n+80>>2]=p[r+80>>2],p[n+84>>2]=p[r+84>>2],p[n+88>>2]=p[r+88>>2],p[n+92>>2]=p[r+92>>2],p[n+96>>2]=p[r+96>>2],sf(n+168|0,r+168|0),p[n+208>>2]=p[r+208>>2],p[n+212>>2]=p[r+212>>2],p[n+216>>2]=p[r+216>>2],p[n+220>>2]=p[r+220>>2],mi(n+204|0),de(r,n),v[n+124>>2]=0,te(n+120|0,r+120|0),v[n+108>>2]=0,te(n+104|0,r+104|0),v[n+140>>2]=0,mn(n+136|0,r+136|0),i=v[r+224>>2],v[n+156>>2]=0,v[n+224>>2]=i,mn(i=n+152|0,i),p[n+196>>2]=p[r+196>>2],p[n+200>>2]=p[r+200>>2],0|n)},function(r){return 0|Mf((r|=0)-64|0)},function(r){Mf(r=(r|=0)-64|0),Ct(r)},jo,function(r){Ct(jo(r|=0))},ja,function(r){return v[(r|=0)>>2]=9900,Lo(r- -64|0),0|Ft(r)},function(r){v[(r|=0)>>2]=9900,Lo(r- -64|0),Ct(Ft(r))},function(){return 21356},function(r){var n;return r|=0,oe(n=ht(84),ha(r)),de(r,n),v[n+68>>2]=0,te(n- -64|0,r- -64|0),e[n+80|0]=l[r+80|0],e[n+81|0]=l[r+81|0],0|n},ja,function(r){return v[(r|=0)>>2]=9924,Lo(r+124|0),Lo(r+108|0),Lo(r+92|0),Lo(r+76|0),Lo(r+60|0),Lo(r+44|0),Uo(r+8|0),0|r},function(r){v[(r|=0)>>2]=9924,Lo(r+124|0),Lo(r+108|0),Lo(r+92|0),Lo(r+76|0),Lo(r+60|0),Lo(r+44|0),Uo(r+8|0),Ct(r)},function(){return 21368},function(r){r|=0;var n,f=w(0),i=0,t=w(0),u=w(0),o=0,a=w(0),c=w(0),b=0,k=0,s=0,h=0,d=0,y=w(0),g=w(0),F=0,A=w(0),$=w(0),I=w(0),C=0,P=w(0),E=w(0),O=0,R=0,S=0,W=w(0),G=0,U=0,j=w(0),H=w(0),L=w(0),M=w(0),_=w(0),z=w(0),x=0,J=0,K=0,B=0,N=0,q=w(0),D=0,Z=0,Y=w(0);if(V=n=V-16|0,(k=v[v[r+24>>2]+60>>2])&&xe(0|La[v[v[k>>2]+8>>2]](k),21356)&&(q=p[r+40>>2],M=p[r+36>>2],q>w(0)|M>w(0))){K=v[r+12>>2],x=v[r+4>>2],s=v[x+48>>2],G=v[x+52>>2],v[n+12>>2]=0,An(r+44|0,C=!!(0|G)+K|0,n+12|0),a=p[r+32>>2];r:if(2==(0|G)|2!=(0|s)){if(2==(0|G)&&(v[n+8>>2]=0,An(r+108|0,K,n+8|0)),O=C-1|0)for(b=v[x+48>>2];;){o=v[(h=i<<2)+v[r+20>>2]>>2];n:if((c=p[v[o+4>>2]+24>>2])<w(9999999747378752e-21)){if(f=w(0),2!=(0|G))break n;v[h+v[r+120>>2]>>2]=0}else if(2!=(0|s))f=w(c*p[o+92>>2]),t=w(f*f),f=w(c*p[o+104>>2]),f=w(T(w(t+w(f*f)))),2==(0|G)&&(p[h+v[r+120>>2]>>2]=f),f=w(w(w(a+(b?w(-0):c))*f)/c);else{if(f=a,2!=(0|G))break n;f=w(c*p[o+92>>2]),t=w(f*f),f=w(c*p[o+104>>2]),p[h+v[r+120>>2]>>2]=T(w(t+w(f*f))),f=a}if(i=i+1|0,p[v[r+56>>2]+(i<<2)>>2]=f,!(i>>>0<O>>>0))break}}else{if(C>>>0<2)break r;for(o=v[r+56>>2],i=1;p[o+(i<<2)>>2]=a,(0|C)!=(0|(i=i+1|0)););}D=!G,U=1==v[x+44>>2],B=2==(0|s),V=R=V-16|0,h=r,u=p[r+28>>2],d=v[r+24>>2],v[R+12>>2]=0,An(O=r+60|0,m(C,3)+2|0,R+12|0),F=r+76|0,b=(0|(i=v[k+52>>2]))/6|0,J=l[k+80|0];r:if(l[k+81|0]){if(J?(v[R+12>>2]=0,An(F,S=i+2|0,R+12|0),Xe(k,d,2,r=i-2|0,F,0,2),Xe(k,d,0,2,F,r,2),r=v[h+88>>2],p[(i=r+(i<<2)|0)>>2]=p[r>>2],p[i+4>>2]=p[r+4>>2]):(v[R+12>>2]=0,An(F,S=i-4|0,R+12|0),Xe(k,d,2,S,F,0,2),b=b-1|0),i=0,v[R+12>>2]=0,An(h+92|0,b,R+12|0),s=v[h+88>>2],a=p[s+4>>2],c=p[s>>2],(0|b)>0)for(d=v[h+104>>2],o=2,f=a,t=c;P=p[(r=(N=o<<2)+s|0)>>2],_=p[r+8>>2],c=p[r+16>>2],a=w(w(w(w(w(P-_)*w(3))-t)+c)*w(.09375)),A=w(w(P-t)*w(.75)),t=w(w(w(t-w(P+P))+_)*w(.1875)),y=w(w(a*w(.1666666716337204))+w(A+t)),t=w(w(t+t)+a),W=w(y+t),t=w(a+t),j=w(W+t),A=w(j+w(a+t)),E=p[s+(4|N)>>2],z=p[r+12>>2],a=p[r+20>>2],t=w(w(w(w(w(E-z)*w(3))-f)+a)*w(.09375)),$=w(w(E-f)*w(.75)),f=w(w(w(f-w(E+E))+z)*w(.1875)),g=w(w(t*w(.1666666716337204))+w($+f)),$=w(w(f+f)+t),f=w(g+$),L=w(t+$),$=w(f+L),t=w($+w(t+L)),I=w(w(w(w(I+w(T(w(w(y*y)+w(g*g)))))+w(T(w(w(W*W)+w(f*f)))))+w(T(w(w(j*j)+w($*$)))))+w(T(w(w(A*A)+w(t*t))))),p[d+(i<<2)>>2]=I,o=o+6|0,j=a,f=a,W=c,t=c,(0|b)!=(0|(i=i+1|0)););if(f=I,f=U?f:w(f/p[(v[k+76>>2]+(b<<2)|0)-4>>2]),!(!B|(0|C)<2))for(i=v[h+56>>2],r=1;p[(o=i+(r<<2)|0)>>2]=I*p[o>>2],(0|C)!=(0|(r=r+1|0)););if(!((0|C)<=0))for(y=w(u*f),S=S-4|0,s=-1,i=0,r=0,k=0,b=0,u=w(0);;){Y=p[v[h+56>>2]+(b<<2)>>2],y=w(y+Y);n:{f:{if(J)o=0,f=Or(y,I),f=w(f+(f<w(0)?I:w(-0)));else{if(y<w(0)){Uf(y,F,O,k);break n}if(y>I)break f;o=r,f=y}for(d=v[h+104>>2];o=(r=o)+1|0,(t=p[(U=d+(r<<2)|0)>>2])<f;);if(r?(A=f,f=p[U-4>>2],f=w(w(A-f)/w(t-f))):f=w(f/t),A=f,(0|r)!=(0|s)){for(d=v[h+136>>2],i=v[h+88>>2]+m(r,24)|0,P=p[i+8>>2],_=p[i+16>>2],c=p[i>>2],W=p[i+24>>2],$=w(w(w(w(w(P-_)*w(3))-c)+W)*w(.006000000052154064)),g=w(w(w(c-w(P+P))+_)*w(.029999999329447746)),f=w(w($*w(.1666666716337204))+w(w(w(P-c)*w(.30000001192092896))+g)),E=p[i+12>>2],z=p[i+20>>2],a=p[i+4>>2],j=p[i+28>>2],L=w(w(w(w(w(E-z)*w(3))-a)+j)*w(.006000000052154064)),u=w(w(w(a-w(E+E))+z)*w(.029999999329447746)),t=w(w(L*w(.1666666716337204))+w(w(w(E-a)*w(.30000001192092896))+u)),H=w(T(w(w(f*f)+w(t*t)))),p[d>>2]=H,u=w(w(u+u)+L),g=w(w(g+g)+$),i=1;f=w(g+f),t=w(u+t),H=w(H+w(T(w(w(f*f)+w(t*t))))),p[(i<<2)+d>>2]=H,u=w(L+u),g=w($+g),8!=(0|(i=i+1|0)););f=w(g+f),t=w(u+t),H=w(H+w(T(w(w(f*f)+w(t*t))))),p[d+32>>2]=H,f=w(w($+g)+f),$=w(f*f),f=w(w(L+u)+t),u=w(H+w(T(w($+w(f*f))))),p[d+36>>2]=u,s=r,i=0}else d=v[h+136>>2];for(o=i,f=w(A*u);o=(i=o)+1|0,(t=p[(U=(i<<2)+d|0)>>2])<f;);i?(A=f,f=p[U-4>>2],f=w(w(w(A-f)/w(t-f))+w(0|i))):f=w(f/t),Vr(w(f*w(.10000000149011612)),c,a,P,E,_,z,W,j,O,k,!!(0|b)&Y<w(9999999747378752e-21)|D);break n}Cf(w(y-I),F,S,O,k)}if(k=k+3|0,(0|C)==(0|(b=b+1|0)))break}}else{if(o=(J?-1:-2)+b|0,a=p[v[k+76>>2]+(o<<2)>>2],!(!B|(0|C)<2))for(s=v[h+56>>2],r=1;p[(b=s+(r<<2)|0)>>2]=a*p[b>>2],(0|C)!=(0|(r=r+1|0)););if(v[R+12>>2]=0,An(F,8,R+12|0),(0|C)<=0)break r;for(u=w(u*(U?a:w(1))),U=i-4|0,B=i-6|0,s=-1,r=0,b=0;;){t=p[v[h+56>>2]+(S<<2)>>2],u=w(u+t);n:{if(J)f=Or(u,a),f=w(f+(f<w(0)?a:w(-0))),i=0;else{if(u<w(0)){-2!=(0|s)&&Xe(k,d,2,4,F,0,2),Uf(u,F,O,b),s=-2;break n}if(u>a){-3!=(0|s)&&Xe(k,d,B,4,F,0,2),Cf(w(u-a),F,0,O,b),s=-3;break n}i=r,f=u}for(N=v[k+76>>2];i=(r=i)+1|0,(c=p[(Z=N+(r<<2)|0)>>2])<f;);r?(A=f,f=p[Z-4>>2],f=w(w(A-f)/w(c-f))):f=w(f/c),(0|r)!=(0|s)&&(!J|(0|r)!=(0|o)?(Xe(k,d,m(r,6)+2|0,8,F,0,2),s=r):(Xe(k,d,U,4,F,0,2),Xe(k,d,0,4,F,4,2),s=o)),i=v[h+88>>2],Vr(f,p[i>>2],p[i+4>>2],p[i+8>>2],p[i+12>>2],p[i+16>>2],p[i+20>>2],p[i+24>>2],p[i+28>>2],O,b,!!(0|S)&t<w(9999999747378752e-21)|D)}if(b=b+3|0,(0|C)==(0|(S=S+1|0)))break}}if(V=R+16|0,r=v[O+12>>2],f=p[r+4>>2],a=p[r>>2],(W=p[x+56>>2])==w(0)?r=1==(0|G):(r=v[v[h+24>>2]+8>>2],W=w(W*(w(w(p[r+92>>2]*p[r+108>>2])-w(p[r+104>>2]*p[r+96>>2]))>w(0)?w(.01745329238474369):w(-.01745329238474369))),r=0),K)for(i=0,s=3;o=v[(k=i<<2)+v[h+20>>2]>>2],c=p[o+100>>2],p[o+100>>2]=w(w(a-c)*q)+c,c=p[o+112>>2],p[o+112>>2]=w(w(f-c)*q)+c,b=v[O+12>>2]+(s<<2)|0,c=p[b>>2],y=w(c-a),a=p[b+4>>2],f=w(a-f),2==(0|G)&&(t=p[k+v[h+120>>2]>>2])>=w(9999999747378752e-21)&&(t=w(w(w(w(w(T(w(w(y*y)+w(f*f))))/t)+w(-1))*M)+w(1)),p[o+92>>2]=t*p[o+92>>2],p[o+104>>2]=t*p[o+104>>2]),M>w(0)?(j=p[o+108>>2],t=p[o+104>>2],A=p[o+96>>2],I=p[o+92>>2],u=p[b-4>>2],G&&(u=p[b+8>>2],p[4+(k+v[h+56>>2]|0)>>2]<w(9999999747378752e-21)||(u=Rr(f,y))),u=w(u-Rr(t,I)),r?(P=Ur(u),E=Hr(u),g=p[v[o+4>>2]+24>>2],f=w(w(w(w(g*w(w(E*I)+w(t*P)))-f)*M)+a),a=w(w(w(w(g*w(w(P*I)-w(t*E)))-y)*M)+c)):(u=w(W+u),f=a,a=c),u>w(3.1415927410125732)?u=w(u+w(-6.2831854820251465)):u<w(-3.1415927410125732)&&(u=w(u+w(6.2831854820251465))),c=Ur(u=w(M*u)),u=Hr(u),p[o+108>>2]=w(u*A)+w(j*c),p[o+104>>2]=w(u*I)+w(t*c),p[o+96>>2]=w(c*A)-w(j*u),p[o+92>>2]=w(c*I)-w(t*u)):(f=a,a=c),e[o+88|0]=0,s=s+3|0,(0|K)!=(0|(i=i+1|0)););}V=n+16|0},function(r){return l[140+(r|=0)|0]},function(r,n){n|=0,e[140+(r|=0)|0]=n},ju,ja,function(r){return v[(r|=0)>>2]=9960,Go(r+24|0),0|eu(r)},function(r){v[(r|=0)>>2]=9960,Go(r+24|0),Ct(eu(r))},function(){return 21380},ja,function(r){return v[(r|=0)>>2]=9984,Lo(r+20|0),0|nu(r)},function(r){v[(r|=0)>>2]=9984,Lo(r+20|0),Ct(nu(r))},function(){return 21392},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=w(0),c=0,b=w(0);r:if(n=v[v[n+100>>2]+(v[r+36>>2]<<2)>>2],0|La[v[v[n>>2]+16>>2]](n)){if(e=v[r+32>>2],p[e>>2]>i){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+36>>2]=p[r+68>>2],void(p[n+40>>2]=p[r+72>>2]);case 1:break n;default:break r}return r=v[n+4>>2],f=p[n+36>>2],p[n+36>>2]=w(w(p[r+68>>2]-f)*t)+f,f=p[n+40>>2],void(p[n+40>>2]=w(w(p[r+72>>2]-f)*t)+f)}if(e=e+(v[r+24>>2]<<2)|0,p[e-12>>2]<=i?(i=p[e-4>>2],f=p[e-8>>2]):(e=(c=(o=Lf(r+20|0,i,3))<<2)+v[r+32>>2]|0,f=p[e-8>>2],b=p[e-4>>2],a=i,i=p[e>>2],a=pn(r,((0|o)/3|0)-1|0,w(w(1)-w(w(a-i)/w(p[e-12>>2]-i)))),r=v[r+32>>2]+c|0,i=w(b+w(a*w(p[r+8>>2]-b))),f=w(f+w(a*w(p[r+4>>2]-f)))),!u)return a=f,r=v[n+4>>2],f=p[r+68>>2],p[n+36>>2]=w(w(a-f)*t)+f,f=p[r+72>>2],void(p[n+40>>2]=w(w(i-f)*t)+f);a=f,f=p[n+36>>2],p[n+36>>2]=w(w(a-f)*t)+f,f=p[n+40>>2],p[n+40>>2]=w(w(i-f)*t)+f}},function(r){return v[36+(r|=0)>>2]+218103808|0},ja,_t,function(r){_t(r|=0),Ct(r)},function(){return 21404},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0);r:if(n=v[v[n+100>>2]+(v[r+36>>2]<<2)>>2],0|La[v[v[n>>2]+16>>2]](n)){if(e=v[r+32>>2],p[e>>2]>i){n:switch(0|u){case 0:return void(p[n+28>>2]=p[v[n+4>>2]+60>>2]);case 1:break n;default:break r}return f=p[n+28>>2],void(p[n+28>>2]=w(w(p[v[n+4>>2]+60>>2]-f)*t)+f)}if(e=e+(v[r+24>>2]<<2)|0,p[e-8>>2]<=i?f=p[e-4>>2]:(e=(a=(o=Lf(r+20|0,i,2))<<2)+v[r+32>>2]|0,f=p[e-4>>2],c=i,i=p[e>>2],f=w(f+w(pn(r,((0|o)/2|0)-1|0,w(w(1)-w(w(c-i)/w(p[e-8>>2]-i))))*w(p[4+(v[r+32>>2]+a|0)>>2]-f)))),!u)return i=f,f=p[v[n+4>>2]+60>>2],void(p[n+28>>2]=w(w(i-f)*t)+f);i=f,f=p[n+28>>2],p[n+28>>2]=w(w(i-f)*t)+f}},function(r){return v[36+(r|=0)>>2]+184549376|0},ja,function(r){Ct(_t(r|=0))},function(){return 21416},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0);r:if(n=v[v[n+100>>2]+(v[r+36>>2]<<2)>>2],0|La[v[v[n>>2]+16>>2]](n)){if(e=v[r+32>>2],p[e>>2]>i){n:switch(0|u){case 0:return void(p[n+32>>2]=p[v[n+4>>2]+64>>2]);case 1:break n;default:break r}return f=p[n+32>>2],void(p[n+32>>2]=w(w(p[v[n+4>>2]+64>>2]-f)*t)+f)}if(a=v[r+24>>2],o=v[2501],p[e+(a-o<<2)>>2]<=i?f=p[e+(a+v[2503]<<2)>>2]:(e=Lf(r+20|0,i,o),a=v[r+32>>2],f=p[a+(e+v[2503]<<2)>>2],c=i,i=p[a+(e<<2)>>2],f=w(f+w(pn(r,((0|e)/(0|o)|0)-1|0,w(w(1)-w(w(c-i)/w(p[a+(e+v[2502]<<2)>>2]-i))))*w(p[v[r+32>>2]+(e+v[2504]<<2)>>2]-f)))),!u)return i=f,f=p[v[n+4>>2]+64>>2],void(p[n+32>>2]=w(w(i-f)*t)+f);i=f,f=p[n+32>>2],p[n+32>>2]=w(w(i-f)*t)+f}},function(r){return v[36+(r|=0)>>2]+201326592|0},ja,function(r){Ct(ru(r|=0))},function(){return 21428},function(r){var n;return r|=0,tt(n=ht(32),ha(r)),p[n+20>>2]=p[r+20>>2],p[n+24>>2]=p[r+24>>2],p[n+28>>2]=p[r+28>>2],0|n},Wa,ja,function(r){var n,f=0;return v[20+(r|=0)>>2]=10148,v[r>>2]=10124,Oe(r+116|0),Lo(r+100|0),Lo(r+84|0),v[r+20>>2]=8748,(n=v[r+28>>2])&&(f=v[r+24>>2])&&La[0|n](f),0|ru(r)},function(r){var n,f=0;v[20+(r|=0)>>2]=10148,v[r>>2]=10124,Oe(r+116|0),Lo(r+100|0),Lo(r+84|0),v[r+20>>2]=8748,(n=v[r+28>>2])&&(f=v[r+24>>2])&&La[0|n](f),Ct(ru(r))},function(){return 21440},function(r){r|=0;var n,f,i,e=0,t=w(0);return xr(n=ht(164),ha(r)),p[n+68>>2]=p[r+68>>2],p[n+72>>2]=p[r+72>>2],p[n+60>>2]=p[r+60>>2],p[n+64>>2]=p[r+64>>2],p[n+76>>2]=p[r+76>>2],p[n+80>>2]=p[r+80>>2],f=v[r+24>>2],(i=v[n+28>>2])&&(!(e=v[n+24>>2])|(0|f)==(0|e)||La[0|i](e)),v[n+28>>2]=0,v[n+24>>2]=f,sf(n+116|0,r+116|0),p[n+32>>2]=p[r+32>>2],p[n+36>>2]=p[r+36>>2],p[n+44>>2]=p[r+44>>2],p[n+48>>2]=p[r+48>>2],p[n+40>>2]=p[r+40>>2],p[n+52>>2]=p[r+52>>2],t=p[r+56>>2],v[n+104>>2]=0,p[n+56>>2]=t,te(n+100|0,r+100|0),v[n+88>>2]=0,te(n+84|0,r+84|0),p[n+148>>2]=p[r+148>>2],p[n+152>>2]=p[r+152>>2],p[n+156>>2]=p[r+156>>2],p[n+160>>2]=p[r+160>>2],mi(n+144|0),0|n},function(r){var n,f;return v[(r|=0)>>2]=10148,v[(n=r-20|0)>>2]=10124,Oe(r+96|0),Lo(r+80|0),Lo(r- -64|0),v[r>>2]=8748,(f=v[r+8>>2])&&(r=v[r+4>>2])&&La[0|f](r),0|ru(n)},function(r){var n,f;v[(r|=0)>>2]=10148,v[(n=r-20|0)>>2]=10124,Oe(r+96|0),Lo(r+80|0),Lo(r- -64|0),v[r>>2]=8748,(f=v[r+8>>2])&&(r=v[r+4>>2])&&La[0|f](r),Ct(ru(n))},ja,function(r){return v[(r|=0)>>2]=10164,Lo(r+24|0),0|nu(r)},function(r){v[(r|=0)>>2]=10164,Lo(r+24|0),Ct(nu(r))},function(){return 21452},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0),b=0;r:if(n=v[v[n+20>>2]+(v[r+20>>2]<<2)>>2],l[n+117|0]){if(e=v[r+36>>2],p[e>>2]>i){n:switch(0|u){case 0:return void(p[n+40>>2]=p[v[n+4>>2]+36>>2]);case 1:break n;default:break r}return f=p[n+40>>2],i=w(p[v[n+4>>2]+36>>2]-f),a=+w(i/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,void(p[n+40>>2]=w(w(i-w(0|m(16384-r|0,360)))*t)+f)}if(e=e+(v[r+28>>2]<<2)|0,p[e-8>>2]<=i){i=p[e-4>>2];n:{f:switch(0|u){case 3:f=p[n+40>>2];break n;case 0:return void(p[n+40>>2]=w(i*t)+p[v[n+4>>2]+36>>2]);case 1:case 2:break f;default:break r}f=p[n+40>>2],i=w(i+w(p[v[n+4>>2]+36>>2]-f)),a=+w(i/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,i=w(i-w(0|m(16384-r|0,360)))}return void(p[n+40>>2]=w(i*t)+f)}e=(b=(o=Lf(r+24|0,i,2))<<2)+v[r+36>>2]|0,f=p[e-4>>2],c=i,i=p[e>>2],i=pn(r,(o>>1)-1|0,w(w(1)-w(w(c-i)/w(p[e-8>>2]-i)))),c=w(p[4+(v[r+36>>2]+b|0)>>2]-f),a=+w(c/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,i=w(w(w(c-w(0|m(16384-r|0,360)))*i)+f);n:{f:switch(0|u){case 3:f=p[n+40>>2];break n;case 0:return a=+w(i/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,void(p[n+40>>2]=w(w(i-w(0|m(16384-r|0,360)))*t)+p[v[n+4>>2]+36>>2]);case 1:case 2:break f;default:break r}f=p[n+40>>2],i=w(i+w(p[v[n+4>>2]+36>>2]-f))}a=+w(i/w(-360))+16384.499999999996,r=g(a)<2147483648?~~a:-2147483648,p[n+40>>2]=w(w(i-w(0|m(16384-r|0,360)))*t)+f}},qo,ja,zt,wa,function(){return 21464},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=w(0),c=w(0),b=0,k=w(0),s=w(0),h=0,d=w(0),y=0,m=w(0);r:{n=v[v[n+20>>2]+(v[r+36>>2]<<2)>>2];n:if(l[n+117|0]){if(e=v[r+32>>2],p[e>>2]>i){f:switch(0|u){case 0:return r=v[n+4>>2],p[n+44>>2]=p[r+40>>2],void(p[n+48>>2]=p[r+44>>2]);case 1:break f;default:break n}return r=v[n+4>>2],f=p[n+44>>2],p[n+44>>2]=w(w(p[r+40>>2]-f)*t)+f,f=p[n+48>>2],void(p[n+48>>2]=w(w(p[r+44>>2]-f)*t)+f)}if(b=v[r+24>>2],h=v[2767],p[e+(b-h<<2)>>2]<=i?(r=v[n+4>>2],c=p[r+44>>2],i=w(p[e+(b+v[2770]<<2)>>2]*c),a=p[r+40>>2],f=w(p[e+(b+v[2769]<<2)>>2]*a)):(e=Lf(r+20|0,i,h),b=v[r+32>>2],f=p[b+(e+v[2769]<<2)>>2],a=p[b+(e+v[2770]<<2)>>2],k=i,i=p[b+(e<<2)>>2],s=pn(r,((0|e)/(0|h)|0)-1|0,w(w(1)-w(w(k-i)/w(p[b+(e+v[2768]<<2)>>2]-i)))),b=v[n+4>>2],c=p[b+44>>2],r=v[r+32>>2],i=w(c*w(a+w(s*w(p[r+(e+v[2772]<<2)>>2]-a)))),a=p[b+40>>2],f=w(w(f+w(s*w(p[r+(e+v[2771]<<2)>>2]-f)))*a)),t==w(1))return 3==(0|u)?(p[n+44>>2]=p[n+44>>2]+w(f-a),void(p[n+48>>2]=p[n+48>>2]+w(i-c))):(p[n+48>>2]=i,void(p[n+44>>2]=f));if(1==(0|o)){f:switch(0|u){case 0:return y=n,m=w(w(w(w(w(g(f))*hu(a))-a)*t)+a),p[y+44>>2]=m,y=n,m=w(w(w(w(w(g(i))*hu(c))-c)*t)+c),void(p[y+48>>2]=m);case 1:case 2:return c=p[n+48>>2],a=p[n+44>>2],y=n,m=w(a+w(w(w(w(g(f))*hu(a))-a)*t)),p[y+44>>2]=m,y=n,m=w(c+w(w(w(w(g(i))*hu(c))-c)*t)),void(p[y+48>>2]=m);case 3:break f;default:break n}return c=p[n+48>>2],k=w(g(f)),f=p[n+44>>2],a=w(k*hu(f)),p[n+44>>2]=f+w(w(a-p[v[n+4>>2]+40>>2])*t),y=n,m=w(c+w(w(w(w(g(i))*hu(c))-p[v[n+4>>2]+44>>2])*t)),void(p[y+48>>2]=m)}f:switch(0|u){case 0:c=hu(f),s=p[v[n+4>>2]+44>>2];break r;case 1:case 2:a=p[n+44>>2],c=hu(f),s=p[n+48>>2];break r;case 3:break f;default:break n}c=hu(f),a=hu(i),r=v[n+4>>2],p[n+44>>2]=w(c*w(g(p[n+44>>2])))+w(w(f-w(c*w(g(p[r+40>>2]))))*t),p[n+48>>2]=w(a*w(g(p[n+48>>2])))+w(w(i-w(a*w(g(p[r+44>>2]))))*t)}return}d=hu(i),k=f,f=w(c*w(g(a))),p[n+44>>2]=w(w(k-f)*t)+f,f=w(d*w(g(s))),p[n+48>>2]=w(w(i-f)*t)+f},function(r){return v[36+(r|=0)>>2]+33554432|0},ja,wa,function(){return 21476},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=w(0),c=0,b=w(0);r:if(n=v[v[n+20>>2]+(v[r+36>>2]<<2)>>2],l[n+117|0]){if(e=v[r+32>>2],p[e>>2]>i){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+52>>2]=p[r+48>>2],void(p[n+56>>2]=p[r+52>>2]);case 1:break n;default:break r}return r=v[n+4>>2],f=p[n+52>>2],p[n+52>>2]=w(w(p[r+48>>2]-f)*t)+f,f=p[n+56>>2],void(p[n+56>>2]=w(w(p[r+52>>2]-f)*t)+f)}o=v[r+24>>2],c=v[2767],p[e+(o-c<<2)>>2]<=i?(i=p[e+(o+v[2770]<<2)>>2],f=p[e+(o+v[2769]<<2)>>2]):(e=Lf(r+20|0,i,c),o=v[r+32>>2],f=p[o+(e+v[2769]<<2)>>2],b=p[o+(e+v[2770]<<2)>>2],a=i,i=p[o+(e<<2)>>2],a=pn(r,((0|e)/(0|c)|0)-1|0,w(w(1)-w(w(a-i)/w(p[o+(e+v[2768]<<2)>>2]-i)))),r=v[r+32>>2],i=w(b+w(a*w(p[r+(e+v[2772]<<2)>>2]-b))),f=w(f+w(a*w(p[r+(e+v[2771]<<2)>>2]-f))));n:switch(0|u){case 0:return r=v[n+4>>2],p[n+52>>2]=w(f*t)+p[r+48>>2],void(p[n+56>>2]=w(i*t)+p[r+52>>2]);case 1:case 2:return r=v[n+4>>2],a=w(f+p[r+48>>2]),f=p[n+52>>2],p[n+52>>2]=w(w(a-f)*t)+f,f=p[n+56>>2],void(p[n+56>>2]=w(w(w(i+p[r+52>>2])-f)*t)+f);case 3:break n;default:break r}p[n+52>>2]=w(f*t)+p[n+52>>2],p[n+56>>2]=w(i*t)+p[n+56>>2]}},function(r){return v[36+(r|=0)>>2]+50331648|0},Bf,function(r){Bf(r|=0),Ct(r)},ko,function(r){Ct(ko(r|=0))},Nu,function(r){Ct(Nu(r|=0))},Bu,function(r){Ct(Bu(r|=0))},Ku,function(r){Ct(Ku(r|=0))},zu,function(r){Ct(zu(r|=0))},ja,gi,function(r){gi(r|=0),Ct(r)},Wa,lo,function(r){Ct(lo(r|=0))},Jf,function(r){Jf(r|=0),Ct(r)},yt,function(r){Ct(yt(r|=0))},Vu,function(r){Ct(Vu(r|=0))},yo,function(r){Ct(yo(r|=0))},function(r){return v[(r|=0)>>2]=10456,Lo(r+4|0),0|r},function(r){v[(r|=0)>>2]=10456,Lo(r+4|0),Ct(r)},function(r){return v[(r|=0)>>2]=10472,Lo(r+208|0),Lo(r+192|0),jo(r+176|0),Lo(r+160|0),Lo(r+144|0),Lo(r+128|0),qi(r+4|0),0|r},function(r){v[(r|=0)>>2]=10472,Lo(r+208|0),Lo(r+192|0),jo(r+176|0),Lo(r+160|0),Lo(r+144|0),Lo(r+128|0),qi(r+4|0),Ct(r)},mo,function(r){Ct(mo(r|=0))},wo,function(r){Ct(wo(r|=0))},Yu,function(r){Ct(Yu(r|=0))},mt,function(r){Ct(mt(r|=0))},wt,function(r){Ct(wt(r|=0))},yn,function(r){yn(r|=0),Ct(r)},Fo,function(r){Ct(Fo(r|=0))},oo,function(r){Ct(oo(r|=0))},uo,function(r){Ct(uo(r|=0))},to,function(r){Ct(to(r|=0))},eo,function(r){Ct(eo(r|=0))},io,function(r){Ct(io(r|=0))},fo,function(r){Ct(fo(r|=0))},no,function(r){Ct(no(r|=0))},Mi,function(r){Mi(r|=0),Ct(r)},function(r){return v[(r|=0)>>2]=10800,Lo(r+20|0),go(r+4|0),0|r},function(r){v[(r|=0)>>2]=10800,Lo(r+20|0),go(r+4|0),Ct(r)},go,function(r){Ct(go(r|=0))},function(r){return v[(r|=0)>>2]=10832,co(r+4|0),0|r},function(r){v[(r|=0)>>2]=10832,co(r+4|0),Ct(r)},tn,function(r){tn(r|=0),Ct(r)},co,function(r){Ct(co(r|=0))},gf,function(r){Ct(gf(r|=0))},bo,function(r){Ct(bo(r|=0))},function(r){return v[(r|=0)>>2]=10912,Lo(r+72|0),0|r},function(r){v[(r|=0)>>2]=10912,Lo(r+72|0),Ct(r)},function(r){return v[(r|=0)>>2]=10928,Oe(r+68|0),Oe(r+8|0),0|r},function(r){v[(r|=0)>>2]=10928,Oe(r+68|0),Oe(r+8|0),Ct(r)},Oe,function(r){Oe(r|=0),Ct(r)},ja,ma,Ua,function(){return 21512},ja,function(r){return v[(r|=0)>>2]=10988,Uo(r+8|0),0|r},function(r){v[(r|=0)>>2]=10988,Uo(r+8|0),Ct(r)},function(){return 21524},function(r){r|=0;var n=w(0),f=0,i=0,t=w(0),u=0,o=w(0),a=w(0),c=0,b=0,k=w(0),s=w(0),h=w(0),y=w(0),F=w(0),A=w(0),$=w(0),I=w(0),C=w(0),P=w(0),E=w(0),O=w(0),R=w(0),S=w(0),W=0,G=0,U=0,j=w(0);if(i=v[r+4>>2],b=l[i+84|0],l[i+85|0]){if(b){if(k=p[r+40>>2],s=p[r+36>>2],h=p[r+32>>2],a=p[r+28>>2],u=v[r+24>>2],l[u+88|0]||Tr(u),v[r+12>>2])for(;i=v[v[r+20>>2]+(c<<2)>>2],l[i+88|0]||Tr(i),o=p[i+68>>2],o=a!=w(0)?w(w(w(p[u+68>>2]+p[v[r+4>>2]+60>>2])*a)+o):o,$=p[i+64>>2],I=p[i+60>>2],h!=w(0)&&(b=v[r+4>>2],$=w(w(w(p[u+64>>2]+p[b+68>>2])*h)+$),I=w(w(w(p[u+60>>2]+p[b+64>>2])*h)+I)),n=p[i+76>>2],t=p[i+72>>2],s!=w(0)&&(t=t>w(9999999747378752e-21)?w(t*w(w(w(w(p[u+72>>2]+w(-1))+p[v[r+4>>2]+72>>2])*s)+w(1))):t,n>w(9999999747378752e-21)&&(n=w(n*w(w(w(w(p[u+76>>2]+w(-1))+p[v[r+4>>2]+76>>2])*s)+w(1))))),C=p[i+84>>2],y=p[i+80>>2],k!=w(0)&&(C=w(w(w(p[u+84>>2]+p[v[r+4>>2]+80>>2])*k)+C)),kr(i,I,$,o,t,n,y,C),(c=c+1|0)>>>0<d[r+12>>2];);return}if(y=p[r+40>>2],k=p[r+36>>2],a=p[r+32>>2],C=p[r+28>>2],b=v[r+24>>2],l[b+88|0]||Tr(b),v[r+12>>2])for(;u=v[v[r+20>>2]+(c<<2)>>2],l[u+88|0]||Tr(u),s=p[u+68>>2],C!=w(0)&&(n=w(w(p[b+68>>2]-s)+p[v[r+4>>2]+60>>2]),G=+w(n/w(-360))+16384.499999999996,i=g(G)<2147483648?~~G:-2147483648,s=w(w(w(n-w(0|m(16384-i|0,360)))*C)+s)),h=p[u+64>>2],o=p[u+60>>2],a!=w(0)&&(i=v[r+4>>2],h=w(w(w(w(p[b+64>>2]-h)+p[i+68>>2])*a)+h),o=w(w(w(w(p[b+60>>2]-o)+p[i+64>>2])*a)+o)),n=p[u+76>>2],t=p[u+72>>2],k!=w(0)&&(t=t>w(9999999747378752e-21)?w(w(w(w(w(p[b+72>>2]-t)+p[v[r+4>>2]+72>>2])*k)+t)/t):t,n>w(9999999747378752e-21)&&(n=w(w(w(w(w(p[b+76>>2]-n)+p[v[r+4>>2]+76>>2])*k)+n)/n))),I=p[u+84>>2],y!=w(0)&&($=w(w(p[b+84>>2]-I)+p[v[r+4>>2]+80>>2]),G=+w($/w(-360))+16384.499999999996,i=g(G)<2147483648?~~G:-2147483648,p[u+56>>2]=w(w($-w(0|m(16384-i|0,360)))*y)+p[u+56>>2]),kr(u,o,h,s,t,n,p[u+80>>2],I),(c=c+1|0)>>>0<d[r+12>>2];);}else if(b){if(V=u=V-16|0,v[r+12>>2])for(i=v[r+4>>2],c=v[r+24>>2],F=p[c+92>>2],P=p[c+108>>2],A=p[c+104>>2],E=p[c+96>>2],n=w(w(F*P)-w(A*E))>w(0)?w(.01745329238474369):w(-.01745329238474369),C=w(p[i+80>>2]*n),I=w(p[i+60>>2]*n),R=p[r+40>>2],S=p[r+36>>2],s=p[r+32>>2],b=(h=p[r+28>>2])!=w(0),$=w(w(T(w(w(E*E)+w(P*P))))+w(-1)),o=w(w(T(w(w(F*F)+w(A*A))))+w(-1));;){f=v[v[r+20>>2]+(W<<2)>>2],h!=w(0)&&(k=p[f+108>>2],a=p[f+104>>2],y=p[f+96>>2],t=p[f+92>>2],(n=w(I+Rr(A,F)))>w(3.1415927410125732)?n=w(n+w(-6.2831854820251465)):n<w(-3.1415927410125732)&&(n=w(n+w(6.2831854820251465))),O=Ur(n=w(h*n)),n=Hr(n),p[f+108>>2]=w(n*y)+w(k*O),p[f+104>>2]=w(n*t)+w(a*O),p[f+96>>2]=w(O*y)-w(k*n),p[f+92>>2]=w(O*t)-w(a*n)),i=b,s!=w(0)&&(i=v[r+4>>2],he(c,p[i+64>>2],p[i+68>>2],u+12|0,u+8|0),p[f+100>>2]=w(p[u+12>>2]*s)+p[f+100>>2],p[f+112>>2]=w(p[u+8>>2]*s)+p[f+112>>2],i=1);r:{n:{f:{if(!(S>w(0))){if(R>w(0))break f;if(i)break n;break r}if(i=v[r+4>>2],n=w(w(w(o+p[i+72>>2])*S)+w(1)),p[f+92>>2]=p[f+92>>2]*n,p[f+104>>2]=n*p[f+104>>2],n=w(w(w($+p[i+76>>2])*S)+w(1)),p[f+96>>2]=p[f+96>>2]*n,p[f+108>>2]=n*p[f+108>>2],!(R>w(0)))break n}(n=w(Rr(P,E)-Rr(A,F)))>w(3.1415927410125732)?n=w(n+w(-6.2831854820251465)):n<w(-3.1415927410125732)&&(n=w(n+w(6.2831854820251465))),t=w(w(C+w(n+w(-1.5707963705062866)))*R),y=p[f+108>>2],n=p[f+96>>2],t=w(t+Rr(y,n)),n=w(T(w(w(n*n)+w(y*y)))),U=f,j=w(Hr(t)*n),p[U+108>>2]=j,U=f,j=w(Ur(t)*n),p[U+96>>2]=j}e[f+88|0]=0}if(!((W=W+1|0)>>>0<d[r+12>>2]))break}V=u+16|0}else{if(V=u=V-16|0,v[r+12>>2])for(i=v[r+4>>2],c=v[r+24>>2],F=p[c+92>>2],P=p[c+108>>2],A=p[c+104>>2],E=p[c+96>>2],n=w(w(F*P)-w(A*E))>w(0)?w(.01745329238474369):w(-.01745329238474369),y=w(p[i+80>>2]*n),C=w(p[i+60>>2]*n),R=p[r+40>>2],S=p[r+36>>2],s=p[r+32>>2],b=(h=p[r+28>>2])!=w(0),I=w(T(w(w(E*E)+w(P*P)))),$=w(T(w(w(F*F)+w(A*A))));;){f=v[v[r+20>>2]+(W<<2)>>2],h!=w(0)&&(k=p[f+108>>2],a=p[f+96>>2],n=Rr(A,F),o=p[f+104>>2],t=p[f+92>>2],(n=w(C+w(n-Rr(o,t))))>w(3.1415927410125732)?n=w(n+w(-6.2831854820251465)):n<w(-3.1415927410125732)&&(n=w(n+w(6.2831854820251465))),O=Ur(n=w(h*n)),n=Hr(n),p[f+108>>2]=w(n*a)+w(k*O),p[f+104>>2]=w(n*t)+w(o*O),p[f+96>>2]=w(O*a)-w(k*n),p[f+92>>2]=w(O*t)-w(o*n)),i=b,s!=w(0)&&(i=v[r+4>>2],he(c,p[i+64>>2],p[i+68>>2],u+12|0,u+8|0),n=p[f+100>>2],p[f+100>>2]=w(w(p[u+12>>2]-n)*s)+n,n=p[f+112>>2],p[f+112>>2]=w(w(p[u+8>>2]-n)*s)+n,i=1);r:{n:{f:{i:{if(S>w(0)){if(o=p[f+92>>2],t=p[f+104>>2],(n=w(T(w(w(o*o)+w(t*t)))))>w(9999999747378752e-21)&&(n=w(w(w(w(w($-n)+p[v[r+4>>2]+72>>2])*S)+n)/n)),k=w(t*n),p[f+104>>2]=k,t=w(o*n),p[f+92>>2]=t,a=p[f+96>>2],o=p[f+108>>2],(n=w(T(w(w(a*a)+w(o*o)))))>w(9999999747378752e-21)&&(n=w(w(w(w(w(I-n)+p[v[r+4>>2]+76>>2])*S)+n)/n)),o=w(o*n),p[f+108>>2]=o,n=w(a*n),p[f+96>>2]=n,R>w(0))break i;break n}if(!(R>w(0)))break f;t=p[f+92>>2],k=p[f+104>>2],o=p[f+108>>2],n=p[f+96>>2]}a=Rr(o,n),(t=w(w(Rr(P,E)-Rr(A,F))-w(a-Rr(k,t))))>w(3.1415927410125732)?t=w(t+w(-6.2831854820251465)):t<w(-3.1415927410125732)&&(t=w(t+w(6.2831854820251465))),o=w(T(w(w(n*n)+w(o*o)))),n=w(w(w(y+t)*R)+a),U=f,j=w(o*Hr(n)),p[U+108>>2]=j,U=f,j=w(o*Ur(n)),p[U+96>>2]=j;break n}if(!i)break r}e[f+88|0]=0}if(!((W=W+1|0)>>>0<d[r+12>>2]))break}V=u+16|0}},Xo,To,ju,ja,function(r){return v[(r|=0)>>2]=11024,Go(r+24|0),0|eu(r)},function(r){v[(r|=0)>>2]=11024,Go(r+24|0),Ct(eu(r))},function(){return 21536},ja,function(r){return v[(r|=0)>>2]=11048,Lo(r+20|0),0|nu(r)},function(r){v[(r|=0)>>2]=11048,Lo(r+20|0),Ct(nu(r))},function(){return 21548},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=w(0),c=w(0),b=w(0),k=0,s=w(0);r:if(n=v[v[n+84>>2]+(v[r+36>>2]<<2)>>2],0|La[v[v[n>>2]+16>>2]](n)){if(e=v[r+32>>2],p[e>>2]>i){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+28>>2]=p[r+44>>2],p[n+32>>2]=p[r+48>>2],p[n+36>>2]=p[r+52>>2],void(p[n+40>>2]=p[r+56>>2]);case 1:break n;default:break r}return r=v[n+4>>2],f=p[n+28>>2],p[n+28>>2]=w(w(p[r+44>>2]-f)*t)+f,f=p[n+32>>2],p[n+32>>2]=w(w(p[r+48>>2]-f)*t)+f,f=p[n+36>>2],p[n+36>>2]=w(w(p[r+52>>2]-f)*t)+f,f=p[n+40>>2],void(p[n+40>>2]=w(w(p[r+56>>2]-f)*t)+f)}if(e=e+(v[r+24>>2]<<2)|0,p[e-20>>2]<=i?(i=p[e-4>>2],f=p[e-8>>2],c=p[e-12>>2],a=p[e-16>>2]):(e=(k=(o=Lf(r+20|0,i,5))<<2)+v[r+32>>2]|0,b=p[e-16>>2],c=p[e-12>>2],f=p[e-8>>2],s=p[e-4>>2],a=i,i=p[e>>2],a=pn(r,((0|o)/5|0)-1|0,w(w(1)-w(w(a-i)/w(p[e-20>>2]-i)))),r=v[r+32>>2]+k|0,i=w(s+w(a*w(p[r+16>>2]-s))),f=w(f+w(a*w(p[r+12>>2]-f))),c=w(c+w(a*w(p[r+8>>2]-c))),a=w(b+w(a*w(p[r+4>>2]-b)))),!u)return b=a,r=v[n+4>>2],a=p[r+44>>2],p[n+28>>2]=w(w(b-a)*t)+a,a=p[r+48>>2],p[n+32>>2]=w(w(c-a)*t)+a,a=f,f=p[r+52>>2],p[n+36>>2]=w(w(a-f)*t)+f,f=p[r+56>>2],void(p[n+40>>2]=w(w(i-f)*t)+f);b=a,a=p[n+28>>2],p[n+28>>2]=w(w(b-a)*t)+a,a=p[n+32>>2],p[n+32>>2]=w(w(c-a)*t)+a,a=f,f=p[n+36>>2],p[n+36>>2]=w(w(a-f)*t)+f,f=p[n+40>>2],p[n+40>>2]=w(w(i-f)*t)+f}},function(r){return v[36+(r|=0)>>2]+167772160|0},ja,function(r){zt(r|=0),Ct(r)},function(){return 21560},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=w(0),c=0,b=w(0);r:if(n=v[v[n+20>>2]+(v[r+36>>2]<<2)>>2],l[n+117|0]){if(e=v[r+32>>2],p[e>>2]>i){n:switch(0|u){case 0:return r=v[n+4>>2],p[n+32>>2]=p[r+28>>2],void(p[n+36>>2]=p[r+32>>2]);case 1:break n;default:break r}return r=v[n+4>>2],f=p[n+32>>2],p[n+32>>2]=w(w(p[r+28>>2]-f)*t)+f,f=p[n+36>>2],void(p[n+36>>2]=w(w(p[r+32>>2]-f)*t)+f)}e=e+(v[r+24>>2]<<2)|0,p[e-12>>2]<=i?(i=p[e-4>>2],f=p[e-8>>2]):(e=(c=(o=Lf(r+20|0,i,3))<<2)+v[r+32>>2]|0,f=p[e-8>>2],b=p[e-4>>2],a=i,i=p[e>>2],a=pn(r,((0|o)/3|0)-1|0,w(w(1)-w(w(a-i)/w(p[e-12>>2]-i)))),r=v[r+32>>2]+c|0,i=w(b+w(a*w(p[r+8>>2]-b))),f=w(f+w(a*w(p[r+4>>2]-f))));n:switch(0|u){case 0:return r=v[n+4>>2],p[n+32>>2]=w(f*t)+p[r+28>>2],void(p[n+36>>2]=w(i*t)+p[r+32>>2]);case 1:case 2:return r=v[n+4>>2],a=w(f+p[r+28>>2]),f=p[n+32>>2],p[n+32>>2]=w(w(a-f)*t)+f,f=p[n+36>>2],void(p[n+36>>2]=w(w(w(i+p[r+32>>2])-f)*t)+f);case 3:break n;default:break r}p[n+32>>2]=w(f*t)+p[n+32>>2],p[n+36>>2]=w(i*t)+p[n+36>>2]}},function(r){return v[36+(r|=0)>>2]+16777216|0},qi,function(r){qi(r|=0),Ct(r)},ja,function(r){return v[(r|=0)>>2]=11148,Lo(r+20|0),0|nu(r)},function(r){v[(r|=0)>>2]=11148,Lo(r+20|0),Ct(nu(r))},function(){return 21572},function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t=w(t),u|=0,o|=0;var a=0,c=w(0),b=w(0),k=w(0),s=w(0),l=w(0),h=w(0),d=0,y=0,m=0,g=0,F=w(0);r:if(n=v[v[n+36>>2]+(v[r+36>>2]<<2)>>2],e=v[n+8>>2],0|La[v[v[e>>2]+16>>2]](e)){if(d=n+36|0,y=n+16|0,e=sa(v[n+4>>2]),o=Ca(v[n+4>>2]),a=v[r+32>>2],p[a>>2]>i){n:switch(0|u){case 0:return p[n+20>>2]=p[e+4>>2],p[n+24>>2]=p[e+8>>2],p[n+28>>2]=p[e+12>>2],p[n+32>>2]=p[e+16>>2],mi(y),p[n+40>>2]=p[o+4>>2],p[n+44>>2]=p[o+8>>2],p[n+48>>2]=p[o+12>>2],p[n+52>>2]=p[o+16>>2],void mi(d);case 1:break n;default:break r}return f=p[n+20>>2],p[n+20>>2]=w(w(f-p[e+4>>2])*t)+f,f=p[n+24>>2],p[n+24>>2]=w(w(f-p[e+8>>2])*t)+f,f=p[n+28>>2],p[n+28>>2]=w(w(f-p[e+12>>2])*t)+f,f=p[n+32>>2],p[n+32>>2]=w(w(f-p[e+16>>2])*t)+f,f=p[n+40>>2],p[n+40>>2]=w(w(f-p[o+4>>2])*t)+f,f=p[n+44>>2],p[n+44>>2]=w(w(f-p[o+8>>2])*t)+f,f=p[n+48>>2],void(p[n+48>>2]=w(w(f-p[o+12>>2])*t)+f)}if(a=a+(v[r+24>>2]<<2)|0,p[a-32>>2]<=i?(i=p[a-4>>2],b=p[a-8>>2],k=p[a-12>>2],c=p[a-16>>2],s=p[a-20>>2],l=p[a-24>>2],f=p[a-28>>2]):(a=(g=(m=Lf(r+20|0,i,8))<<2)+v[r+32>>2]|0,h=p[a-28>>2],l=p[a-24>>2],s=p[a-20>>2],c=p[a-16>>2],k=p[a-12>>2],b=p[a-8>>2],F=p[a-4>>2],f=p[a>>2],f=pn(r,(m>>>3|0)-1|0,w(w(1)-w(w(i-f)/w(p[a-32>>2]-f)))),r=v[r+32>>2]+g|0,i=w(F+w(f*w(p[r+28>>2]-F))),b=w(b+w(f*w(p[r+24>>2]-b))),k=w(k+w(f*w(p[r+20>>2]-k))),c=w(c+w(f*w(p[r+16>>2]-c))),s=w(s+w(f*w(p[r+12>>2]-s))),l=w(l+w(f*w(p[r+8>>2]-l))),f=w(h+w(f*w(p[r+4>>2]-h)))),t==w(1))return p[n+32>>2]=c,p[n+28>>2]=s,p[n+24>>2]=l,p[n+20>>2]=f,mi(y),v[n+52>>2]=1065353216,p[n+48>>2]=i,p[n+44>>2]=b,p[n+40>>2]=k,void mi(d);u||(p[n+20>>2]=p[e+4>>2],p[n+24>>2]=p[e+8>>2],p[n+28>>2]=p[e+12>>2],p[n+32>>2]=p[e+16>>2],mi(y),p[n+40>>2]=p[o+4>>2],p[n+44>>2]=p[o+8>>2],p[n+48>>2]=p[o+12>>2],p[n+52>>2]=p[o+16>>2],mi(d)),h=p[n+32>>2],p[n+32>>2]=h+w(w(c-h)*t),c=p[n+28>>2],p[n+28>>2]=c+w(w(s-c)*t),c=p[n+24>>2],p[n+24>>2]=c+w(w(l-c)*t),c=p[n+20>>2],p[n+20>>2]=c+w(w(f-c)*t),mi(y),p[n+52>>2]=p[n+52>>2]+w(0),f=p[n+48>>2],p[n+48>>2]=f+w(w(i-f)*t),f=p[n+44>>2],p[n+44>>2]=f+w(w(b-f)*t),f=p[n+40>>2],p[n+40>>2]=f+w(w(k-f)*t),mi(d)}},function(r){return v[36+(r|=0)>>2]+234881024|0},ja,ma,Ua,function(){return 21584},ja,Ua,function(){return 21596},ja,ja,ja,Wa,function(){return 21624},Ia,function(r,n,f){r|=0,n|=0,f|=0;var i=w(0),e=w(0),t=w(0),u=0,o=w(0);e=p[r+8>>2],i=p[r+4>>2],t=w(-i),u=n,o=w(kf(t,i,w(w(i-i)*w(.5)))+p[n>>2]),p[u>>2]=o,u=f,o=w(kf(t,e,w(w(e-i)*w(.5)))+p[f>>2]),p[u>>2]=o},ja,Wa,function(){return 21636},function(r,n){n|=0,p[20+(r|=0)>>2]=p[n+172>>2]+p[r+4>>2],p[r+24>>2]=p[n+176>>2]+p[r+8>>2]},function(r,n,f){r|=0,n|=0,f|=0;var i=w(0),e=w(0),t=w(0),u=w(0),o=0;t=w(p[n>>2]-p[r+20>>2]),u=w(p[f>>2]-p[r+24>>2]),(i=w(T(w(w(t*t)+w(u*u)))))<(e=p[r+12>>2])&&(o=v[r+28>>2],e=Hr(i=w(La[v[v[o>>2]+8>>2]](o,w(0),p[r+16>>2],w(w(e-i)/e)))),i=Ur(i),p[n>>2]=p[r+20>>2]+w(w(i*t)-w(u*e)),p[f>>2]=w(w(e*t)+w(u*i))+p[r+24>>2])},ja,function(){return 21677},Iu,zo,function(){return 0|ut(11308)},cn,Ae,fa,je,function(r,n){return n|=0,v[12+(r|=0)>>2]+m(n,20)|0},function(r,n,f){n|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),r=0|La[0|i](n,f),n=Jt(20),v[n+16>>2]=v[r+16>>2],f=v[r+12>>2],v[n+8>>2]=v[r+8>>2],v[n+12>>2]=f,f=v[r+4>>2],v[n>>2]=v[r>>2],v[n+4>>2]=f,0|n},function(){return 21683},Iu,zo,function(){return 0|ut(8796)},An,function(r,n,f,i){var e,t;r|=0,n|=0,f|=0,i=w(i),V=e=V-16|0,n=((t=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,p[e+12>>2]=i,La[0|r](n,f,e+12|0),V=e+16|0},fa,je,Yt,function(r,n,f){n|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),w(p[La[0|i](n,f)>>2])},function(r,n,f){n|=0,f|=0,p[v[12+(r|=0)>>2]+(n<<2)>>2]=p[f>>2]},function(){return 21688},Iu,zo,function(){return 0|ut(9496)},function(r,n,f){r|=0,n|=0,f|=0;var i=0,e=0,t=w(0),u=0,o=0,a=0;i=v[r+4>>2],v[r+4>>2]=n,(e=v[r+8>>2])>>>0<n>>>0&&(t=w(w(n>>>0)*w(1.75)),u=w(g(t))<w(2147483648)?~~t:-2147483648,n=(n=e?u:n)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5316],o=r,a=0|La[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<4,8610,89),v[o+12>>2]=a,n=v[r+4>>2]);r:if(n>>>0<=i>>>0){if(n>>>0>=i>>>0)break r;for(;f=v[r+12>>2]+(n<<4)|0,La[v[v[f>>2]>>2]](f),(0|i)!=(0|(n=n+1|0)););}else for(;Xn(v[r+12>>2]+(i<<4)|0,f),(i=i+1|0)>>>0<d[r+4>>2];);},Ae,fa,je,Qt,function(r,n,f){n|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),r=0|La[0|i](n,f),0|Xn(ht(16),r)},function(r,n,f){n|=0,f|=0,(0|(r=v[12+(r|=0)>>2]+(n<<4)|0))!=(0|f)&&(v[r+4>>2]=0,te(r,f))},function(){return 21691},Iu,zo,function(){return 0|ut(8764)},Tn,$i,fa,je,Yt,ge,It,function(){return 21694},Iu,zo,function(){return 0|ut(11644)},Tn,$i,fa,je,Yt,ge,It,function(){return 21698},Iu,zo,function(){return 0|ut(9540)},function(r,n,f){r|=0,n|=0,f|=0;var i=0,e=0,t=w(0),u=0,o=0,a=0;i=v[r+4>>2],v[r+4>>2]=n,(e=v[r+8>>2])>>>0<n>>>0&&(t=w(w(n>>>0)*w(1.75)),u=w(g(t))<w(2147483648)?~~t:-2147483648,n=(n=e?u:n)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5316],o=r,a=0|La[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<4,8610,89),v[o+12>>2]=a,n=v[r+4>>2]);r:if(n>>>0<=i>>>0){if(n>>>0>=i>>>0)break r;for(;f=v[r+12>>2]+(n<<4)|0,La[v[v[f>>2]>>2]](f),(0|i)!=(0|(n=n+1|0)););}else for(;Qn(v[r+12>>2]+(i<<4)|0,f),(i=i+1|0)>>>0<d[r+4>>2];);},Ae,fa,je,Qt,function(r,n,f){n|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),r=0|La[0|i](n,f),0|Qn(ht(16),r)},function(r,n,f){n|=0,f|=0,(0|(r=v[12+(r|=0)>>2]+(n<<4)|0))!=(0|f)&&(v[r+4>>2]=0,ue(r,f))},function(){return 21701},Iu,zo,function(){return 0|ut(10816)},function(r,n,f){r|=0,n|=0,f|=0;var i=0,e=0,t=w(0),u=0,o=0,a=0;if(i=v[r+4>>2],v[r+4>>2]=n,(e=v[r+8>>2])>>>0<n>>>0&&(t=w(w(n>>>0)*w(1.75)),u=w(g(t))<w(2147483648)?~~t:-2147483648,n=(n=e?u:n)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5316],o=r,a=0|La[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<2,8610,89),v[o+12>>2]=a,n=v[r+4>>2]),n>>>0>i>>>0)for(n=v[r+12>>2];v[n+(i<<2)>>2]=v[f>>2],(i=i+1|0)>>>0<d[r+4>>2];);},$i,fa,je,Yt,ge,It,function(){return 21704},Iu,zo,function(){return 0|ut(9884)},$n,function(r,n,f,i){var e,t;n|=0,f|=0,i|=0,V=e=V-16|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,s[e+14>>1]=i,La[0|r](n,f,e+14|0),V=e+16|0},fa,je,function(r,n){return n|=0,v[12+(r|=0)>>2]+(n<<1)|0},function(r,n,f){n|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),h[La[0|i](n,f)>>1]},function(r,n,f){n|=0,f|=0,s[v[12+(r|=0)>>2]+(n<<1)>>1]=h[f>>1]},function(){return 21708},Iu,zo,function(){return 0|ut(9244)},un,fi,fa,je,function(r,n){return n|=0,v[12+(r|=0)>>2]+m(n,12)|0},function(r,n,f){n|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),f=0|La[0|i](n,f),n=X((r=v[f+4>>2])+4|0),v[n>>2]=r,Me(n+4|0,v[f+8>>2],r),0|n},function(r,n,f){n|=0,f|=0,sf(v[12+(r|=0)>>2]+m(n,12)|0,f)},function(){return 21711},Iu,zo,function(){return 0|ut(9292)},In,$i,fa,je,Yt,ge,function(){return 21714},Iu,zo,function(){return 0|ut(9708)},In,$i,fa,je,Yt,ge,function(){return 21718},Iu,zo,function(){return 0|ut(10584)},In,$i,fa,je,Yt,ge,function(){return 21722},Iu,zo,function(){return 0|ut(10664)},In,$i,fa,je,Yt,ge,function(){return 21725},Iu,zo,function(){return 0|ut(10680)},In,$i,fa,je,Yt,ge,function(){return 21728},Iu,zo,function(){return 0|ut(10896)},In,$i,fa,je,Yt,ge,function(){return 21731},Iu,zo,function(){return 0|ut(10264)},In,$i,fa,je,Yt,ge,function(){return 21735},Iu,zo,function(){return 0|ut(10600)},In,$i,fa,je,Yt,ge,function(){return 21739},Iu,zo,function(){return 0|ut(10616)},In,$i,fa,je,Yt,ge,function(){return 21743},Iu,zo,function(){return 0|ut(8844)},In,$i,fa,je,Yt,ge,function(){return 21747},Iu,zo,function(){return 0|ut(10632)},In,$i,fa,je,Yt,ge,function(){return 21751},Iu,zo,function(){return 0|ut(10280)},In,$i,fa,je,Yt,ge,function(){return 21754},Iu,zo,function(){return 0|ut(10648)},In,$i,fa,je,Yt,ge,function(){return 21757},Iu,zo,function(){return 0|ut(10296)},In,$i,fa,je,Yt,ge,function(){return 21760},Iu,zo,function(){return 0|ut(10312)},In,$i,fa,je,Yt,ge,function(){return 21763},Iu,zo,function(){return 0|ut(1048)},In,$i,fa,je,Yt,ge,It,function(){return 21766},Iu,zo,function(){return 0|ut(8780)},In,$i,fa,je,Yt,ge,function(){return 21770},Iu,zo,function(){return 0|ut(10328)},In,$i,fa,je,Yt,ge,function(){return 21773},Iu,zo,function(){return 0|ut(12960)},In,$i,fa,je,Yt,ge,function(){return 21777},Iu,zo,function(){return 0|ut(13024)},function(r,n,f){r|=0,n|=0,f|=0;var i=0,e=0,t=w(0),u=0,o=0,a=0;i=v[r+4>>2],v[r+4>>2]=n,(e=v[r+8>>2])>>>0<n>>>0&&(t=w(w(n>>>0)*w(1.75)),u=w(g(t))<w(2147483648)?~~t:-2147483648,n=(n=e?u:n)>>>0<=8?8:n,v[r+8>>2]=n,e=v[5316],o=r,a=0|La[v[v[e>>2]+16>>2]](e,v[r+12>>2],n<<4,8610,89),v[o+12>>2]=a,n=v[r+4>>2]);r:if(n>>>0<=i>>>0){if(n>>>0>=i>>>0)break r;for(;f=v[r+12>>2]+(n<<4)|0,La[v[v[f>>2]>>2]](f),(0|i)!=(0|(n=n+1|0)););}else for(;Bn(v[r+12>>2]+(i<<4)|0,f),(i=i+1|0)>>>0<d[r+4>>2];);},Ae,fa,je,Qt,function(r,n,f){n|=0,f|=0;var i=0;return i=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(i=v[i+v[n>>2]>>2]),r=0|La[0|i](n,f),0|Bn(ht(16),r)},function(){return 21780},va,zo,function(){var r;return r=Jt(8),v[r>>2]=0,v[r+4>>2]=0,0|r},ye,function(r,n){return r|=0,n|=0,0|At(Jt(8),p[r>>2],p[n>>2])},tu,Zt,At,function(r,n,f,i){r|=0,n|=0,f=w(f),i=w(i);var e=0;return e=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),r=0|La[0|e](n,f,i),n=Jt(8),e=v[r+4>>2],v[n>>2]=v[r>>2],v[n+4>>2]=e,0|n},function(r){r|=0;var n=w(0),f=w(0);return n=p[r>>2],f=w(n*n),n=p[r+4>>2],w(w(T(w(f+w(n*n)))))},Ie,function(r){r|=0;var n=w(0),f=w(0),i=w(0);return n=p[r>>2],f=p[r+4>>2],i=w(w(1)/w(T(w(w(n*n)+w(f*f))))),p[r+4>>2]=f*i,p[r>>2]=n*i,0|r},function(r,n){n|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),r=0|La[0|f](n),n=Jt(8),f=v[r+4>>2],v[n>>2]=v[r>>2],v[n+4>>2]=f,0|n},function(){return 21783},Iu,zo,function(){var r;return r=ht(20),v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=9404,v[r+12>>2]=0,v[r+16>>2]=0,0|r},function(r,n,f,i,e){var t;return r|=0,n=w(n),f=w(f),i=w(i),e=w(e),V=t=V-16|0,p[t+12>>2]=n,p[t+8>>2]=f,p[t+4>>2]=i,p[t>>2]=e,r=0|La[0|r](t+12|0,t+8|0,t+4|0,t),V=t+16|0,0|r},function(r,n,f,i){r|=0,n|=0,f|=0,i|=0;var e,t=w(0),u=w(0),o=w(0);return e=ht(20),t=p[r>>2],u=p[n>>2],o=p[f>>2],p[e+16>>2]=p[i>>2],p[e+12>>2]=o,p[e+8>>2]=u,p[e+4>>2]=t,v[e>>2]=9404,mi(e),0|e},function(r,n,f,i,e){return r|=0,n=w(n),f=w(f),i=w(i),e=w(e),p[r+16>>2]=e,p[r+12>>2]=i,p[r+8>>2]=f,p[r+4>>2]=n,mi(r),0|r},function(r,n,f,i,e,t){r|=0,n|=0,f=w(f),i=w(i),e=w(e),t=w(t);var u=0;return u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),n=0|La[0|u](n,f,i,e,t),r=ht(20),v[r>>2]=9404,u=v[n+8>>2],v[r+4>>2]=v[n+4>>2],v[r+8>>2]=u,u=v[n+16>>2],v[r+12>>2]=v[n+12>>2],v[r+16>>2]=u,0|r},function(r,n,f,i,e){return r|=0,n=w(n),f=w(f),i=w(i),e=w(e),p[r+4>>2]=p[r+4>>2]+n,p[r+8>>2]=p[r+8>>2]+f,p[r+12>>2]=p[r+12>>2]+i,p[r+16>>2]=p[r+16>>2]+e,mi(r),0|r},mi,function(r,n){n|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),n=0|La[0|f](n),r=ht(20),v[r>>2]=9404,f=v[n+8>>2],v[r+4>>2]=v[n+4>>2],v[r+8>>2]=f,f=v[n+16>>2],v[r+12>>2]=v[n+12>>2],v[r+16>>2]=f,0|r},tu,Zt,Lt,bu,we,function(){return 21786},Iu,zo,function(){var r;return r=Jt(12),v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=8748,0|r},Wt,Iu,Ee,function(r){return r|=0,0|pt(ht(24),r)},ha,di,du,uu,gu,ou,Wt,ma,ma,Iu,Ee,function(r){return r|=0,0|ui(ht(60),r)},sa,du,uu,du,uu,gu,ou,tu,Zt,Wt,ma,ma,Iu,Ee,function(r){return r|=0,0|lt(ht(76),r)},sa,du,uu,du,uu,du,uu,du,uu,tu,Zt,function(){return 21792},Iu,zo,function(){var r;return r=ht(72),v[r+44>>2]=0,v[r+48>>2]=0,v[r+40>>2]=10440,v[r+36>>2]=0,v[r+28>>2]=0,v[r+32>>2]=0,v[r+24>>2]=10424,v[r+20>>2]=0,v[r+12>>2]=0,v[r+16>>2]=0,v[r+8>>2]=10440,v[r+4>>2]=10408,v[r>>2]=10392,v[r+52>>2]=0,v[r+56>>2]=0,v[r+60>>2]=0,v[r+64>>2]=0,v[r+68>>2]=0,0|r},function(r,n,f){r|=0,n|=0,f|=0;var i,e=0,t=0,u=w(0),o=0,a=0,c=0,b=0,k=w(0),s=w(0),l=w(0),h=w(0),y=0,m=0,g=0,F=0;if(V=i=V-16|0,b=v[n+28>>2],v[r+28>>2]=0,e=v[r+44>>2])for(o=r+4|0;Tf(o,v[v[r+52>>2]+(t<<2)>>2]),(0|e)!=(0|(t=t+1|0)););if(v[r+44>>2]=0,b)for(g=r+40|0,F=r+24|0,o=r+4|0,t=0;a=v[v[n+36>>2]+(t<<2)>>2],e=v[a+8>>2],0|La[v[v[e>>2]+16>>2]](e)&&(e=v[a+60>>2])&&xe(0|La[v[v[e>>2]+8>>2]](e),21156)&&(v[i+12>>2]=e,_n(F,i+12|0),(e=v[o+8>>2])?(c=e-1|0,e=v[v[o+16>>2]+(c<<2)>>2],v[o+8>>2]=c):(e=ht(24),v[e+4>>2]=8796,v[e>>2]=10456,v[e+8>>2]=0,v[e+12>>2]=0,v[e+16>>2]=0,v[e+20>>2]=0,Oa(e+4|0,16)),v[i+8>>2]=e,_n(g,i+8|0),c=v[i+8>>2],e=v[i+12>>2],y=v[e+52>>2],v[c+20>>2]=y,m=c+4|0,d[c+8>>2]<y>>>0&&(v[i+4>>2]=0,An(m,y,i+4|0),e=v[i+12>>2]),lr(e,a,0,v[e+52>>2],v[m+12>>2],0,2)),(0|b)!=(0|(t=t+1|0)););if(f){if(n=0,t=v[r+44>>2])for(o=v[r+52>>2],l=w(34028234663852886e22),h=w(11754943508222875e-54),k=w(11754943508222875e-54),s=w(34028234663852886e22);;){if(f=v[o+(n<<2)>>2],(0|(b=v[f+20>>2]))>0)for(e=v[f+16>>2],f=0;l=(u=p[(a=f<<2)+e>>2])<l?l:u,h=u>h?h:u,s=(u=p[e+(4|a)>>2])<s?s:u,k=u>k?k:u,(0|b)>(0|(f=f+2|0)););if((0|t)==(0|(n=n+1|0)))break}else k=w(11754943508222875e-54),s=w(34028234663852886e22),l=w(34028234663852886e22),h=w(11754943508222875e-54);p[r+68>>2]=s,p[r+64>>2]=l,p[r+60>>2]=k,p[r+56>>2]=h}else v[r+64>>2]=2139095039,v[r+68>>2]=2139095039,v[r+56>>2]=8388608,v[r+60>>2]=8388608;V=i+16|0},Ae,function(r,n,f){r|=0,n=w(n),f=w(f);var i=0;return!(p[r+60>>2]<=f)|!(p[r+56>>2]<=n)|!(p[r+64>>2]>=n)||(i=p[r+68>>2]>=f),0|i},function(r,n,f,i){r|=0,n|=0,f=w(f),i=w(i);var e=0;return e=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),0|La[0|e](n,f,i)},function(r,n,f,i,e){r|=0,n=w(n),f=w(f),i=w(i),e=w(e);var t=w(0),u=w(0),o=w(0),a=w(0),c=0;return(t=p[r+56>>2])>=n&i<=t|(u=p[r+60>>2])>=f&e<=u|(o=p[r+64>>2])<=n&i>=o|(a=p[r+68>>2])<=f&e>=a||(c=1,i=w(w(e-f)/w(i-n)),(e=w(w(i*w(t-n))+f))>u&e<a||(e=w(w(i*w(o-n))+f))>u&e<a||(e=w(w(w(u-f)/i)+n))>t&e<o||(c=(n=w(w(w(a-f)/i)+n))>t&n<o)),0|c},function(r,n,f,i,e,t){r|=0,n|=0,f=w(f),i=w(i),e=w(e),t=w(t);var u=0;return u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),0|La[0|u](n,f,i,e,t)},function(r,n){n|=0;var f=0;return!(p[60+(r|=0)>>2]<p[n+68>>2])|!(p[r+56>>2]<p[n+64>>2])|!(p[r+64>>2]>p[n+56>>2])||(f=p[r+68>>2]>p[n+60>>2]),0|f},function(r,n,f){f|=0;var i,e,t=0,u=0,o=0,a=0;if(V=i=V-80|0,e=(t=n|=0)+((n=v[4+(r|=0)>>2])>>1)|0,r=v[r>>2],r=1&n?v[v[e>>2]+r>>2]:r,v[i+12>>2]=10408,v[i+8>>2]=10392,Dn(i+16|0,f+8|0),v[i+32>>2]=10424,v[i+36>>2]=v[f+28>>2],n=v[f+32>>2],v[i+44>>2]=0,v[i+40>>2]=n,n&&(t=v[5316],o=i,a=0|La[v[v[t>>2]+12>>2]](t,n<<2,8610,221),v[o+44>>2]=a,t=v[i+36>>2]))for(n=0;v[(u=n<<2)+v[i+44>>2]>>2]=v[v[f+36>>2]+u>>2],(0|t)!=(0|(n=n+1|0)););return Dn(i+48|0,f+40|0),t=v[4+(n=f- -64|0)>>2],v[i+72>>2]=v[n>>2],v[i+76>>2]=t,n=v[f+60>>2],v[i+64>>2]=v[f+56>>2],v[i+68>>2]=n,n=i+8|0,r=0|La[0|r](e,n),Jf(n),V=i+80|0,0|r},function(r,n,f){r|=0,n=w(n),f=w(f);var i,e=w(0),t=0,u=w(0),o=0,a=0,c=0,b=0,k=0,s=0,l=0;r:{if(i=v[r+44>>2])for(s=r,l=v[r+52>>2];;){if(a=0,r=v[(o<<2)+l>>2],(0|(c=v[r+20>>2]))>0)for(t=c-2|0,b=v[r+16>>2],r=0;!((e=p[(4|(k=r<<2))+b>>2])<f&(u=p[4+(t=(t<<2)+b|0)>>2])>=f)&(!(f<=e)|!(f>u))||(u=w(w(f-e)/w(u-e)),e=p[b+k>>2],w(w(u*w(p[t>>2]-e))+e)<n&&(a^=1)),t=r,(0|c)>(0|(r=r+2|0)););if(r=v[v[s+36>>2]+(o<<2)>>2],1&a)break r;if((0|i)==(0|(o=o+1|0)))break}r=0}return 0|r},function(r,n,f,i,e){r|=0,n=w(n),f=w(f),i=w(i),e=w(e);var t,u=0,o=w(0),a=w(0),c=0,b=w(0),k=w(0),s=0,l=w(0),h=0,d=0,y=w(0),m=w(0),g=w(0),F=w(0),A=w(0),T=w(0),$=0,I=w(0);b=n,k=f;r:{if(t=v[r+44>>2])for($=v[r+52>>2];;){u=v[(s<<2)+$>>2];n:{if(h=v[u+20>>2])for(y=w(w(b*e)-w(k*i)),m=w(-w(k-e)),d=v[u+16>>2],n=p[(u=d+(h<<2)|0)-8>>2],f=p[u-4>>2],g=w(b-i),I=w(-g),u=0;;){if(l=f,o=n,n=p[(c=u<<2)+d>>2],a=w(o-n),f=p[(4|c)+d>>2],F=w(w(o*f)-w(n*l)),A=w(l-f),T=w(w(g*A)+w(a*m)),!(!((a=w(w(w(y*a)+w(F*I))/T))>=o&n>=a)&(!(n<=a)|!(o>=a))|!(a>=b&i>=a)&(!(i<=a)|!(a<=b))||!((o=w(w(w(y*A)+w(F*m))/T))>=l&f>=o)&(!(f<=o)|!(o<=l)))){if(c=1,o>=k&e>=o)break n;if(e<=o&&o<=k)break n}if(!(h>>>0>(u=u+2|0)>>>0))break}c=0}if(u=v[v[r+36>>2]+(s<<2)>>2],c)break r;if((0|t)==(0|(s=s+1|0)))break}u=0}return 0|u},function(r){return w(w(p[64+(r|=0)>>2]-p[r+56>>2]))},Ie,function(r){return w(w(p[68+(r|=0)>>2]-p[r+60>>2]))},function(){return 21796},Iu,Le,function(r,n){return r|=0,n|=0,0|le(ht(40),p[r>>2],n)},fa,cu,du,uu,tu,Zt,Ue,Pe,function(){return 21798},Iu,Ee,function(r){return r|=0,0|me(ht(56),r)},ha,di,du,uu,tu,Zt,Ue,Pe,Wt,Iu,ha,di,Wt,ma,ma,Iu,Yo,je,ta,ka,du,uu,du,uu,Xe,function(r,n,f,i,e,t,u,o){n|=0,f|=0,i|=0,e|=0,t|=0,u|=0,o|=0;var a=0;a=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(a=v[a+v[n>>2]>>2]),La[0|a](n,f,i,e,t,u,o)},de,Ge,Wt,ma,ma,Iu,Ee,function(r){return r|=0,0|Ru(ht(64),r)},di,je,Wt,ma,ma,Iu,Ee,function(r){return r|=0,0|Gt(ht(68),r)},function(r){return v[64+(r|=0)>>2]},Qu,je,Ge,je,Wt,ma,ma,Iu,Ee,function(r){return r|=0,0|Jr(ht(236),r)},Ue,Pe,function(r){return 120+(r|=0)|0},ia,function(r){return 136+(r|=0)|0},function(r){return 0|Fa(r|=0)},cu,tu,Zt,du,uu,function(r){return 152+(r|=0)|0},hr,Be,function(r){return v[100+(r|=0)>>2]},je,Zn,Ge,je,Nr,Wt,ma,ma,Iu,Ee,function(r){return r|=0,0|oe(ht(84),r)},function(r){return(r|=0)- -64|0},gu,ou,je,Wt,ma,ma,Iu,Ee,function(r){return r|=0,0|tt(ht(32),r)},tu,Zt,function(r,n,f,i){var e;r|=0,n|=0,f=w(f),i=w(i),V=e=V-16|0,p[e+12>>2]=f,p[e+8>>2]=i,he(n,p[r+20>>2],p[r+24>>2],e+12|0,e+8|0),V=e+16|0},function(r,n){r|=0,n|=0;var f=w(0),i=w(0),e=w(0),t=w(0);return e=p[n+96>>2],i=Hr(f=w(p[r+28>>2]*w(.01745329238474369))),t=p[n+92>>2],f=Ur(f),w(w(Rr(w(w(f*p[n+104>>2])+w(i*p[n+108>>2])),w(w(f*t)+w(i*e)))*w(57.2957763671875)))},Fe,je,Wt,ma,ma,Iu,Ee,function(r){return r|=0,0|xr(ht(164),r)},tu,Zt,function(r){return 0|$a(r|=0)},cu,Ue,Pe,aa,uf,function(r,n,f,i,e,t,u){r|=0,n|=0,f=w(f),i=w(i),e=w(e),t=w(t),u|=0;var o=0;o=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(o=v[o+v[n>>2]>>2]),La[0|o](n,f,i,e,t,u)},ea,Br,Be,function(r,n,f,i,e){i|=0,e|=0,rn(r|=0,n|=0,v[12+(f|=0)>>2],i,e)},function(r,n,f,i,e,t){n|=0,f|=0,i|=0,e|=0,t|=0;var u=0;u=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),La[0|u](n,f,i,e,t)},je,Wt,Iu,Df,Df,Df,Df,mf,mf,Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|St(ht(8),v[r>>2])},mf,mf,Df,Df,Df,Df,function(){return 21803},Iu,Ee,function(r){return r|=0,0|ii(ht(68),r)},function(r,n){zf(r|=0,16+(n|=0)|0)},du,uu,du,uu,du,uu,function(){return 21806},Iu,function(r,n){zf(r|=0,8+(n|=0)|0)},du,uu,gu,ou,function(){return 21809},Iu,function(){return 21812},Iu,function(r,n,f,i){var t;return r|=0,f|=0,i|=0,V=t=V-32|0,n=Sf(t+20|0,4+(n|=0)|0,v[n>>2],0),v[t+16>>2]=f,e[t+15|0]=i,r=0|La[0|r](n,t+16|0,t+15|0),Oe(n),V=t+32|0,0|r},function(r,n,f){var i,t,u,o,a;return r|=0,n|=0,f|=0,i=ht(40),a=l[0|f],V=t=V-16|0,v[i+36>>2]=v[n>>2],v[i+32>>2]=0,v[i+24>>2]=0,v[i+28>>2]=0,v[i+20>>2]=9080,v[i+16>>2]=0,v[i+8>>2]=0,v[i+12>>2]=0,v[i+4>>2]=9064,v[i>>2]=8924,u=t+4|0,f=ve(n=v[r+8>>2],47),n=se(f=$t(u,o=(n=ve(n,92))>>>0<f>>>0?f:n,0),r),Oe(f),f=v[5316],n=(n=n+o|0)?n-v[r+8>>2]|0:0,f=Me(0|La[v[v[f>>2]+12>>2]](f,n+1|0,8610,54),v[r+8>>2],n),e[n+f|0]=0,n=v[5316],(n=0|La[v[v[n>>2]+24>>2]](n,r,u))&&tr(i,n,v[t+4>>2],f,a),r=v[5316],La[v[v[r>>2]+20>>2]](r,n,8610,63),r=v[5316],La[v[v[r>>2]+20>>2]](r,f,8610,64),V=t+16|0,0|i},ai,Xf,Lt,ma,ma,bu,Ne,function(r){var n;return r|=0,n=Jt(8),v[n+4>>2]=v[r>>2],v[n>>2]=9788,0|n},Lt,ma,ma,bu,Ne,function(r){var n;return r|=0,n=Jt(8),v[n+4>>2]=v[r>>2],v[n>>2]=9816,0|n},function(){return 21791},Iu,function(r,n,f,i){var e;return r|=0,n|=0,f|=0,i|=0,V=e=V-16|0,v[e+12>>2]=n,n=Sf(e,f+4|0,v[f>>2],0),r=0|La[0|r](e+12|0,n,i),Oe(n),V=e+16|0,0|r},function(r,n,f){return r|=0,n|=0,f|=0,0|nf(ht(84),v[r>>2],n,f)},fa,je,la,di,qo,cu,function(r){return 0|sa(r|=0)},cu,function(r){return 0|Ca(r|=0)},du,uu,Wt,Iu,Be,je,je,Ge,Wt,ma,ma,Iu,xt,function(r,n){return r|=0,n|=0,0|kn(ht(48),r,n)},fa,cu,la,du,uu,du,uu,gu,ou,tu,Zt,function(r,n,f,i,e,t,u,o){r|=0,n|=0,f=w(f),i=w(i),e|=0,t|=0,u|=0,o=w(o),La[0|r](n,f,i,e,t,u,o)},Ar,function(r,n,f,i,e,t,u,o,a){r|=0,n|=0,f|=0,i=w(i),e=w(e),t|=0,u|=0,o=w(o),a=w(a),La[0|r](n,f,i,e,t,u,o,a)},cr,Wt,ma,ma,Iu,xt,function(r,n){return r|=0,n|=0,0|Ir(ht(144),r,n)},fa,cu,la,du,uu,tu,Zt,Wt,ma,ma,Iu,Ee,function(r){return r|=0,0|vt(ht(88),r)},sa,function(r){return v[40+(r|=0)>>2]},je,function(r){return w(p[44+(r|=0)>>2])},Ie,function(r){return w(p[48+(r|=0)>>2])},function(r){return w(p[52+(r|=0)>>2])},function(r){return w(p[56+(r|=0)>>2])},Ju,function(r){return w(p[64+(r|=0)>>2])},function(r){return w(p[68+(r|=0)>>2])},function(r){return w(p[72+(r|=0)>>2])},function(r){return w(p[76+(r|=0)>>2])},function(r){return w(p[80+(r|=0)>>2])},function(r){return l[84+(r|=0)|0]},je,function(r){return l[85+(r|=0)|0]},Wt,ma,ma,Iu,xt,function(r,n){return r|=0,n|=0,0|sn(ht(48),r,n)},fa,cu,la,Jo,je,tu,Zt,Wt,ma,ma,Iu,function(r,n,f,i){var e;return r|=0,n|=0,f|=0,i|=0,V=e=V-16|0,v[e+12>>2]=i,r=0|La[0|r](n,f,e+12|0),V=e+16|0,0|r},function(r,n,f){return r|=0,n|=0,f|=0,0|If(ht(120),r,n,v[f>>2])},fa,cu,na,cu,Ko,je,oa,tu,Zt,gu,ou,De,Be,kr,function(r,n,f,i,e,t,u,o,a){r|=0,n|=0,f=w(f),i=w(i),e=w(e),t=w(t),u=w(u),o=w(o),a=w(a);var c=0;c=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(c=v[c+v[n>>2]>>2]),La[0|c](n,f,i,e,t,u,o,a)},ni,function(r){return w(w(Rr(p[104+(r|=0)>>2],p[r+92>>2])*w(57.2957763671875)))},Ie,function(r){return w(w(Rr(p[108+(r|=0)>>2],p[r+96>>2])*w(57.2957763671875)))},function(r){r|=0;var n=w(0),f=w(0);return n=p[r+92>>2],f=w(n*n),n=p[r+104>>2],w(w(T(w(f+w(n*n)))))},function(r){r|=0;var n=w(0),f=w(0);return n=p[r+96>>2],f=w(n*n),n=p[r+108>>2],w(w(T(w(f+w(n*n)))))},function(r,n){r|=0,n|=0;var f,i=w(0),e=w(0),t=w(0),u=w(0),o=w(0),a=w(0),c=w(0);V=f=V-16|0,u=p[r+92>>2],i=p[r+108>>2],o=p[r+104>>2],e=p[r+96>>2],t=w(w(1)/w(w(u*i)-w(o*e))),a=i,i=w(p[n>>2]-p[r+100>>2]),c=e,e=w(p[n+4>>2]-p[r+112>>2]),p[f+12>>2]=w(w(a*i)*t)-w(t*w(c*e)),p[f+8>>2]=w(w(u*e)*t)-w(t*w(o*i)),p[n>>2]=p[f+12>>2],p[n+4>>2]=p[f+8>>2],V=f+16|0},function(r,n){var f;V=f=V-16|0,he(r|=0,p[(n|=0)>>2],p[n+4>>2],f+12|0,f+8|0),p[n>>2]=p[f+12>>2],p[n+4>>2]=p[f+8>>2],V=f+16|0},function(r,n){r|=0,n=w(n);var f=w(0),i=w(0),e=w(0);return i=p[r+96>>2],n=Hr(f=w(n*w(.01745329238474369))),e=p[r+108>>2],f=Ur(f),w(w(w(w(Rr(w(w(n*p[r+92>>2])-w(f*p[r+104>>2])),w(w(e*f)-w(n*i)))*w(57.2957763671875))+p[r+40>>2])-p[r+52>>2]))},we,function(r,n){r|=0,n=w(n);var f=w(0),i=w(0),e=w(0);return i=p[r+96>>2],n=Hr(f=w(w(n-w(p[r+40>>2]-p[r+52>>2]))*w(.01745329238474369))),e=p[r+92>>2],f=Ur(f),w(w(Rr(w(w(f*p[r+104>>2])+w(n*p[r+108>>2])),w(w(f*e)+w(n*i)))*w(57.2957763671875)))},function(r,n){r|=0,n=w(n);var f=w(0),i=w(0),t=w(0),u=w(0),o=w(0);e[r+88|0]=0,i=p[r+108>>2],n=Ur(f=w(n*w(.01745329238474369))),t=p[r+96>>2],f=Hr(f),p[r+108>>2]=w(t*f)+w(n*i),u=p[r+92>>2],o=p[r+104>>2],p[r+104>>2]=w(f*u)+w(n*o),p[r+96>>2]=w(n*t)-w(i*f),p[r+92>>2]=w(n*u)-w(o*f)},Se,function(){return 21790},Iu,yi,function(r,n,f){return r|=0,n|=0,f|=0,0|af(ht(64),v[r>>2],n,v[f>>2])},fa,je,la,di,du,uu,tu,Zt,du,uu,gu,ou,function(){return 21800},Iu,xt,function(r,n){return r|=0,n|=0,0|En(ht(88),r,n)},fa,cu,na,cu,oa,cu,ka,ca,Ko,Yo,je,$e,Ge,function(r,n){r|=0,n=w(n),p[r+68>>2]=p[v[r+12>>2]+160>>2]-n},Se,function(r){return w(w(p[v[12+(r|=0)>>2]+160>>2]-p[r+68>>2]))},Ie,hn,Be,function(){return 21801},Iu,Ee,function(r){return r|=0,0|Of(ht(68),r)},ha,di,ka,function(r){return 52+(r|=0)|0},Ht,function(r,n,f,i,e){n|=0,f|=0,i|=0,e|=0;var t,u=0;V=t=V-16|0,n=((u=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=Sf(t+4|0,i+4|0,v[i>>2],0),La[0|r](u,f,n,e),Oe(n),V=t+16|0},function(r,n){r|=0;var f=0,i=0,e=0,t=0,u=0,o=0,a=0,c=0;if(i=v[40+(n|=0)>>2])for(c=r+36|0;;){t=v[n+48>>2]+(e<<2)|0;r:{if(u=v[r+40>>2]){if(f=0,o=v[t>>2],a=v[r+48>>2],(0|o)==v[a>>2])break r;for(;(0|u)!=(0|(f=f+1|0))&(0|o)!=v[a+(f<<2)>>2];);if(f>>>0<u>>>0)break r}_n(c,t),i=v[n+40>>2]}if(!((e=e+1|0)>>>0<i>>>0))break}if(i=v[n+56>>2])for(c=r+52|0,e=0;;){t=v[n+64>>2]+(e<<2)|0;r:{if(u=v[r+56>>2]){if(f=0,o=v[t>>2],a=v[r+64>>2],(0|o)==v[a>>2])break r;for(;(0|u)!=(0|(f=f+1|0))&(0|o)!=v[a+(f<<2)>>2];);if(f>>>0<u>>>0)break r}_n(c,t),i=v[n+56>>2]}if(!((e=e+1|0)>>>0<i>>>0))break}r:if(i=v[n+24>>2])for(u=r+16|0,r=0,f=0;;){if(t=v[n+32>>2],d[4+(t+(f<<4)|0)>>2]<=r>>>0){n:{f:{for(;;){if((0|i)==(0|(f=f+1|0)))break f;if(v[4+(t+(f<<4)|0)>>2])break}r=f;break n}r=i}if(e=r,i=f>>>0>=i>>>0,r=0,f=e,i)break r}if(e=v[12+(t+(f<<4)|0)>>2]+m(r,20)|0,vr(u,v[e>>2],e+4|0,v[e+16>>2]),r=r+1|0,!((i=v[n+24>>2])>>>0>f>>>0))break}},Ge,function(r,n){r|=0;var f=0,i=0,e=0,t=0,u=0,o=0,a=0,c=0;if(e=v[40+(n|=0)>>2])for(c=r+36|0;;){u=v[n+48>>2]+(f<<2)|0;r:{if(t=v[r+40>>2]){if(i=0,o=v[u>>2],a=v[r+48>>2],(0|o)==v[a>>2])break r;for(;(0|t)!=(0|(i=i+1|0))&(0|o)!=v[a+(i<<2)>>2];);if(i>>>0<t>>>0)break r}_n(c,u),e=v[n+40>>2]}if(!(e>>>0>(f=f+1|0)>>>0))break}if(e=v[n+56>>2])for(c=r+52|0,f=0;;){u=v[n+64>>2]+(f<<2)|0;r:{if(t=v[r+56>>2]){if(i=0,o=v[u>>2],a=v[r+64>>2],(0|o)==v[a>>2])break r;for(;(0|t)!=(0|(i=i+1|0))&(0|o)!=v[a+(i<<2)>>2];);if(i>>>0<t>>>0)break r}_n(c,u),e=v[n+56>>2]}if(!(e>>>0>(f=f+1|0)>>>0))break}r:if(f=v[n+24>>2])for(u=r+16|0,e=0,i=0;;){if(t=v[n+32>>2],d[4+(t+(i<<4)|0)>>2]<=e>>>0){n:{f:{for(;;){if((0|(i=i+1|0))==(0|f))break f;if(v[4+(t+(i<<4)|0)>>2])break}r=i;break n}r=f}if(f=f>>>0<=i>>>0,e=0,i=r,f)break r}if(r=v[12+(t+(i<<4)|0)>>2]+m(e,20)|0,f=v[r+16>>2],t=Su(0|La[v[v[f>>2]+8>>2]](f),21344),f=v[r+16>>2],e=e+1|0,vr(u,o=v[r>>2],a=r+4|0,r=t?Nr(f):0|La[v[v[f>>2]+12>>2]](f)),!(i>>>0<(f=v[n+24>>2])>>>0))break}},function(r,n,f){n|=0,f|=0;var i,e=0,t=0,u=0,o=0;V=i=V-16|0,v[12+(r|=0)>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=9244,au(i+4|0,n),n=v[i+8>>2],t=v[i+4>>2];r:if(!(n>>>0>=(e=v[t+4>>2])>>>0))for(;;){if(u=v[i+12>>2],o=v[t+12>>2],u>>>0>=d[4+(o+(n<<4)|0)>>2]){for(;;){if((0|e)==(0|(n=n+1|0)))break r;if(v[4+((n<<4)+o|0)>>2])break}if(v[i+8>>2]=n,u=0,n>>>0>=e>>>0)break r}if(e=v[12+((n<<4)+o|0)>>2],v[i+12>>2]=u+1,e=e+m(u,20)|0,v[e>>2]==(0|f)&&(On(r,e+4|0),t=v[i+4>>2],n=v[i+8>>2]),!((e=v[t+4>>2])>>>0>n>>>0))break}V=i+16|0},Mt,Df,function(r,n){n|=0;var f,i=0,e=0,t=0;V=f=V-16|0,v[12+(r|=0)>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=12960,au(f+4|0,n),n=v[f+8>>2],i=v[f+4>>2];r:if(!(n>>>0>=(e=v[i+4>>2])>>>0))for(;;){if(t=v[f+12>>2],i=v[i+12>>2],t>>>0>=d[4+(i+(n<<4)|0)>>2]){for(;;){if((0|e)==(0|(n=n+1|0)))break r;if(v[4+(i+(n<<4)|0)>>2])break}if(v[f+8>>2]=n,t=0,n>>>0>=e>>>0)break r}if(n=v[12+(i+(n<<4)|0)>>2],v[f+12>>2]=t+1,v[f>>2]=n+m(t,20),_n(r,f),n=v[f+8>>2],i=v[f+4>>2],!(n>>>0<(e=v[i+4>>2])>>>0))break}V=f+16|0},function(r,n,f){n|=0,f|=0;var i=0,e=0,t=0,u=0,o=0;r:if(!(d[24+(r|=0)>>2]<=n>>>0)&&(i=v[r+32>>2]+(n<<4)|0,v[i+4>>2])){for(;;){if(!se(4+(v[i+12>>2]+m(e,20)|0)|0,f)){if((e=e+1|0)>>>0<d[i+4>>2])continue;break r}break}if(!((0|e)<0)){if(Ve(v[16+(v[12+((f=n<<4)+v[r+32>>2]|0)>>2]+m(e,20)|0)>>2]),V=n=V-32|0,r=f+v[r+32>>2]|0,f=v[r+4>>2]-1|0,v[r+4>>2]=f,f>>>0>e>>>0)for(o=n+16|0;i=(f=m(e,20))+v[r+12>>2]|0,v[n+12>>2]=v[i>>2],u=zf(o,i+4|0),v[n+28>>2]=v[i+16>>2],i=f+(t=v[r+12>>2])|0,f=t+(t=f+20|0)|0,v[i>>2]=v[f>>2],sf(i+4|0,f+4|0),v[i+16>>2]=v[f+16>>2],f=v[r+12>>2]+t|0,v[f>>2]=v[n+12>>2],sf(f+4|0,u),v[f+16>>2]=v[n+28>>2],Oe(u),(f=v[r+4>>2])>>>0>(e=e+1|0)>>>0;);Oe(4+(v[r+12>>2]+m(f,20)|0)|0),V=n+32|0}}},fi,function(r,n,f){n|=0,f|=0;var i,e=0,t=0,u=0,o=0;V=i=V-16|0,v[12+(r|=0)>>2]=0,v[r+4>>2]=0,v[r+8>>2]=0,v[r>>2]=12960,au(i+4|0,n),n=v[i+8>>2],t=v[i+4>>2];r:if(!(n>>>0>=(e=v[t+4>>2])>>>0))for(;;){if(u=v[i+12>>2],o=v[t+12>>2],u>>>0>=d[4+(o+(n<<4)|0)>>2]){for(;;){if((0|e)==(0|(n=n+1|0)))break r;if(v[4+((n<<4)+o|0)>>2])break}if(v[i+8>>2]=n,u=0,n>>>0>=e>>>0)break r}if(e=v[12+((n<<4)+o|0)>>2],v[i+12>>2]=u+1,e=e+m(u,20)|0,v[e>>2]==(0|f)&&(v[i>>2]=e,_n(r,i),t=v[i+4>>2],n=v[i+8>>2]),!((e=v[t+4>>2])>>>0>n>>>0))break}V=i+16|0},function(){return 21819},function(r){(r|=0)&&(Oe(r+4|0),ar(r))},yi,function(r,n,f){var i;return r|=0,n|=0,f|=0,i=Jt(20),f=v[f>>2],v[i>>2]=v[r>>2],zf(i+4|0,n),v[i+16>>2]=f,0|i},du,uu,function(r,n){zf(r|=0,4+(n|=0)|0)},Bo,function(){return 21822},Iu,zo,function(){return 0|Er(ht(232))},function(r){return 160+(r|=0)|0},cu,function(r){return 176+(r|=0)|0},cu,function(r){return 192+(r|=0)|0},rr,function(r,n,f,i){n|=0,f|=0,i|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),0|La[0|e](n,f,i)},ji,Ge,Ce,Be,Hu,je,function(){return 21825},Iu,zo,function(){return 0|zr(ht(232))},Ue,Pe,oa,function(r){return 32+(r|=0)|0},function(r){return 48+(r|=0)|0},du,uu,ba,aa,ea,function(r){return 116+(r|=0)|0},function(r){return 132+(r|=0)|0},tu,Zt,yu,Xf,Fu,Xf,$u,Xf,mu,wu,Xf,Au,Xf,ri,Xf,ku,Xf,su,Xf,vu,Xf,function(r,n){n|=0;var f,i=0;r:{if(f=v[136+(r|=0)>>2])for(;;){if(se(ha(v[v[r+144>>2]+(i<<2)>>2]),n))break r;if((0|f)==(0|(i=i+1|0)))break}i=-1}return 0|i},function(){return 21828},Iu,function(r,n,f,i){var e;return r|=0,n|=0,f|=0,i=w(i),V=e=V-16|0,n=Sf(e+4|0,n+4|0,v[n>>2],0),v[e>>2]=f,r=0|La[0|r](n,e,i),E(v[e>>2]),Oe(n),V=e+16|0,0|r},function(r,n,f){r|=0,n|=0,f=w(f);var i,e=0,t=0,u=0,o=0,a=0,c=0,b=0;if(V=i=V-32|0,e=v[n>>2],t=0|j(4696),u=0|U(0|e,0|t),E(0|t),o=+G(0|u,21697,0|(t=i+12|0)),W(v[i+12>>2]),E(0|u),v[i+24>>2]=0,v[i+16>>2]=0,v[i+20>>2]=0,v[i+12>>2]=1048,v[i+28>>2]=0,In(e=t,t=o<4294967296&o>=0?~~o>>>0:0,i+28|0),v[i+8>>2]=0,t)for(;V=u=V-16|0,e=v[n>>2],v[u+8>>2]=v[i+8>>2],a=e,e=0|_(21697,u+8|0),c=i,b=0|U(0|a,0|e),v[c+4>>2]=b,E(0|e),V=u+16|0,o=+G(v[i+4>>2],7890,i+28|0),W(v[i+28>>2]),a=v[i+24>>2]+(v[i+8>>2]<<2)|0,e=o<4294967296&o>=0?~~o>>>0:0,v[a>>2]=e,E(v[i+4>>2]),u=v[i+8>>2]+1|0,v[i+8>>2]=u,u>>>0<t>>>0;);return n=r,r=i+12|0,n=Zr(ht(48),n,r,f),Po(r),V=i+32|0,0|n},ka,di,ha,Gi,Te,tu,Zt,Wt,Iu,je,Wt,ma,ma,Iu,function(r){return 1+(d[8+(r|=0)>>2]/19|0)|0},je,function(r,n){n|=0,v[v[16+(r|=0)>>2]+m(n,76)>>2]=0},Ge,Pt,Kr,function(r,n,f,i,e,t,u){r|=0,n|=0,f|=0,i=w(i),e=w(e),t=w(t),u=w(u);var o=0;o=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(o=v[o+v[n>>2]>>2]),La[0|o](n,f,i,e,t,u)},pn,function(r,n,f,i){r|=0,n|=0,f|=0,i=w(i);var e=0;return e=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),w(w(La[0|e](n,f,i)))},function(r,n){return n|=0,w(p[v[16+(r|=0)>>2]+m(n,76)>>2])},Fe,Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|qf(ht(40),v[r>>2])},function(r){return v[(r|=0)>>2]},ze,function(r,n,f,i,e,t){r|=0,n|=0,f|=0,i=w(i),e=w(e),t=w(t);var u=0;u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),La[0|u](n,f,i,e,t)},Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|Ou(ht(40),v[r>>2])},Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|Cu(ht(40),v[r>>2])},Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|ki(ht(40),v[r>>2])},du,uu,sa,it,ke,Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|ci(ht(40),v[r>>2])},du,uu,sa,Ji,Ci,Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|Zf(ht(40),v[r>>2])},function(r){return v[36+(r|=0)>>2]},function(r,n){n|=0,v[36+(r|=0)>>2]=n},je,Ge,_f,function(r,n,f,i,e,t,u,o,a,c,b){r|=0,n|=0,f|=0,i=w(i),e=w(e),t=w(t),u=w(u),o=w(o),a=w(a),c=w(c),b=w(b);var k=0;k=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(k=v[k+v[n>>2]>>2]),La[0|k](n,f,i,e,t,u,o,a,c,b)},Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|Hn(ht(40),v[r>>2])},du,uu,la,sa,function(r,n){n|=0;var f=0;return f=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(f=v[f+v[n>>2]>>2]),r=0|La[0|f](n),0|qn(ht(16),r)},Ko,je,Ye,function(r,n,f,i,e){r|=0,n|=0,f|=0,i=w(i),e|=0;var t,u=0;V=t=V-16|0,n=((u=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=Sf(t+4|0,e+4|0,v[e>>2],0),La[0|r](u,f,i,n),Oe(n),V=t+16|0},Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|Gr(ht(60),v[r>>2])},du,uu,du,uu,sa,ua,function(r,n,f,i){r|=0,n|=0,f=w(f);var e,t=0,u=0,o=0;if(V=e=V-32|0,t=v[(i|=0)>>2],u=0|j(4696),t=0|U(0|t,0|u),E(0|u),o=+G(0|t,21697,0|(u=e+12|0)),W(v[e+12>>2]),E(0|t),v[e+24>>2]=0,v[e+16>>2]=0,v[e+20>>2]=0,v[e+12>>2]=8796,v[e+28>>2]=0,An(u,t=o<4294967296&o>=0?~~o>>>0:0,e+28|0),v[e+8>>2]=0,t)for(;ne(e+4|0,i,e+8|0),o=+G(v[e+4>>2],21686,e+28|0),W(v[e+28>>2]),p[v[e+24>>2]+(v[e+8>>2]<<2)>>2]=o,E(v[e+4>>2]),u=v[e+8>>2]+1|0,v[e+8>>2]=u,u>>>0<t>>>0;);et(i=r,n,f,r=e+12|0),Lo(r),V=e+32|0},Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|Af(ht(36),v[r>>2])},ha,ta,na,je,nt,Ae,Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|Mr(ht(36),v[r>>2])},ha,na,je,ta,_e,function(r,n,f,i,e){r|=0,n|=0,f|=0,i=w(i),e|=0;var t=0;t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),La[0|t](n,f,i,e)},Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|No(ht(40),v[r>>2])},si,function(r,n,f,i,e,t,u,o,a){r|=0,n|=0,f|=0,i=w(i),e=w(e),t=w(t),u|=0,o|=0,a|=0;var c=0;c=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(c=v[c+v[n>>2]>>2]),La[0|c](n,f,i,e,t,u,o,a)},Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|xo(ht(40),v[r>>2])},Bi,Ci,Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|bi(ht(40),v[r>>2])},ft,ke,Wt,ma,ma,Iu,Ne,function(r){return r|=0,0|Vo(ht(40),v[r>>2])},function(){return 21831},Iu,zo,function(){return 0|ln(ht(172))},Bo,je,qo,je,Jo,function(r){return v[28+(r|=0)>>2]},function(r){return v[32+(r|=0)>>2]},je,gu,ou,tu,Zt,Ju,function(r,n){r|=0,n=w(n),p[r+64>>2]=n,p[r+60>>2]=n},Ie,Se,du,uu,function(r){r|=0;var n=w(0),f=w(0);n=p[r+52>>2];r:{if(l[r+36|0]){if((f=w(p[r+56>>2]-n))==w(0))break r;return w(w(n+Or(p[r+72>>2],f)))}n=(n=w(p[r+72>>2]+n))<(f=p[r+56>>2])?n:f}return w(n)},Ie,function(r){return p[72+(r|=0)>>2]>=w(p[r+56>>2]-p[r+52>>2])|0},je,function(r){v[152+(r|=0)>>2]=0},Be,function(){return 21833},Iu,Ne,function(r){return r|=0,0|qe(ht(24),v[r>>2])},tu,Zt,fa,je,xi,Pf,function(r,n,f,i){var e;r|=0,n|=0,f|=0,i=w(i),V=e=V-16|0,p[e+12>>2]=i,Vt(e,n,f),Gn(r+12|0,e,e+12|0),V=e+16|0},function(r,n,f,i,e){r|=0,n|=0,f|=0,i|=0,e=w(e);var t=0;t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),La[0|t](n,f,i,e)},Jn,function(r,n,f,i){n|=0,f|=0,i|=0;var e=0;return e=v[(r|=0)>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),w(w(La[0|e](n,f,i)))},function(){return 21836},Iu,Ne,function(r){return r|=0,0|bn(ht(108),v[r>>2])},Bo,je,ua,function(r){return w(p[104+(r|=0)>>2])},function(r,n){r|=0,n=w(n),p[r+104>>2]=n},Ie,Se,Cr,Se,or,Te,Qf,Be,ef,Ge,function(r,n,f,i){return f|=0,i|=0,0|ff(r|=0,n|=0,ri(v[v[r+16>>2]+4>>2],f),i)},function(r,n,f,i,e){n|=0,f|=0,i|=0,e|=0;var t,u=0;return V=t=V-16|0,n=((u=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=Sf(t+4|0,i+4|0,v[i>>2],0),r=0|La[0|r](u,f,n,e),Oe(n),V=t+16|0,0|r},function(r,n,f,i){return 0|ff(r|=0,n|=0,f|=0,i|=0)},function(r,n,f,i,e){return r|=0,n|=0,f|=0,i|=0,e=w(e),0|vn(r,n,ri(v[v[r+16>>2]+4>>2],f),i,e)},function(r,n,f,i,e,t){r|=0,n|=0,f|=0,i|=0,e|=0,t=w(t);var u,o=0;return V=u=V-16|0,n=((o=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&o?v[v[n>>2]+r>>2]:r,o=n,n=Sf(u+4|0,i+4|0,v[i>>2],0),r=0|La[0|r](o,f,n,e,t),Oe(n),V=u+16|0,0|r},vn,function(r,n,f,i,e,t){r|=0,n|=0,f|=0,i|=0,e|=0,t=w(t);var u=0;return u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),0|La[0|u](n,f,i,e,t)},function(r,n,f){return r|=0,n|=0,f=w(f),ti(),r=ff(r,n,21044,0),p[r+84>>2]=f,p[r+100>>2]=f,0|r},function(r,n,f,i){r|=0,n|=0,f|=0,i=w(i);var e=0;return e=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(e=v[e+v[n>>2]>>2]),0|La[0|e](n,f,i)},function(r,n,f,i){return r|=0,n|=0,f=w(f),i=w(i),ti(),r=vn(r,n,21044,0,w(i-(i<=w(0)?f:w(0)))),p[r+84>>2]=f,p[r+100>>2]=f,0|r},function(r,n,f,i,e){r|=0,n|=0,f|=0,i=w(i),e=w(e);var t=0;return t=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(t=v[t+v[n>>2]>>2]),0|La[0|t](n,f,i,e)},function(r,n){r|=0,n=w(n);var f,i,t=0,u=0;if(t=v[r+72>>2],i=l[t+28|0],e[t+28|0]=1,f=v[r+44>>2]){for(t=0;v[v[r+52>>2]+(t<<2)>>2]&&(ti(),u=ff(r,t,21044,0),p[u+84>>2]=n,p[u+100>>2]=n),(0|f)!=(0|(t=t+1|0)););t=v[r+72>>2]}e[t+28|0]=i,mr(t)},function(r,n){return n|=0,0|(d[44+(r|=0)>>2]>n>>>0?v[v[r+52>>2]+(n<<2)>>2]:0)},Te,function(r){e[v[72+(r|=0)>>2]+28|0]=1},function(r){e[v[72+(r|=0)>>2]+28|0]=0},function(){return 21795},Iu,Ne,function(r){return r|=0,0|sr(ht(180),v[r>>2])},fa,je,la,sa,ua,function(r){return 56+(r|=0)|0},ca,function(r){return 88+(r|=0)|0},ia,du,uu,function(r){return 0|Aa(r|=0)},cu,tu,Zt,nr,Be,wn,ya,jr,hf,function(r){return v[12+(r|=0)>>2]?v[v[r+20>>2]>>2]:0},je,Eu,Xf,function(r,n){return 0|zi(8+(r|=0)|0,n|=0)},Xf,Tu,Xf,function(r,n){return 0|zi(24+(r|=0)|0,n|=0)},pu,hi,Lr,Ge,function(r,n,f){return n|=0,f|=0,0|Ui(r|=0,mu(v[r+4>>2],n),f)},function(r,n,f,i){n|=0,f|=0,i|=0;var e,t=0;return V=e=V-32|0,n=((t=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&t?v[v[n>>2]+r>>2]:r,t=n,n=Sf(e+20|0,f+4|0,v[f>>2],0),f=Sf(e+8|0,i+4|0,v[i>>2],0),r=0|La[0|r](t,n,f),Oe(f),Oe(n),V=e+32|0,0|r},Ui,Df,function(r,n,f){n|=0,f|=0;var i,e=0,t=0;if(i=v[28+(r|=0)>>2])for(;;){if(t=v[v[r+36>>2]+(e<<2)>>2],se(la(v[t+4>>2]),n))return void $e(t,r=v[f+4>>2]?Ui(r,e,f):0);if((0|i)==(0|(e=e+1|0)))break}},jf,function(r,n){n|=0;var f=0,i=0,e=0;r:if(i=v[60+(r|=0)>>2]){for(;;){if(e=v[v[r+68>>2]+(f<<2)>>2],!se(ha(v[e+4>>2]),n)){if((0|i)!=(0|(f=f+1|0)))continue;break r}break}return 0|e}return 0},Xf,function(r,n){n|=0;var f=0,i=0,e=0;r:if(i=v[76+(r|=0)>>2]){for(;;){if(e=v[v[r+84>>2]+(f<<2)>>2],!se(ha(v[e+4>>2]),n)){if((0|i)!=(0|(f=f+1|0)))continue;break r}break}return 0|e}return 0},Xf,function(r,n){n|=0;var f=0,i=0,e=0;r:if(i=v[92+(r|=0)>>2]){for(;;){if(e=v[v[r+100>>2]+(f<<2)>>2],!se(ha(v[e+4>>2]),n)){if((0|i)!=(0|(f=f+1|0)))continue;break r}break}return 0|e}return 0},Xf,Xt,Se,Wt,Iu,Ge,pe,Be,Wt,ma,ma,Iu,ye,function(r,n){r|=0,n|=0;var f,i=w(0);return f=ht(12),i=p[r>>2],p[f+8>>2]=p[n>>2],p[f+4>>2]=i,v[f>>2]=11232,0|f},tu,Zt,Ge,pe,Be,Wt,ma,ma,Iu,Le,function(r,n){r|=0,n|=0;var f,i=w(0);return f=ht(32),i=p[r>>2],v[f+28>>2]=n,v[f+24>>2]=0,v[f+16>>2]=0,v[f+20>>2]=0,p[f+12>>2]=i,v[f+4>>2]=0,v[f+8>>2]=0,v[f>>2]=11264,0|f},Ge,pe,Be,tu,Zt,function(r){return w(p[16+(r|=0)>>2])},function(r,n){r|=0,n=w(n),p[r+16>>2]=n*w(.01745329238474369)},Ie,Se,function(){return 21841},function(r){(r|=0)&&ar(Do(r))},du,uu,oa,je,ma,je,function(){return 21682},va,du,uu,function(){return 21846},function(r){(r|=0)&&ar(Sr(r))},zo,function(){var r,n,f;return r=Jt(132),v[r+24>>2]=17112,v[r+8>>2]=17112,v[r+4>>2]=1065353216,e[0|r]=0,v[r+20>>2]=0,v[r+12>>2]=0,v[r+16>>2]=0,Ze(r+28|0,0,44),v[r+124>>2]=0,v[r+128>>2]=0,v[r+120>>2]=17144,v[r+116>>2]=0,v[r+108>>2]=0,v[r+112>>2]=0,v[r+104>>2]=11308,v[r+100>>2]=1065353216,v[r+92>>2]=1065353216,v[r+96>>2]=1065353216,v[r+84>>2]=0,v[r+88>>2]=1065353216,v[r+76>>2]=0,v[r+80>>2]=0,v[r+72>>2]=17128,n=Jt(52),V=f=V-16|0,v[n+12>>2]=0,v[n+4>>2]=0,v[n+8>>2]=0,v[n+20>>2]=0,v[n+24>>2]=0,v[n+16>>2]=9244,v[n>>2]=11644,v[n+28>>2]=0,v[n+32>>2]=0,v[n+36>>2]=0,v[n+40>>2]=0,v[n+44>>2]=0,v[n+48>>2]=0,v[f+12>>2]=0,Tn(n,6,f+12|0),V=f+16|0,v[r+64>>2]=n,Qi(r+8|0,6),Qi(r+24|0,6),0|r},gu,ou,tu,Zt,function(r,n){var f=0,i=0,e=0,t=0,u=0;return(0|(n|=0))!=v[44+(r|=0)>>2]&&((f=v[r+56>>2])&&La[v[v[f>>2]+4>>2]](f),(f=v[r+52>>2])&&La[v[v[f>>2]+4>>2]](f),(f=v[r+48>>2])&&La[v[v[f>>2]+4>>2]](f),(f=v[r+40>>2])&&La[v[v[f>>2]+4>>2]](f),v[r+44>>2]=n,t=r,u=sr(ht(180),v[r+44>>2]),v[t+40>>2]=u,t=r,u=qe(ht(24),v[r+44>>2]),v[t+48>>2]=u,t=r,u=bn(ht(108),v[r+48>>2]),v[t+52>>2]=u,t=r,u=Er(ht(232)),v[t+56>>2]=u,ya(v[r+40>>2]),wn(v[r+40>>2]),n=v[r+52>>2],(e=v[(f=n)+12>>2])&&(!(i=v[n+8>>2])|(0|r)==(0|i)||(La[0|e](i),f=v[r+52>>2])),v[n+12>>2]=0,v[n+8>>2]=r,v[f+96>>2]=0,v[f+92>>2]=1706),v[r+40>>2]},Te,function(r,n,f,i){r|=0,n=w(n),f|=0,i|=0;var e=0,t=0;if(e=v[r+40>>2]){if(e=ri(v[e+4>>2],f),f=v[r+52>>2],!e)return Qf(f),ya(v[r+40>>2]),0;t=ff(f,t=n<w(4294967296)&n>=w(0)?~~n>>>0:0,e,i),or(v[r+52>>2],v[r+40>>2]),wn(v[r+40>>2])}return 0|t},function(r,n,f,i,e){r|=0,n|=0,f=w(f),i|=0,e|=0;var t,u=0;return V=t=V-16|0,n=((u=v[r+4>>2])>>1)+n|0,r=v[r>>2],r=1&u?v[v[n>>2]+r>>2]:r,u=n,n=Sf(t+4|0,i+4|0,v[i>>2],0),r=0|La[0|r](u,f,n,e),Oe(n),V=t+16|0,0|r},function(r,n){var f;n|=0,(f=v[40+(r|=0)>>2])&&(pu(f,n),hf(v[r+40>>2]))},hi,function(r,n){var f;r|=0,n=w(n),(f=v[r+40>>2])&&(Xt(f,n=w(p[r+4>>2]*n)),Cr(v[r+52>>2],n),or(v[r+52>>2],v[r+40>>2]),$r(r))},Se,function(r){r|=0;var n,f,i,e,t=0,u=0,o=0,a=0,c=0,b=0,k=0,d=0,y=0,g=w(0),F=w(0),A=0,T=0,$=0,I=0,C=0,P=0,E=0,O=w(0),R=w(0),S=0,W=0,G=w(0),U=0;l[r+86|0]&&(v[r+108>>2]=0),wn(v[r+40>>2]),v[5464]=v[5463],v[5466]=v[5465],t=v[r+64>>2],V=a=V-16|0,v[a+4>>2]=0,Tn(t,0,u=a+4|0),un(t+16|0,0,u=$t(u,8610,0)),Oe(u),v[t+32>>2]=0,v[t+36>>2]=0,V=a+16|0,v[v[r+64>>2]+48>>2]=l[r+84|0]?28:24,V=n=V+-64|0,a=r,i=v[r+40>>2],e=v[i+44>>2],t=l[r+84|0],v[n+48>>2]=v[5471],r=v[5470],v[n+40>>2]=v[5469],v[n+44>>2]=r,r=v[5468],v[n+32>>2]=v[5467],v[n+36>>2]=r,f=zf(n+52|0,21888),(r=v[a+60>>2])&&La[v[v[r>>2]+12>>2]](r,v[a+40>>2]);r:if(r=v[5476]){for(E=(P=t?28:24)>>>2|0,t=v[a+44>>2],u=r;;){if((0|t)!=v[u+4>>2]){if(u=v[u+12>>2])continue;break r}break}n:{f:{for(;;){if((0|t)==v[r+4>>2])break f;if(!(r=v[r+12>>2]))break}W=0;break n}W=v[r+8>>2]}if(I=Aa(v[a+40>>2]),e)for(U=a+104|0;;){y=v[v[i+52>>2]+(S<<2)>>2],c=v[y+8>>2];n:if(0|La[v[v[c>>2]+16>>2]](c))if(v[y+60>>2]){F=p[a+100>>2],O=p[a+96>>2],g=p[a+92>>2],R=p[a+88>>2],r=v[y+60>>2];f:{i:{if(u=o=v[W+4>>2])for(;;){if(v[u+4>>2]==(0|r))break i;if(!(u=v[u+12>>2]))break}t=gn(r);break f}i:{for(;;){if(v[o+4>>2]==(0|r))break i;if(!(o=v[o+12>>2]))break}t=0;break f}t=v[o+8>>2]}f:if(l[a+87|0]&&(u=o=v[a+124>>2])){for(;;){if((0|y)!=v[u+4>>2]){if(u=v[u+20>>2])continue;break f}break}i:{for(;;){if((0|y)==v[o+4>>2])break i;if(!(o=v[o+20>>2]))break}r=0;break f}t=(r=v[o+16>>2])||t,r=v[o+12>>2]}f:{i:{e:{t:{u:{if(Su(u=0|La[v[v[r>>2]+8>>2]](r),21440)){if(sf(f,t+8|0),t=v[t+4>>2],$=(k=v[t+12>>2])<<1,o=v[t+8>>2],d=v[5464],b=v[5466],!l[a+84|0])break u;if(u=0,(0|o)<=0)break i;for(;A=v[t>>2]+m(u,24)|0,C=v[A+16>>2],T=d+m(u,28)|0,v[T+12>>2]=v[A+12>>2],v[T+16>>2]=C,(0|o)!=(0|(u=u+1|0)););break i}if(Su(u,21344)){if(sf(f,u=t+8|0),t=v[t+4>>2],o=v[t+8>>2],c=v[t+12>>2],d=v[5464],k=v[5466],se(f,u),$=c<<1,!l[a+84|0])break t;if(u=0,(0|o)<=0)break e;for(;T=v[t>>2]+m(u,24)|0,A=v[T+16>>2],b=d+m(u,28)|0,v[b+12>>2]=v[T+12>>2],v[b+16>>2]=A,(0|o)!=(0|(u=u+1|0)););break e}if(u=Su(u,21168),t=v[a+56>>2],u){rr(t,y,r);break n}ji(t,y);break n}Me(d,v[t>>2],m(o,P));break i}Me(d,v[t>>2],m(o,P))}t=Me(k,v[t+4>>2],$),lr(r,y,0,v[r+52>>2],d,0,E),v[n+44>>2]=c,v[n+40>>2]=o,v[n+36>>2]=t,v[n+32>>2]=d,$=1,r=Fa(r);break f}t=Me(b,v[t+4>>2],$),$=0,rn(r,c,d,0,E),v[n+44>>2]=k,v[n+40>>2]=o,v[n+36>>2]=t,v[n+32>>2]=d,r=$a(r)}t=(F=w(w(F*p[r+16>>2])*w(w(p[I+16>>2]*w(255))*p[y+32>>2])))<w(4294967296)&F>=w(0)?~~F>>>0:0,F=l[a+85|0]?w(t>>>0):w(255),G=w(g*p[r+8>>2]),u=(g=w(F*w(G*w(p[I+8>>2]*p[y+24>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0,u<<=8,R=w(R*p[r+4>>2]),o=u+((o=(g=w(w(R*w(p[I+4>>2]*p[y+20>>2]))*F))<w(4294967296)&g>=w(0)?~~g>>>0:0)+(t<<24)|0)|0,O=w(O*p[r+12>>2]),d=(r=(g=w(F*w(O*w(p[I+12>>2]*p[y+28>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0)<<16,r=0,t=0,u=0,l[y+56|0]&&(r=(g=w(F*w(O*w(p[I+12>>2]*p[y+48>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0,t=(g=w(F*w(G*w(p[I+8>>2]*p[y+44>>2]))))<w(4294967296)&g>=w(0)?~~g>>>0:0,u=(F=w(F*w(R*w(p[I+4>>2]*p[y+40>>2]))))<w(4294967296)&F>=w(0)?~~F>>>0:0),d=o+d|0,c=l[a+85|0],b=l[a+84|0],k=Hu(v[a+56>>2]),o=v[n+32>>2];f:if(b){if(c=(u+((t<<8)+(r<<16)|0)|0)+(c?-16777216:0)|0,k){if(fr(v[a+56>>2],o,v[n+36>>2],v[n+44>>2],o+12|0,E),r=v[a+56>>2],!(u=v[r+180>>2])){ji(r,y);break n}if(k=v[5464],o=v[5466],t=(b=v[r+164>>2])>>1,v[n+40>>2]=t,v[n+36>>2]=o,v[n+32>>2]=k,v[n+44>>2]=u,Me(o,v[r+188>>2],u<<1),o=v[r+204>>2],k=v[r+172>>2],T=v[n+32>>2],v[a+60>>2]){if((0|b)<2)break f;for(A=(0|t)<=1?1:t,u=0,r=0;t=T+m(r,28)|0,b=u<<2,p[t>>2]=p[b+k>>2],C=4|b,p[t+4>>2]=p[C+k>>2],p[t+12>>2]=p[o+b>>2],p[t+16>>2]=p[o+C>>2],b=v[a+60>>2],La[v[v[b>>2]+16>>2]](b,t,t+4|0),v[t+24>>2]=c,v[t+20>>2]=d,u=u+2|0,(0|A)!=(0|(r=r+1|0)););break f}if((0|b)<2)break f;for(A=(0|t)<=1?1:t,u=0,r=0;t=T+m(r,28)|0,b=u<<2,p[t>>2]=p[b+k>>2],C=4|b,p[t+4>>2]=p[C+k>>2],p[t+12>>2]=p[o+b>>2],F=p[o+C>>2],v[t+24>>2]=c,v[t+20>>2]=d,p[t+16>>2]=F,u=u+2|0,(0|A)!=(0|(r=r+1|0)););}else if(u=v[n+40>>2],v[a+60>>2]){if(t=0,!u)break f;for(;k=v[a+60>>2],r=o+m(t,28)|0,La[v[v[k>>2]+16>>2]](k,r,r+4|0),v[r+24>>2]=c,v[r+20>>2]=d,(0|u)!=(0|(t=t+1|0)););}else if(r=0,u)for(;t=o+m(r,28)|0,v[t+24>>2]=c,v[t+20>>2]=d,(0|u)!=(0|(r=r+1|0)););}else{if(k){if(fr(v[a+56>>2],o,v[n+36>>2],v[n+44>>2],o+12|0,E),r=v[a+56>>2],!(u=v[r+180>>2])){ji(r,y);break n}if(c=v[5464],o=v[5466],t=(b=v[r+164>>2])>>1,v[n+40>>2]=t,v[n+36>>2]=o,v[n+32>>2]=c,v[n+44>>2]=u,Me(o,v[r+188>>2],u<<1),c=v[r+204>>2],k=v[r+172>>2],T=v[n+32>>2],v[a+60>>2]){if((0|b)<2)break f;for(b=(0|t)<=1?1:t,o=0,u=0;r=T+m(o,24)|0,t=u<<2,p[r>>2]=p[t+k>>2],A=4|t,p[r+4>>2]=p[A+k>>2],p[r+12>>2]=p[t+c>>2],p[r+16>>2]=p[c+A>>2],t=v[a+60>>2],La[v[v[t>>2]+16>>2]](t,r,r+4|0),v[r+20>>2]=d,u=u+2|0,(0|b)!=(0|(o=o+1|0)););break f}if((0|b)<2)break f;for(b=(0|t)<=1?1:t,o=0,u=0;r=T+m(o,24)|0,t=u<<2,p[r>>2]=p[t+k>>2],A=4|t,p[r+4>>2]=p[A+k>>2],p[r+12>>2]=p[t+c>>2],F=p[c+A>>2],v[r+20>>2]=d,p[r+16>>2]=F,u=u+2|0,(0|b)!=(0|(o=o+1|0)););break f}if(t=v[n+40>>2],v[a+60>>2]){if(r=0,!t)break f;for(;c=v[a+60>>2],u=o+m(r,24)|0,La[v[v[c>>2]+16>>2]](c,u,u+4|0),v[u+20>>2]=d,(0|t)!=(0|(r=r+1|0)););break f}if(r=0,!t)break f;for(;v[20+(o+m(r,24)|0)>>2]=d,(0|t)!=(0|(r=r+1|0)););}if(v[5464]=v[5464]+m(v[n+40>>2],P),v[5466]=v[5466]+(v[n+44>>2]<<1),l[a+86|0]&&(r=v[a+108>>2],v[n+24>>2]=0,v[n+16>>2]=0,v[n+20>>2]=0,v[n+8>>2]=0,v[n+12>>2]=0,cn(U,r+1|0,n+8|0),r=v[a+116>>2]+m(r,20)|0,v[r>>2]=$,t=v[a+64>>2],v[r+4>>2]=v[t+32>>2],v[r+8>>2]=v[n+40>>2],v[r+12>>2]=v[t+36>>2],v[r+16>>2]=v[n+44>>2]),v[n+48>>2]=v[v[y+4>>2]+80>>2],V=d=V-16|0,t=v[a+64>>2],r=n+32|0,!(u=v[t+4>>2])|v[(v[t+12>>2]+(u<<2)|0)-4>>2]!=v[r+16>>2]||!se((v[t+28>>2]+m((u>>>0)/5|0,12)|0)-12|0,r+20|0)?(v[d+12>>2]=0,Tn(t,u+5|0,d+12|0),u=v[t+12>>2]+(u<<2)|0,v[u>>2]=v[r>>2],v[u+4>>2]=v[r+4>>2],v[u+8>>2]=v[r+8>>2],v[u+12>>2]=v[r+12>>2],v[u+16>>2]=v[r+16>>2],On(t+16|0,r+20|0)):(u=v[t+12>>2]+(u<<2)|0,v[(o=u-12|0)>>2]=v[o>>2]+v[r+8>>2],v[(u=u-8|0)>>2]=v[u>>2]+v[r+12>>2]),u=v[r+12>>2])for(c=v[r+4>>2],k=v[t+32>>2],o=0;s[($=c+(o<<1)|0)>>1]=k+h[$>>1],(0|u)!=(0|(o=o+1|0)););if(c=v[r+8>>2])for(k=v[t+48>>2]>>>2|0,r=v[r>>2],o=0;v[8+(r+(m(o,k)<<2)|0)>>2]=0,(0|c)!=(0|(o=o+1|0)););v[t+32>>2]=c+v[t+32>>2],v[t+36>>2]=u+v[t+36>>2],V=d+16|0,ji(v[a+56>>2],y)}else ji(v[a+56>>2],y);if((0|(S=S+1|0))==(0|e))break}Ce(v[a+56>>2]),(r=v[a+60>>2])&&La[v[v[r>>2]+20>>2]](r)}return Oe(f),V=n- -64|0,wf(21888,8610),r=v[5463],t=v[a+64>>2],v[t+44>>2]=v[5465],v[t+40>>2]=r,v[a+64>>2]},je,function(r,n){n|=0,e[85+(r|=0)|0]=n},Ge,function(r,n){n|=0,e[84+(r|=0)|0]=n},function(r,n,f,i,e){r|=0,n=w(n),f=w(f),i=w(i),e=w(e),p[r+100>>2]=e,p[r+96>>2]=i,p[r+92>>2]=f,p[r+88>>2]=n},function(r,n,f,i,e,t){r|=0,n|=0,f=w(f),i=w(i),e=w(e),t=w(t);var u=0;u=v[r>>2],n=((r=v[r+4>>2])>>1)+n|0,1&r&&(u=v[u+v[n>>2]>>2]),La[0|u](n,f,i,e,t)},so,Ge,so,Ge,function(r){v[60+(r|=0)>>2]=0},Be,function(r){return v[52+(r|=0)>>2]},je,function(r,n,f,i){r|=0,n|=0,f|=0,i=w(i),xi(v[r+48>>2],n,f,i)},Pf,function(r,n){n|=0,v[68+(r|=0)>>2]=n},Ge,function(r,n,f){r|=0,n|=0,f|=0;var i,e=0,t=0;if(V=i=V-16|0,v[i+12>>2]=n,v[i+8>>2]=f,!v[f+8>>2]){r:{n:{if(n=v[r+76>>2])for(f=v[i+8>>2];;){if((0|f)==v[n+4>>2])break n;if(!(n=v[n+12>>2]))break}n=ht(20),v[n+12>>2]=0,v[n+16>>2]=0,v[n>>2]=17176,v[n+4>>2]=v[i+8>>2],v[n+8>>2]=v[i+12>>2],(f=v[r+76>>2])&&(v[f+16>>2]=n,v[n+12>>2]=f),v[r+76>>2]=n,v[r+80>>2]=v[r+80>>2]+1;break r}v[n+4>>2]=f,v[n+8>>2]=v[i+12>>2]}f=v[i+8>>2],(t=v[(n=f)+12>>2])&&(!(e=v[f+8>>2])|(0|r)==(0|e)||(La[0|t](e),n=v[i+8>>2])),v[f+12>>2]=0,v[f+8>>2]=r,v[n+168>>2]=0,v[n+164>>2]=1707}V=i+16|0},Ae,function(r,n){n|=0,e[86+(r|=0)|0]=n},ia,function(r,n,f,i,t){r|=0,n|=0,f|=0,i|=0,t|=0;var u=0,o=0,a=w(0),c=w(0),b=0,k=0,s=0,h=0,d=0;r:if((u=v[r+40>>2])&&(o=Tu(u,n))&&(u=v[o+60>>2])){if(t&&(u=0|La[v[v[u>>2]+12>>2]](u)),Su(0|La[v[v[u>>2]+8>>2]](u),21440)){if(a=w(f>>>0),p[u+68>>2]=a,c=w(i>>>0),p[u+72>>2]=c,p[u+76>>2]=a,p[u+80>>2]=c,p[u+52>>2]=a,p[u+56>>2]=c,uf(u,w(0),w(0),w(1),w(1),0),Br(u),!(n=gn(v[o+60>>2])))break r;for(t&&(n=k=Ei(n)),i=v[u+112>>2],h=v[v[n+4>>2]>>2],n=0,f=0;s=h+m(n,24)|0,b=f<<2,p[s+12>>2]=p[b+i>>2],p[s+16>>2]=p[i+(4|b)>>2],f=f+2|0,4!=(0|(n=n+1|0)););}else if(Su(0|La[v[v[u>>2]+8>>2]](u),21344)){if(a=w(f>>>0),p[u+84>>2]=a,c=w(i>>>0),p[u+88>>2]=c,p[u+92>>2]=a,p[u+96>>2]=c,p[u+196>>2]=a,p[u+200>>2]=c,p[u+180>>2]=0,p[u+184>>2]=0,p[u+188>>2]=1,p[u+192>>2]=1,e[u+228|0]=1,v[u+232>>2]=0,hr(u),!(n=gn(v[o+60>>2])))break r;if(t&&(n=k=Ei(n)),f=v[u+52>>2])for(h=v[v[n+4>>2]>>2],s=f-1>>>1|0,i=v[u+116>>2],f=0,n=0;b=h+m(n,24)|0,d=f<<2,p[b+12>>2]=p[i+d>>2],p[b+16>>2]=p[i+(4|d)>>2],f=f+2|0,b=(0|n)!=(0|s),n=n+1|0,b;);}n:{f:{if(f=v[r+124>>2]){n=f;i:{for(;;){if(v[n+4>>2]!=(0|o)){if(n=v[n+20>>2])continue;break i}break}for(;;){if(v[f+4>>2]!=(0|o)){if(f=v[f+20>>2])continue;break i}break}!(i=v[f+12>>2])|!l[f+8|0]||(n=v[f+16>>2],La[v[v[i>>2]+4>>2]](i),n&&La[v[v[n>>2]+4>>2]](n))}if(n=v[r+124>>2])for(;;){if(v[n+4>>2]==(0|o))break f;if(!(n=v[n+20>>2]))break}}n=ht(28),v[n+20>>2]=0,v[n+24>>2]=0,v[n>>2]=17160,e[n+8|0]=t,v[n+4>>2]=o,v[n+16>>2]=k,v[n+12>>2]=u,(f=v[r+124>>2])&&(v[f+24>>2]=n,v[n+20>>2]=f),v[r+124>>2]=n,v[r+128>>2]=v[r+128>>2]+1;break n}e[n+8|0]=t,v[n+4>>2]=o,v[n+16>>2]=k,v[n+12>>2]=u}nr(v[r+40>>2])}},function(r,n,f,i,e,t){n|=0,f|=0,i|=0,e|=0,t|=0;var u,o=0;V=u=V-16|0,n=((o=v[4+(r|=0)>>2])>>1)+n|0,r=v[r>>2],r=1&o?v[v[n>>2]+r>>2]:r,o=n,n=Sf(u+4|0,f+4|0,v[f>>2],0),La[0|r](o,n,i,e,t),Oe(n),V=u+16|0},function(r){Sr(r|=0),ar(r)},function(r,n,f){n|=0,f|=0;var i=0;r:if((i=v[40+(r|=0)>>2])&&(i=Tu(i,n))&&(e[r+87|0]=1,n=v[r+124>>2])){for(r=n;;){if((0|i)!=v[r+4>>2]){if(r=v[r+20>>2])continue;break r}break}for(;;){if((0|i)!=v[n+4>>2]){if(n=v[n+20>>2])continue;break r}break}(r=v[n+16>>2])&&sf(r+8|0,f)}},jf,function(r,n,f,i){var e;r|=0,n|=0,f|=0,i=w(i),V=e=V-16|0,r=v[r>>2],p[e+12>>2]=i,La[0|r](n,f,e+12|0),V=e+16|0},Ot,Re,Re,Ot,Re,function(r,n,f,i){var e;n|=0,f|=0,i|=0,V=e=V-16|0,r=v[(r|=0)>>2],s[e+14>>1]=i,La[0|r](n,f,e+14|0),V=e+16|0},function(r,n,f,i){var e,t;n|=0,f|=0,i|=0,V=e=V-16|0,r=v[(r|=0)>>2],t=n,n=Sf(e+4|0,i+4|0,v[i>>2],0),La[0|r](t,f,n),Oe(n),V=e+16|0},Re,cu,cu,function(r,n,f,i){return r|=0,n|=0,f=w(f),i=w(i),0|La[v[r>>2]](n,f,i)},function(r,n,f,i,e,t){return r|=0,n|=0,f=w(f),i=w(i),e=w(e),t=w(t),0|La[v[r>>2]](n,f,i,e,t)},cu,cu,cu,cu,cu,function(r,n,f,i,e){r|=0,n|=0,f|=0,i=w(i),e=w(e),La[v[r>>2]](n,f,i,e)},cu,Li,Li,cu,cu,cu,cu,cu,function(r,n,f){n|=0,f|=0,La[v[(r|=0)>>2]](n,f)},cu,cu,cu,cu,function(r,n,f){var i;return n|=0,f|=0,V=i=V-16|0,La[v[(r|=0)>>2]](i,n,f),r=qn(ht(16),i),yf(i),V=i+16|0,0|r},function(r,n){var f;return n|=0,V=f=V-16|0,La[v[(r|=0)>>2]](f,n),r=Bn(ht(16),f),Zu(f),V=f+16|0,0|r},function(r,n,f){var i;return n|=0,f|=0,V=i=V-16|0,La[v[(r|=0)>>2]](i,n,f),r=Bn(ht(16),i),Zu(i),V=i+16|0,0|r},Li,cu,cu,cu,cu,cu,cu,cu,cu,cu,cu,cu,cu,cu,cu,cu,function(r,n,f,i,e){var t;r|=0,n|=0,f|=0,i=w(i),e|=0,V=t=V-16|0,r=v[r>>2],v[t+12>>2]=e,La[0|r](n,f,i,t+12|0),E(v[t+12>>2]),V=t+16|0},cu,cu,cu,cu,cu,function(r,n,f,i,e){return n|=0,f|=0,i|=0,e|=0,0|La[v[(r|=0)>>2]](n,f,i,e)},cu,cu,cu,cu,cu,cu,function(r,n,f,i){r|=0,n|=0,f=w(f),i=w(i),La[v[r>>2]](n,f,i)},cu,function(){return 21849},va,function(r){La[0|(r|=0)]()},function(){var r=0,n=0;r=Jt(4),v[r>>2]=9616,v[r>>2]=17336,v[5316]=r,v[5463]||(n=X(3669960),v[5463]=n,r=X(1048560),v[5464]=n,v[5465]=r,v[5466]=r)},function(){var r=0;(r=v[5316])&&La[v[v[r>>2]+4>>2]](r),(r=v[5487])&&(ar(r),v[5487]=0),ar(v[5463]),v[5463]=0,ar(v[5465]),v[5465]=0},function(r,n){return n|=0,0|La[0|(r|=0)](n)},function(r){return r=Jt(r|=0),v[5487]=r,0|r},function(){var r;(r=v[5487])&&(ar(r),v[5487]=0)},Ee,function(r){r|=0;var n=0,f=0;r:if(n=v[5482]){for(;;){if(!se(n+4|0,r)){if(n=v[n+20>>2])continue;break r}break}if(n=v[5482]){for(;;){if(!se(n+4|0,r)){if(n=v[n+20>>2])continue;break r}break}f=v[n+16>>2]}}return 0|f},function(r,n,f,i,e){var t;return r|=0,f|=0,i|=0,e|=0,V=t=V-32|0,n=Sf(t+20|0,4+(n|=0)|0,v[n>>2],0),f=Sf(t+8|0,f+4|0,v[f>>2],0),v[t>>2]=e,v[t+4>>2]=i,r=0|La[0|r](n,f,t+4|0,t),E(v[t>>2]),E(v[t+4>>2]),Oe(f),Oe(n),V=t+32|0,0|r},function(r,n,f,i){r|=0,n|=0,f|=0,i|=0;var t,u,o,a,c,b,k=0,h=0,y=0,g=0,F=0,A=0,T=0,$=0,I=w(0),C=0,P=w(0),O=0,R=0,S=0,L=0,M=0,_=0,z=0,x=0,J=0,K=0,B=0,N=0,q=w(0),D=0,Z=w(0),Y=0;if(V=o=V+-64|0,y=v[f>>2],k=0|j(4696),y=0|U(0|y,0|k),E(0|k),b=+G(0|y,21697,0|(k=o+48|0)),W(v[o+48>>2]),E(0|y),v[o+60>>2]=0,v[o+52>>2]=0,v[o+56>>2]=0,v[o+48>>2]=9244,v[o+44>>2]=0,v[o+36>>2]=0,v[o+40>>2]=0,v[o+32>>2]=9244,un(h=k,y=b<4294967296&b>=0?~~b>>>0:0,k=$t(o+20|0,8610,0)),Oe(k),un(o+32|0,y,k=$t(o+20|0,8610,0)),Oe(k),v[o+16>>2]=0,y)for(;ne(T=o+12|0,f,k=o+16|0),Ii(C=o+20|0,T),sf(v[o+60>>2]+m(v[o+16>>2],12)|0,C),Oe(C),E(v[o+12>>2]),ne(T,i,k),Ii(C,T),sf(v[o+44>>2]+m(v[o+16>>2],12)|0,C),Oe(C),E(v[o+12>>2]),k=v[o+16>>2]+1|0,v[o+16>>2]=k,k>>>0<y>>>0;);V=c=V-48|0,n=wi(ht(40),v[n+8>>2],v[n+4>>2]),n=jt(ht(12),n),e[28+(u=c+4|0)|0]=0,v[u+4>>2]=n,v[u+20>>2]=0,v[u+24>>2]=1065353216,v[u+12>>2]=0,v[u+16>>2]=0,v[u+8>>2]=10376,v[u>>2]=10712,Ut(n=u+32|0),v[u+24>>2]=1065353216,r=v[r+8>>2],V=t=V-288|0,wf(n,8610),v[u+12>>2]=0,a=Nf(Jt(32),r),k=zr(ht(232)),(y=Un(a,3838))&&(wf(k+176|0,qt(y,4828,0)),wf(k+164|0,qt(y,5369,0)),i=v[k+172>>2],f=v[k+168>>2],(51!=l[0|i]|(0|f)<3|46!=l[i+1|0]||56!=l[i+2|0])&&(n=Ze(t+49|0,0,238),e[t+48|0]=l[10736],r=v[2683],v[t+40>>2]=v[2682],v[t+44>>2]=r,r=v[2681],v[t+32>>2]=v[2680],v[t+36>>2]=r,Me(Me(n,i,f)+f|0,10752,38),He(u,0,n=$t(t+20|0,t+32|0,0),r=$t(t+8|0,8610,0)),Oe(r),Oe(n)),D=k,Z=Dt(y,1391,w(0)),p[D+148>>2]=Z,D=k,Z=Dt(y,1217,w(0)),p[D+152>>2]=Z,D=k,Z=Dt(y,4734,w(0)),p[D+156>>2]=Z,D=k,Z=Dt(y,2183,w(0)),p[D+160>>2]=Z,D=k,Z=Dt(y,2613,w(30)),p[D+204>>2]=Z,wf(k+220|0,qt(y,3816,0)),wf(k+208|0,qt(y,2886,0))),n=Un(a,2774),r=v[n+12>>2],v[t+32>>2]=0,In(k+16|0,r,t+32|0);r:{n:{if(r=v[n+4>>2])for(i=0;;){if(g=0,(f=qt(r,1834,0))&&(g=yu(k,n=$t(t+32|0,f,0)),Oe(n),!g))break n;n=af(n=ht(64),i,f=$t(t+32|0,qt(r,5773,0),0),g),Oe(f),D=n,Z=w(Dt(r,4696,w(0))*p[u+24>>2]),p[D+24>>2]=Z,D=n,Z=w(Dt(r,1391,w(0))*p[u+24>>2]),p[D+28>>2]=Z,D=n,Z=w(Dt(r,1217,w(0))*p[u+24>>2]),p[D+32>>2]=Z,D=n,Z=Dt(r,4056,w(0)),p[D+36>>2]=Z,D=n,Z=Dt(r,6719,w(1)),p[D+40>>2]=Z,D=n,Z=Dt(r,6626,w(1)),p[D+44>>2]=Z,D=n,Z=Dt(r,6693,w(0)),p[D+48>>2]=Z,D=n,Z=Dt(r,6587,w(0)),p[D+52>>2]=Z,y=qt(r,4463,4581),v[n+56>>2]=0;f:if(vi(y,4581)){if(f=1,vi(y,4266)&&(f=2,vi(y,4012)&&(f=3,vi(y,5849)))){if(vi(y,4035))break f;f=4}v[n+56>>2]=f}if(D=n,Y=Hf(r),e[D+60|0]=Y,v[v[k+28>>2]+(i<<2)>>2]=n,i=i+1|0,!(r=v[r>>2]))break}f:{if((f=Un(a,2439))&&(Oa(n=k+32|0,v[f+12>>2]),r=v[f+12>>2],v[t+32>>2]=0,In(n,r,t+32|0),r=v[f+4>>2]))for(i=0;;){if(y=yu(k,n=$t(t+32|0,f=qt(r,5343,0),0)),Oe(n),!y)break f;n=nf(n=ht(84),i,f=$t(t+32|0,qt(r,5773,0),0),y),Oe(f),(y=qt(r,3520,0))&&(D=f=sa(n),Z=$f(y,0),p[D+4>>2]=Z,D=f,Z=$f(y,1),p[D+8>>2]=Z,D=f,Z=$f(y,2),p[D+12>>2]=Z,D=f,Z=$f(y,3),p[D+16>>2]=Z),(y=qt(r,4607,0))&&(D=f=Ca(n),Z=$f(y,0),p[D+4>>2]=Z,D=f,Z=$f(y,1),p[D+8>>2]=Z,I=$f(y,2),v[f+16>>2]=1065353216,p[f+12>>2]=I,Sa(n)),(f=Un(r,1841))&&(sf(n+68|0,f=$t(t+32|0,v[f+16>>2],0)),Oe(f));i:if(f=Un(r,6108)){if(y=1,vi(f=v[f+16>>2],4974)&&(y=2,vi(f,1204))){if(vi(f,4456))break i;y=3}v[n+80>>2]=y}if(v[v[k+44>>2]+(i<<2)>>2]=n,i=i+1|0,!(r=v[r>>2]))break}if((f=Un(a,4612))&&(Oa(n=k+100|0,v[f+12>>2]),r=v[f+12>>2],v[t+32>>2]=0,In(n,r,t+32|0),f=v[f+4>>2]))for(C=0;;){T=ui(n=ht(60),r=$t(i=t+32|0,qt(f,5773,0),0)),Oe(r),Ta(T,Bt(f,3633,0)),Pa(T,Hf(f)),n=T+24|0,y=Un(f,2774),Oa(n,v[y+12>>2]),r=v[y+12>>2],v[t+32>>2]=0,In(n,r,i);i:if(r=v[y+4>>2]){for(g=0;;){if(i=yu(k,y=$t(t+32|0,v[r+16>>2],0)),v[(n=g<<2)+v[T+36>>2]>>2]=i,Oe(y),v[n+v[T+36>>2]>>2]){if(g=g+1|0,r=v[r>>2])continue;break i}break}La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8574,0),r=$t(t+20|0,v[r+16>>2],0)),Oe(r),Oe(n);break r}if(D=T,Y=yu(k,r=$t(t+32|0,i=qt(f,2246,0),0)),v[D+40>>2]=Y,Oe(r),!v[T+40>>2]){k&&La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8479,0),r=$t(t+20|0,i,0)),Oe(r),Oe(n);break r}if(D=T,Z=Dt(f,1231,w(1)),p[D+52>>2]=Z,D=T,Z=w(Dt(f,2590,w(0))*p[u+24>>2]),p[D+56>>2]=Z,D=T,Y=Bt(f,4961,1)?1:-1,v[D+44>>2]=Y,D=T,Y=!!(0|Bt(f,2581,0)),e[D+48|0]=Y,D=T,Y=!!(0|Bt(f,4833,0)),e[D+49|0]=Y,D=T,Y=!!(0|Bt(f,4494,0)),e[D+50|0]=Y,v[v[k+112>>2]+(C<<2)>>2]=T,C=C+1|0,!(f=v[f>>2]))break}if((f=Un(a,4463))&&(Oa(n=k+116|0,v[f+12>>2]),r=v[f+12>>2],v[t+32>>2]=0,In(n,r,t+32|0),f=v[f+4>>2]))for(C=0;;){T=vt(n=ht(88),r=$t(i=t+32|0,qt(f,5773,0),0)),Oe(r),Ta(T,Bt(f,3633,0)),Pa(T,Hf(f)),n=T+24|0,y=Un(f,2774),Oa(n,v[y+12>>2]),r=v[y+12>>2],v[t+32>>2]=0,In(n,r,i);i:if(g=v[y+4>>2]){for(i=0;;){if(n=yu(k,y=$t(t+32|0,v[g+16>>2],0)),v[(r=i<<2)+v[T+36>>2]>>2]=n,Oe(y),v[r+v[T+36>>2]>>2]){if(i=i+1|0,g=v[g>>2])continue;break i}break}k&&La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8525,0),r=$t(t+20|0,v[g+16>>2],0)),Oe(r),Oe(n);break r}if(D=T,Y=yu(k,r=$t(t+32|0,i=qt(f,2246,0),0)),v[D+40>>2]=Y,Oe(r),!v[T+40>>2]){k&&La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8479,0),r=$t(t+20|0,i,0)),Oe(r),Oe(n);break r}if(D=T,Y=!!(0|Bt(f,4588,0)),e[D+85|0]=Y,D=T,Y=!!(0|Bt(f,4999,0)),e[D+84|0]=Y,D=T,Z=Dt(f,4056,w(0)),p[D+60>>2]=Z,D=T,Z=w(Dt(f,1391,w(0))*p[u+24>>2]),p[D+64>>2]=Z,D=T,Z=w(Dt(f,1217,w(0))*p[u+24>>2]),p[D+68>>2]=Z,D=T,Z=Dt(f,6719,w(0)),p[D+72>>2]=Z,D=T,Z=Dt(f,6626,w(0)),p[D+76>>2]=Z,D=T,Z=Dt(f,6587,w(0)),p[D+80>>2]=Z,D=T,Z=Dt(f,1269,w(1)),p[D+44>>2]=Z,D=T,Z=Dt(f,1279,w(1)),p[D+48>>2]=Z,D=T,Z=Dt(f,1292,w(1)),p[D+52>>2]=Z,D=T,Z=Dt(f,1260,w(1)),p[D+56>>2]=Z,v[v[k+128>>2]+(C<<2)>>2]=T,C=C+1|0,!(f=v[f>>2]))break}if((f=Un(a,4749))&&(Oa(n=k+132|0,v[f+12>>2]),r=v[f+12>>2],v[t+32>>2]=0,In(n,r,t+32|0),f=v[f+4>>2]))for(C=0;;){r=lt(r=ht(76),n=$t(y=t+32|0,qt(f,5773,0),0)),Oe(n),Ta(r,Bt(f,3633,0)),Pa(r,Hf(f)),i=r+24|0,T=Un(f,2774),Oa(i,v[T+12>>2]),n=v[T+12>>2],v[t+32>>2]=0,In(i,n,y);i:if(g=v[T+4>>2]){for(i=0;;){if(y=yu(k,T=$t(t+32|0,v[g+16>>2],0)),v[(n=i<<2)+v[r+36>>2]>>2]=y,Oe(T),v[n+v[r+36>>2]>>2]){if(i=i+1|0,g=v[g>>2])continue;break i}break}k&&La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8552,0),r=$t(t+20|0,v[g+16>>2],0)),Oe(r),Oe(n);break r}if(D=r,Y=$u(k,n=$t(t+32|0,i=qt(f,2246,0),0)),v[D+40>>2]=Y,Oe(n),!v[r+40>>2]){k&&La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8180,0),r=$t(t+20|0,i,0)),Oe(r),Oe(n);break r}i:{if(vi(n=qt(f,5915,2101),6266)){if(vi(n,2101))break i;n=1}else n=0;v[r+44>>2]=n}i=0;i:{if(vi(n=qt(f,5996,4696),4696)&&(i=1,vi(n,6266))){if(vi(n,2101))break i;i=2}v[r+48>>2]=i}i=0;i:{if(vi(n=qt(f,6027,2093),2093)&&(i=1,vi(n,4438))){if(vi(n,5857))break i;i=2}v[r+52>>2]=i}if(D=r,Z=Dt(f,4056,w(0)),p[D+56>>2]=Z,I=Dt(f,3948,w(0)),p[r+60>>2]=I,v[r+44>>2]||(p[r+60>>2]=I*p[u+24>>2]),I=Dt(f,4921,w(0)),p[r+64>>2]=I,d[r+48>>2]<=1&&(p[r+64>>2]=I*p[u+24>>2]),D=r,Z=Dt(f,1269,w(1)),p[D+68>>2]=Z,D=r,Z=Dt(f,1279,w(1)),p[D+72>>2]=Z,v[v[k+144>>2]+(C<<2)>>2]=r,C=C+1|0,!(f=v[f>>2]))break}if(z=u+8|0,(f=Un(a,2685))&&(Oa(n=k+48|0,v[f+12>>2]),r=v[f+12>>2],v[t+32>>2]=0,In(n,r,t+32|0),F=v[f+4>>2]))for(;;){if(g=qt(F,5773,8610),l[0|g]||(g=v[F+28>>2]),O=Of(n=ht(68),r=$t(t+32|0,g,0)),Oe(r),(r=Un(F,2774))&&(r=v[r+4>>2]))for(f=O+36|0;;){if(i=yu(k,n=$t(t+32|0,v[r+16>>2],0)),Oe(n),v[t+8>>2]=i,!i){k&&La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8503,0),r=$t(t+20|0,v[r+16>>2],0)),Oe(r),Oe(n);break r}if(_n(f,t+8|0),!(r=v[r>>2]))break}if((r=Un(F,4612))&&(r=v[r+4>>2]))for(f=O+52|0;;){if(i=ku(k,n=$t(t+32|0,v[r+16>>2],0)),Oe(n),!i){k&&La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8320,0),r=$t(t+20|0,v[r+16>>2],0)),Oe(r),Oe(n);break r}if(v[t+32>>2]=i,_n(f,t+32|0),!(r=v[r>>2]))break}if((r=Un(F,4463))&&(r=v[r+4>>2]))for(f=O+52|0;;){if(i=su(k,n=$t(t+32|0,v[r+16>>2],0)),Oe(n),!i){k&&La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8221,0),r=$t(t+20|0,v[r+16>>2],0)),Oe(r),Oe(n);break r}if(v[t+32>>2]=i,_n(f,t+32|0),!(r=v[r>>2]))break}if((r=Un(F,4749))&&(r=v[r+4>>2]))for(f=O+52|0;;){if(i=vu(k,n=$t(t+32|0,v[r+16>>2],0)),Oe(n),!i){k&&La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8259,0),r=$t(t+20|0,v[r+16>>2],0)),Oe(r),Oe(n);break r}if(v[t+32>>2]=i,_n(f,t+32|0),!(r=v[r>>2]))break}if(v[v[k+60>>2]+(R<<2)>>2]=O,vi(g,2155)||(v[k+64>>2]=O),r=Un(F,2546),A=v[(r||F)+4>>2])for(;;){if(C=$u(k,r=$t(t+32|0,v[A+28>>2],0)),Oe(r),r=v[A+4>>2])for(;;){n=qt(r,5773,T=v[r+28>>2]),y=qt(r,4749,n);i:{e:{t:{u:{o:{a:{c:{if(vi(f=qt(r,5206,4327),4327)){if(!vi(f,4781))break c;if(!vi(f,4775))break c;if(!vi(f,1219))break a;if(!vi(f,4749))break o;if(!vi(f,4884))break t;if(!vi(f,1662))break u;k&&La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8119,0),r=$t(t+20|0,f,0)),Oe(r),Oe(n);break r}if(i=v[u+4>>2],f=$t(t+32|0,n,0),n=$t(t+20|0,y,0),g=0|La[v[v[i>>2]+12>>2]](i,O,f,n),Oe(n),Oe(f),!g)break i;wf(g+116|0,y),D=g,Z=w(Dt(r,1391,w(0))*p[u+24>>2]),p[D+32>>2]=Z,D=g,Z=w(Dt(r,1217,w(0))*p[u+24>>2]),p[D+36>>2]=Z,D=g,Z=Dt(r,6719,w(1)),p[D+44>>2]=Z,D=g,Z=Dt(r,6626,w(1)),p[D+48>>2]=Z,D=g,Z=Dt(r,4056,w(0)),p[D+40>>2]=Z,D=g,Z=w(Dt(r,4734,w(32))*p[u+24>>2]),p[D+52>>2]=Z,D=g,Z=w(Dt(r,2183,w(32))*p[u+24>>2]),p[D+56>>2]=Z,(n=qt(r,3520,0))&&(I=$f(n,0),D=$a(g),Z=I,p[D+4>>2]=Z,I=$f(n,1),D=$a(g),Z=I,p[D+8>>2]=Z,I=$f(n,2),D=$a(g),Z=I,p[D+12>>2]=Z,I=$f(n,3),D=$a(g),Z=I,p[D+16>>2]=Z),Br(g),n=v[u+4>>2],La[v[v[n>>2]+36>>2]](n,g);break e}if(i=v[u+4>>2],f=$t(t+32|0,n,0),n=$t(t+20|0,y,0),g=0|La[v[v[i>>2]+16>>2]](i,O,f,n),Oe(n),Oe(f),!g)break i;if(wf(g+168|0,y),(n=qt(r,3520,0))&&(I=$f(n,0),D=Fa(g),Z=I,p[D+4>>2]=Z,I=$f(n,1),D=Fa(g),Z=I,p[D+8>>2]=Z,I=$f(n,2),D=Fa(g),Z=I,p[D+12>>2]=Z,I=$f(n,3),D=Fa(g),Z=I,p[D+16>>2]=Z),D=g,Z=w(Dt(r,4734,w(32))*p[u+24>>2]),p[D+196>>2]=Z,D=g,Z=w(Dt(r,2183,w(32))*p[u+24>>2]),p[D+200>>2]=Z,!(f=Un(r,1834))){if(f=g+136|0,i=Un(r,2831),Ea(f,v[i+12>>2]),n=v[i+12>>2],s[t+32>>1]=0,$n(f,n,t+32|0),i=v[i+4>>2])for(n=v[g+148>>2],f=0;s[n+(f<<1)>>1]=v[i+20>>2],f=f+1|0,i=v[i>>2];);if(f=g+120|0,n=Un(r,2415),Oa(f,y=v[n+12>>2]),v[t+32>>2]=0,An(f,y,t+32|0),i=v[n+4>>2])for(n=v[g+132>>2],f=0;p[n+(f<<2)>>2]=p[i+24>>2],f=f+1|0,i=v[i>>2];);if(yr(u,r,g,y),hr(g),D=g,Y=Bt(r,4535,0),v[D+224>>2]=Y,(i=Un(r,2871))&&(Ea(f=g+152|0,v[i+12>>2]),n=v[i+12>>2],s[t+32>>1]=0,$n(f,n,t+32|0),i=v[i+4>>2]))for(n=v[g+164>>2],f=0;s[n+(f<<1)>>1]=v[i+20>>2],f=f+1|0,i=v[i>>2];);n=v[u+4>>2],La[v[v[n>>2]+36>>2]](n,g);break e}n=Bt(r,4502,1),n=Je(h=ht(40),g,y=$t(t+32|0,qt(r,4381,0),0),$=v[C+4>>2],f=$t(i=t+20|0,v[f+16>>2],0),!!(0|n)),Oe(f),Oe(y),v[t+20>>2]=n,_n(z,i);break e}f=v[u+4>>2],n=$t(t+32|0,n,0),g=0|La[v[v[f>>2]+20>>2]](f,O,n),Oe(n),yr(u,r,g,Bt(r,1622,0)<<1),n=v[u+4>>2],La[v[v[n>>2]+36>>2]](n,g);break e}if(f=v[u+4>>2],n=$t(i=t+32|0,n,0),g=0|La[v[v[f>>2]+24>>2]](f,O,n),Oe(n),D=g,Y=!!(0|Bt(r,6272,0)),e[D+80|0]=Y,D=g,Y=!!(0|Bt(r,6311,1)),e[D+81|0]=Y,yr(u,r,g,(n=Bt(r,1622,0))<<1),Oa(f=g- -64|0,n=(0|n)/3|0),v[t+32>>2]=0,An(f,n,i),i=v[Un(r,2722)+4>>2])for(n=v[g+76>>2],f=0;p[n+(f<<2)>>2]=p[i+24>>2]*p[u+24>>2],f=f+1|0,i=v[i>>2];);n=v[u+4>>2],La[v[v[n>>2]+36>>2]](n,g);break e}f=v[u+4>>2],n=$t(t+32|0,n,0),g=0|La[v[v[f>>2]+28>>2]](f,O,n),Oe(n),D=g,Z=w(Dt(r,1391,w(0))*p[u+24>>2]),p[D+20>>2]=Z,D=g,Z=w(Dt(r,1217,w(0))*p[u+24>>2]),p[D+24>>2]=Z,D=g,Z=Dt(r,4056,w(0)),p[D+28>>2]=Z,n=v[u+4>>2],La[v[v[n>>2]+36>>2]](n,g);break e}f=v[u+4>>2],n=$t(t+32|0,n,0),g=0|La[v[v[f>>2]+32>>2]](f,O,n),Oe(n),(n=qt(r,6135,0))&&(D=g,Y=$u(k,n=$t(t+32|0,n,0)),v[D+64>>2]=Y,Oe(n)),yr(u,r,g,Bt(r,1622,0)<<1),n=v[u+4>>2],La[v[v[n>>2]+36>>2]](n,g)}Ht(O,f=v[C+4>>2],n=$t(t+32|0,T,0),g),Oe(n)}if(!(r=v[r>>2]))break}if(!(A=v[A>>2]))break}if(R=R+1|0,!(F=v[F>>2]))break}if((0|(n=v[u+12>>2]))>0)for(g=0;i=v[v[u+20>>2]+(g<<2)>>2],(r=v[i+12>>2]?wu(k,i+8|0):v[k+64>>2])&&(f=Mt(r,v[i+20>>2],i+24|0))&&(r=v[i+4>>2],v[r+56>>2]=l[i+36|0]?f:r,Zn(r,f),hr(v[i+4>>2]),r=v[u+4>>2],La[v[v[r>>2]+36>>2]](r,v[i+4>>2])),(0|n)!=(0|(g=g+1|0)););if(jn(z),v[u+12>>2]=0,(f=Un(a,2529))&&(Oa(n=k+68|0,v[f+12>>2]),r=v[f+12>>2],v[t+32>>2]=0,In(n,r,t+32|0),r=v[f+4>>2]))for(i=0;f=me(f=ht(56),n=$t(t+32|0,v[r+28>>2],0)),Oe(n),D=f,Y=Bt(r,1793,0),v[D+16>>2]=Y,D=f,Z=Dt(r,2351,w(0)),p[D+20>>2]=Z,wf(f+24|0,qt(r,4860,0)),wf(f+36|0,n=qt(r,3816,0)),n&&(D=f,Z=Dt(r,5681,w(1)),p[D+48>>2]=Z,D=f,Z=Dt(r,6100,w(0)),p[D+52>>2]=Z),v[v[k+80>>2]+(i<<2)>>2]=f,i=i+1|0,r=v[r>>2];);if((f=Un(a,2641))&&(Oa(n=k+84|0,v[f+12>>2]),r=v[f+12>>2],g=0,v[t+32>>2]=0,In(n,r,t+32|0),r=v[f+4>>2]))for(;;){I=w(0),V=h=V-80|0,v[h+76>>2]=0,v[h+68>>2]=0,v[h+72>>2]=0,v[h+64>>2]=1048,x=Un(r,2774),n=Un(r,2439),i=r,B=Un(r,4612),O=Un(r,4463),(L=Un(r,4749))||(L=Un(i,2741)),z=Un(i,4502),J=Un(i,3639),N=Un(i,2529),J||(J=Un(i,3629));i:{e:{if(n&&($=v[n+4>>2]))for(C=v[2437],T=v[2341],y=v[2784];;){if(M=mu(k,r=$t(h+48|0,v[$+28>>2],0)),Oe(r),-1==(0|M)){jn(h- -64|0),F=0,He(u,0,n=$t(h+48|0,8204,0),r=$t(h+32|0,v[$+28>>2],0)),Oe(r),Oe(n);break i}if(A=v[$+4>>2])for(;;){if(vi(r=v[A+28>>2],1841))if(vi(r,3520)){if(vi(r,3535))break e;if(_=Zf(ht(40),v[A+12>>2]),v[_+36>>2]=M,f=0,r=v[A+4>>2])for(;S=qt(r,2177,0),n=qt(r,4607,0),_f(_,f,Dt(r,5688,w(0)),$f(S,0),$f(S,1),$f(S,2),$f(S,3),$f(n,0),$f(n,1),$f(n,2)),Yf(r,_,f),f=f+1|0,r=v[r>>2];);v[h+48>>2]=_,_n(h- -64|0,h+48|0),n=m(y,v[A+12>>2]-1|0),r=_+32|0}else{if(S=ci(ht(40),v[A+12>>2]),v[S+20>>2]=M,f=0,r=v[A+4>>2])for(;n=qt(r,3520,0),Ji(S,f,Dt(r,5688,w(0)),$f(n,0),$f(n,1),$f(n,2),$f(n,3)),Yf(r,S,f),f=f+1|0,r=v[r>>2];);v[h+48>>2]=S,_n(h- -64|0,h+48|0),n=m(T,v[A+12>>2]-1|0),r=S+36|0}else{if(S=Hn(ht(40),v[A+12>>2]),v[S+4>>2]=M,F=0,r=v[A+4>>2])for(;f=h+48|0,n=Un(r,5773),n=$t(f,n=(0|C)!=v[n+8>>2]?v[n+16>>2]:8610,0),Ye(S,F,Dt(r,5688,w(0)),n),Oe(n),F=F+1|0,r=v[r>>2];);v[h+48>>2]=S,_n(h- -64|0,h+48|0),n=v[A+12>>2]-1|0,r=S+20|0}if(I=I>(P=p[v[r>>2]+(n<<2)>>2])?I:P,!(A=v[A>>2]))break}if(!($=v[$>>2]))break}t:{if(x&&(A=v[x+4>>2]))for(f=v[2767];;){if(T=Fu(k,r=$t(h+48|0,v[A+28>>2],0)),Oe(r),-1==(0|T)){jn(h- -64|0),F=0,He(u,0,n=$t(h+48|0,8594,0),r=$t(h+32|0,v[A+28>>2],0)),Oe(r),Oe(n);break i}if($=v[A+4>>2])for(;;){if(vi(r=v[$+28>>2],5081)){if(n=vi(r,5843),y=vi(r,5106),(r=vi(r,3761))&&!(!n|!y))break t;if(P=p[u+24>>2],n?y?(q=w(0),n=0,r||(n=Cu(ht(40),v[$+12>>2]))):(q=w(0),n=qf(ht(40),v[$+12>>2])):(q=w(1),n=Ou(ht(40),v[$+12>>2])),v[n+36>>2]=T,r=v[$+4>>2])for(P=y?w(1):P,F=0;ze(n,F,Dt(r,5688,w(0)),w(P*Dt(r,1391,q)),w(P*Dt(r,1217,q))),Yf(r,n,F),F=F+1|0,r=v[r>>2];);v[h+48>>2]=n,_n(h- -64|0,h+48|0),F=m(f,v[$+12>>2]-1|0),r=n+32|0}else{if(n=ki(ht(40),v[$+12>>2]),v[n+20>>2]=T,F=0,r=v[$+4>>2])for(;it(n,F,Dt(r,5688,w(0)),Dt(r,5820,w(0))),Yf(r,n,F),F=F+1|0,r=v[r>>2];);v[h+48>>2]=n,_n(h- -64|0,h+48|0),F=(v[$+12>>2]<<1)-2|0,r=n+36|0}if(I=I>(P=p[v[r>>2]+(F<<2)>>2])?I:P,!($=v[$>>2]))break}if(!(A=v[A>>2]))break}if(B&&(A=v[B+4>>2]))for(T=v[2429];;){y=ku(k,r=$t(h+48|0,v[A+28>>2],0)),Oe(r),C=No(ht(40),v[A+12>>2]);u:if(f=v[k+104>>2])for(n=v[k+112>>2],r=0;;){if((0|y)==v[n+(r<<2)>>2]){v[C+36>>2]=r;break u}if((0|f)==(0|(r=r+1|0)))break}if(F=0,r=v[A+4>>2])for(;si(C,F,Dt(r,5688,w(0)),Dt(r,1231,w(1)),w(Dt(r,2590,w(0))*p[u+24>>2]),Bt(r,4961,1)?1:-1,!!(0|Bt(r,2581,0)),!!(0|Bt(r,4833,0))),Yf(r,C,F),F=F+1|0,r=v[r>>2];);if(v[h+48>>2]=C,_n(h- -64|0,h+48|0),I=I>(P=p[v[C+32>>2]+(m(T,v[A+12>>2]-1|0)<<2)>>2])?I:P,!(A=v[A>>2]))break}if(O&&(A=v[O+4>>2]))for(T=v[2759];;){y=su(k,r=$t(h+48|0,v[A+28>>2],0)),Oe(r),C=xo(ht(40),v[A+12>>2]);u:if(f=v[k+120>>2])for(n=v[k+128>>2],r=0;;){if((0|y)==v[n+(r<<2)>>2]){v[C+36>>2]=r;break u}if((0|f)==(0|(r=r+1|0)))break}if(F=0,r=v[A+4>>2])for(;Bi(C,F,Dt(r,5688,w(0)),Dt(r,1269,w(1)),Dt(r,1279,w(1)),Dt(r,1292,w(1)),Dt(r,1260,w(1))),Yf(r,C,F),F=F+1|0,r=v[r>>2];);if(v[h+48>>2]=C,_n(h- -64|0,h+48|0),I=I>(P=p[v[C+32>>2]+(m(T,v[A+12>>2]-1|0)<<2)>>2])?I:P,!(A=v[A>>2]))break}u:{if(L&&(R=v[L+4>>2]))for(y=v[2501],f=v[2493];;){if(C=vu(k,r=$t(h+48|0,v[R+28>>2],0)),Oe(r),!C)break u;o:{if(n=v[k+136>>2])for(r=v[k+144>>2],A=0;;){if((0|C)==v[r+(A<<2)>>2])break o;if((0|n)==(0|(A=A+1|0)))break}A=0}if($=v[R+4>>2])for(;;){if(vi(T=v[$+28>>2],3948)&&vi(T,4921)){if(!vi(T,1231)){if(n=Vo(ht(40),v[$+12>>2]),v[n+36>>2]=A,F=0,r=v[$+4>>2])for(;ze(n,F,Dt(r,5688,w(0)),Dt(r,1269,w(1)),Dt(r,1279,w(1))),Yf(r,n,F),F=F+1|0,r=v[r>>2];);v[h+48>>2]=n,_n(h- -64|0,h+48|0),I=I>(P=p[v[n+32>>2]+(m(f,v[$+12>>2]-1|0)<<2)>>2])?I:P}}else{if(r=vi(T,4921),O=ht(40),n=v[$+12>>2],r?(bi(O,n),n=!v[C+44>>2]):(Kt(O,n),n=d[C+48>>2]<2),P=p[u+24>>2],v[O+36>>2]=A,r=v[$+4>>2])for(P=n?P:w(1),F=0;ft(O,F,Dt(r,5688,w(0)),w(P*Dt(r,T,w(0)))),Yf(r,O,F),F=F+1|0,r=v[r>>2];);v[h+48>>2]=O,_n(h- -64|0,h+48|0),I=I>(P=p[v[O+32>>2]+(m(y,v[$+12>>2]-1|0)<<2)>>2])?I:P}if(!($=v[$>>2]))break}if(!(R=v[R>>2]))break}if(z&&(L=v[z+4>>2]))for(;;){if(B=wu(k,r=$t(h+48|0,v[L+28>>2],0)),Oe(r),M=v[L+4>>2])for(;;){if(S=mu(k,r=$t(h+48|0,v[M+28>>2],0)),Oe(r),f=v[M+4>>2])for(;;){if(K=Mt(B,S,r=$t(h+48|0,v[f+28>>2],0)),Oe(r),K){if(r=v[K+40>>2],_=(x=v[K+24>>2])?(r>>>0)/3<<1:r,$=Gr(ht(60),v[f+12>>2]),v[$+56>>2]=K,v[$+20>>2]=S,A=v[f+4>>2])for(O=K+36|0,R=0,z=!!(0|x)|(0|_)<=0;;){r=Un(A,2901),v[h+60>>2]=0,v[h+52>>2]=0,v[h+56>>2]=0,v[h+48>>2]=8796;o:if(r){F=Bt(A,2200,0),v[h+32>>2]=0,An(h+48|0,_,h+32|0),r=v[r+4>>2];a:if(p[u+24>>2]!=w(1)){if(r)for(n=v[h+60>>2];p[n+(F<<2)>>2]=p[r+24>>2]*p[u+24>>2],F=F+1|0,r=v[r>>2];);}else{if(!r)break a;for(n=v[h+60>>2];p[n+(F<<2)>>2]=p[r+24>>2],F=F+1|0,r=v[r>>2];);}if(!z)for(C=v[K+48>>2],r=0,T=v[h+60>>2];p[(n=T+(y=r<<2)|0)>>2]=p[y+C>>2]+p[n>>2],(0|_)!=(0|(r=r+1|0)););}else{if(x){v[h+32>>2]=0,An(h+48|0,_,h+32|0);break o}te(h+48|0,O)}if(r=h+48|0,et($,R,Dt(A,5688,w(0)),r),Yf(A,$,R),R=R+1|0,Lo(r),!(A=v[A>>2]))break}v[h+48>>2]=$,_n(h- -64|0,h+48|0),I=I>(P=p[(v[$+36>>2]+(v[f+12>>2]<<2)|0)-4>>2])?I:P}else jn(h- -64|0);if(!(f=v[f>>2]))break}if(!(M=v[M>>2]))break}if(!(L=v[L>>2]))break}if(J){if(C=Mr(ht(36),v[J+12>>2]),A=v[J+4>>2])for(R=0;;){if(v[h+60>>2]=0,v[h+52>>2]=0,v[h+56>>2]=0,v[h+48>>2]=8764,f=Un(A,2573)){v[h+44>>2]=0,v[h+36>>2]=0,v[h+40>>2]=0,v[h+32>>2]=8764,(0|(r=v[k+36>>2]))!=(0|(n=v[f+12>>2]))?(n=r-n|0,v[h+40>>2]=n,r=v[5316],D=h,Y=0|La[v[v[r>>2]+16>>2]](r,0,n<<2,8610,105),v[D+44>>2]=Y,r=v[f+12>>2],n=v[k+36>>2]):n=r,v[h+20>>2]=0,Tn(h+32|0,n-r|0,h+20|0),(r=v[k+36>>2])>>>0>d[h+56>>2]&&(v[h+56>>2]=r,n=v[5316],D=h,Y=0|La[v[v[n>>2]+16>>2]](n,v[h+60>>2],r<<2,8610,105),v[D+60>>2]=Y,r=v[k+36>>2]),v[h+20>>2]=0,Tn(h+48|0,r,h+20|0),(0|(r=v[k+36>>2]))>0&&Ze(v[h+60>>2],255,r<<2),r=0,F=0;o:if($=v[f+4>>2]){for(;;){if(n=mu(k,f=$t(h+20|0,qt($,1545,0),0)),Oe(f),-1!=(0|n)){if((0|r)!=(0|n)){for(f=v[h+44>>2];v[f+(F<<2)>>2]=r,F=F+1|0,(0|n)!=(0|(r=r+1|0)););r=n}if(n=Bt($,2200,0),v[v[h+60>>2]+(r+n<<2)>>2]=r,r=r+1|0,$=v[$>>2])continue;break o}break}jn(h- -64|0),F=0,He(u,0,n=$t(h+20|0,8204,0),r=$t(h+8|0,qt($,1545,0),0)),Oe(r),Oe(n),Io(h+32|0),Io(h+48|0);break i}if((f=v[k+36>>2])>>>0>r>>>0)for(n=v[h+44>>2];v[n+(F<<2)>>2]=r,F=F+1|0,(0|f)!=(0|(r=r+1|0)););if((0|f)>0)for(T=v[h+44>>2],y=v[h+60>>2];-1==v[(n=y+((r=f-1|0)<<2)|0)>>2]&&(F=F-1|0,v[n>>2]=v[T+(F<<2)>>2]),n=f>>>0>1,f=r,n;);Io(h+32|0)}if(r=h+48|0,_e(C,R,Dt(A,5688,w(0)),r),R=R+1|0,Io(r),!(A=v[A>>2]))break}v[h+48>>2]=C,_n(h- -64|0,h+48|0),I=I>(P=p[(v[C+16>>2]+(v[J+12>>2]<<2)|0)-4>>2])?I:P}o:{if(N){if(f=Af(ht(36),v[N+12>>2]),r=v[N+4>>2])for($=0;;){if(y=Au(k,n=$t(h+48|0,qt(r,5773,0),0)),Oe(n),!y)break o;if(D=n=le(ht(40),Dt(r,5688,w(0)),y),Y=Bt(r,1793,v[y+16>>2]),v[D+12>>2]=Y,D=n,Z=Dt(r,2351,p[y+20>>2]),p[D+16>>2]=Z,wf(n+20|0,qt(r,4860,v[y+32>>2])),v[y+40>>2]&&(D=n,Z=Dt(r,5681,w(1)),p[D+32>>2]=Z,D=n,Z=Dt(r,6100,w(0)),p[D+36>>2]=Z),nt(f,$,n),$=$+1|0,!(r=v[r>>2]))break}v[h+48>>2]=f,_n(h- -64|0,h+48|0),I=I>(P=p[(v[f+16>>2]+(v[N+12>>2]<<2)|0)-4>>2])?I:P}F=Zr(n=ht(48),r=$t(h+48|0,v[i+28>>2],0),h- -64|0,I),Oe(r);break i}jn(h- -64|0),F=0,He(u,0,n=$t(h+48|0,8351,0),r=$t(h+32|0,qt(r,5773,0),0)),Oe(r),Oe(n);break i}jn(h- -64|0),F=0,He(u,0,n=$t(h+48|0,8292,0),r=$t(h+32|0,v[R+28>>2],0)),Oe(r),Oe(n);break i}jn(h- -64|0),F=0,He(u,0,n=$t(h+48|0,8145,0),r=$t(h+32|0,v[$+28>>2],0)),Oe(r),Oe(n);break i}jn(h- -64|0),F=0,He(u,0,n=$t(h+48|0,8057,0),r=$t(h+32|0,v[A+28>>2],0)),Oe(r),Oe(n)}if(Po(h- -64|0),V=h+80|0,F&&(v[v[k+96>>2]+(g<<2)>>2]=F,g=g+1|0),!(r=v[i>>2]))break}ar(Ef(a));break r}La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8433,0),r=$t(t+20|0,f,0)),Oe(r),Oe(n);break r}La[v[v[k>>2]+4>>2]](k),k=0,He(u,a,n=$t(t+32|0,8455,0),r=$t(t+20|0,f,0)),Oe(r),Oe(n)}return V=t+288|0,r=k,v[u+36>>2]&&(v[c>>2]=v[u+40>>2],H(20976,17196,0|c)),er(r,f=o+48|0,n=o+32|0),Mi(u),V=c+48|0,yf(n),yf(f),V=o- -64|0,0|r},function(r,n,f,i,e){var t;return r|=0,n|=0,i|=0,e|=0,V=t=V-32|0,f=Sf(t+20|0,4+(f|=0)|0,v[f>>2],0),v[t+12>>2]=e,v[t+16>>2]=i,r=0|La[0|r](n,f,t+16|0,t+12|0),E(v[t+12>>2]),E(v[t+16>>2]),Oe(f),V=t+32|0,0|r},function(r,n,f,i){r|=0,n|=0,f|=0,i|=0;var t,o,a,c,b=0,s=0,h=0,y=0,g=0,F=0,A=w(0),T=0,$=0,I=0,C=0,P=0,O=w(0),R=0,S=0,L=0,M=0,_=0,z=0,x=0,J=0,K=0,B=0,N=0,q=0,D=0,Z=0,Y=0,X=0,rr=w(0),nr=0,fr=0,ir=0,tr=0,ur=0;if(V=o=V+-64|0,h=v[f>>2],s=0|j(4696),b=0|U(0|h,0|s),E(0|s),c=+G(0|b,21697,0|(s=o+48|0)),W(v[o+48>>2]),E(0|b),v[o+60>>2]=0,v[o+52>>2]=0,v[o+56>>2]=0,v[o+48>>2]=9244,v[o+44>>2]=0,v[o+36>>2]=0,v[o+40>>2]=0,v[o+32>>2]=9244,un(h=s,s=c<4294967296&c>=0?~~c>>>0:0,b=$t(o+20|0,8610,0)),Oe(b),un(o+32|0,s,b=$t(o+20|0,8610,0)),Oe(b),v[o+16>>2]=0,s)for(;ne(h=o+12|0,f,$=o+16|0),Ii(b=o+20|0,h),sf(v[o+60>>2]+m(v[o+16>>2],12)|0,b),Oe(b),E(v[o+12>>2]),ne(h,i,$),Ii(b,h),sf(v[o+44>>2]+m(v[o+16>>2],12)|0,b),Oe(b),E(v[o+12>>2]),b=v[o+16>>2]+1|0,v[o+16>>2]=b,b>>>0<s>>>0;);V=a=V-48|0,n=wi(ht(40),v[n+8>>2],v[n+4>>2]),tr=$=a+4|0,ur=jt(ht(12),n),v[tr+4>>2]=ur,v[$+20>>2]=0,v[$+12>>2]=0,v[$+16>>2]=0,v[$+8>>2]=10376,v[$>>2]=10344,Ut($+24|0),e[$+40|0]=0,v[$+36>>2]=1065353216,v[$+36>>2]=1065353216,n=v[5487],V=t=V-16|0,b=ht(12),v[b+8>>2]=r+n,v[b+4>>2]=n,v[b>>2]=10360,v[$+12>>2]=0,Hi((f=zr(ht(232)))+176|0,Si(b)),Hi(r=f+164|0,Si(b)),r=se(n=$t(t+4|0,6910,0),r),Oe(n);r:{n:{if(!r){if(r=v[b+4>>2],v[b+4>>2]=r+1,n=l[0|r],v[b+4>>2]=r+2,i=l[r+1|0],v[b+4>>2]=r+3,s=l[r+2|0],v[b+4>>2]=r+4,v[f+148>>2]=l[r+3|0]|(s|i<<8|n<<16)<<8,v[b+4>>2]=r+5,n=l[r+4|0],v[b+4>>2]=r+6,i=l[r+5|0],v[b+4>>2]=r+7,s=l[r+6|0],v[b+4>>2]=r+8,v[f+152>>2]=l[r+7|0]|(s|i<<8|n<<16)<<8,v[b+4>>2]=r+9,n=l[r+8|0],v[b+4>>2]=r+10,i=l[r+9|0],v[b+4>>2]=r+11,s=l[r+10|0],v[b+4>>2]=r+12,v[f+156>>2]=l[r+11|0]|(s|i<<8|n<<16)<<8,v[b+4>>2]=r+13,n=l[r+12|0],v[b+4>>2]=r+14,i=l[r+13|0],v[b+4>>2]=r+15,s=l[r+14|0],v[b+4>>2]=r+16,v[f+160>>2]=l[r+15|0]|(s|i<<8|n<<16)<<8,v[b+4>>2]=r+17,(h=l[r+16|0])&&(v[b+4>>2]=r+18,n=l[r+17|0],v[b+4>>2]=r+19,i=l[r+18|0],v[b+4>>2]=r+20,s=l[r+19|0],v[b+4>>2]=r+21,v[f+204>>2]=l[r+20|0]|(s|i<<8|n<<16)<<8,Hi(f+208|0,Si(b)),Hi(f+220|0,Si(b))),(0|(r=zn(b,1)))>0)for(n=f+188|0;tr=t,ur=Si(b),v[tr+4>>2]=ur,_n(n,t+4|0),(0|r)!=(0|(y=y+1|0)););if(s=zn(b,1),v[t+4>>2]=0,In(f+16|0,s,t+4|0),(0|s)>0)for(r=0;i=Si(b),r?(n=zn(b,1),n=v[v[f+28>>2]+(n<<2)>>2]):n=0,i=af(g=ht(64),r,y=$t(t+4|0,i,1),n),Oe(y),n=v[b+4>>2],v[b+4>>2]=n+1,y=l[0|n],v[b+4>>2]=n+2,g=l[n+1|0],v[b+4>>2]=n+3,F=l[n+2|0],v[b+4>>2]=n+4,v[i+36>>2]=l[n+3|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+5,y=l[n+4|0],v[b+4>>2]=n+6,g=l[n+5|0],v[b+4>>2]=n+7,F=l[n+6|0],v[b+4>>2]=n+8,A=p[$+36>>2],p[i+28>>2]=A*(u(2,l[n+7|0]|(F|g<<8|y<<16)<<8),k()),v[b+4>>2]=n+9,y=l[n+8|0],v[b+4>>2]=n+10,g=l[n+9|0],v[b+4>>2]=n+11,F=l[n+10|0],v[b+4>>2]=n+12,p[i+32>>2]=A*(u(2,l[n+11|0]|(F|g<<8|y<<16)<<8),k()),v[b+4>>2]=n+13,y=l[n+12|0],v[b+4>>2]=n+14,g=l[n+13|0],v[b+4>>2]=n+15,F=l[n+14|0],v[b+4>>2]=n+16,v[i+40>>2]=l[n+15|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+17,y=l[n+16|0],v[b+4>>2]=n+18,g=l[n+17|0],v[b+4>>2]=n+19,F=l[n+18|0],v[b+4>>2]=n+20,v[i+44>>2]=l[n+19|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+21,y=l[n+20|0],v[b+4>>2]=n+22,g=l[n+21|0],v[b+4>>2]=n+23,F=l[n+22|0],v[b+4>>2]=n+24,v[i+48>>2]=l[n+23|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+25,y=l[n+24|0],v[b+4>>2]=n+26,g=l[n+25|0],v[b+4>>2]=n+27,F=l[n+26|0],v[b+4>>2]=n+28,v[i+52>>2]=l[n+27|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+29,y=l[n+28|0],v[b+4>>2]=n+30,g=l[n+29|0],v[b+4>>2]=n+31,F=l[n+30|0],v[b+4>>2]=n+32,p[i+24>>2]=A*(u(2,l[n+31|0]|(F|g<<8|y<<16)<<8),k()),tr=i,ur=zn(b,1),v[tr+56>>2]=ur,n=v[b+4>>2],v[b+4>>2]=n+1,e[i+60|0]=0!=l[0|n],h&&(v[b+4>>2]=n+5),v[v[f+28>>2]+(r<<2)>>2]=i,(0|s)!=(0|(r=r+1|0)););if(y=zn(b,1),v[t+4>>2]=0,In(f+32|0,y,t+4|0),(0|y)>0)for(r=0;n=Si(b),i=zn(b,1),i=v[v[f+28>>2]+(i<<2)>>2],i=nf(s=ht(84),r,n=$t(t+4|0,n,1),i),Oe(n),s=sa(i),n=v[b+4>>2],v[b+4>>2]=n+1,p[s+4>>2]=w(l[0|n])/w(255),v[b+4>>2]=n+2,p[s+8>>2]=w(l[n+1|0])/w(255),v[b+4>>2]=n+3,p[s+12>>2]=w(l[n+2|0])/w(255),v[b+4>>2]=n+4,p[s+16>>2]=w(l[n+3|0])/w(255),v[b+4>>2]=n+5,s=l[n+4|0],v[b+4>>2]=n+6,g=l[n+5|0],v[b+4>>2]=n+7,F=l[n+6|0],v[b+4>>2]=n+8,255==(s&g)&255==(F&l[n+7|0])||(n=Ca(i),v[n+16>>2]=1065353216,p[n+12>>2]=w(F>>>0)/w(255),p[n+8>>2]=w(g>>>0)/w(255),p[n+4>>2]=w(s>>>0)/w(255),mi(n),Sa(i)),wf(s=i+68|0,n=(n=zn(b,1))?v[(v[f+200>>2]+(n<<2)|0)-4>>2]:0),tr=i,ur=zn(b,1),v[tr+80>>2]=ur,v[v[f+44>>2]+(r<<2)>>2]=i,(0|y)!=(0|(r=r+1|0)););if(s=zn(b,1),v[t+4>>2]=0,In(f+100|0,s,t+4|0),(0|s)>0)for(r=0;;){if(n=Si(b),i=ui(i=ht(60),n=$t(y=t+4|0,n,1)),Oe(n),Ta(i,zn(b,1)),n=v[b+4>>2],v[b+4>>2]=n+1,Pa(i,0!=l[0|n]),n=zn(b,1),v[t+4>>2]=0,In(i+24|0,n,y),(0|n)>0)for(y=0;g=zn(b,1),v[v[i+36>>2]+(y<<2)>>2]=v[v[f+28>>2]+(g<<2)>>2],(0|n)!=(0|(y=y+1|0)););if(n=zn(b,1),v[i+40>>2]=v[v[f+28>>2]+(n<<2)>>2],n=v[b+4>>2],v[b+4>>2]=n+1,y=l[0|n],v[b+4>>2]=n+2,g=l[n+1|0],v[b+4>>2]=n+3,F=l[n+2|0],v[b+4>>2]=n+4,v[i+52>>2]=l[n+3|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+5,y=l[n+4|0],v[b+4>>2]=n+6,g=l[n+5|0],v[b+4>>2]=n+7,F=l[n+6|0],v[b+4>>2]=n+8,p[i+56>>2]=p[$+36>>2]*(u(2,l[n+7|0]|(F|g<<8|y<<16)<<8),k()),v[b+4>>2]=n+9,v[i+44>>2]=e[n+8|0],v[b+4>>2]=n+10,e[i+48|0]=0!=l[n+9|0],v[b+4>>2]=n+11,e[i+49|0]=0!=l[n+10|0],v[b+4>>2]=n+12,e[i+50|0]=0!=l[n+11|0],v[v[f+112>>2]+(r<<2)>>2]=i,(0|s)==(0|(r=r+1|0)))break}if(s=zn(b,1),v[t+4>>2]=0,In(f+116|0,s,t+4|0),(0|s)>0)for(r=0;;){if(n=Si(b),i=vt(i=ht(88),n=$t(y=t+4|0,n,1)),Oe(n),Ta(i,zn(b,1)),n=v[b+4>>2],v[b+4>>2]=n+1,Pa(i,0!=l[0|n]),n=zn(b,1),v[t+4>>2]=0,In(i+24|0,n,y),(0|n)>0)for(y=0;g=zn(b,1),v[v[i+36>>2]+(y<<2)>>2]=v[v[f+28>>2]+(g<<2)>>2],(0|n)!=(0|(y=y+1|0)););if(n=zn(b,1),v[i+40>>2]=v[v[f+28>>2]+(n<<2)>>2],n=v[b+4>>2],v[b+4>>2]=n+1,e[i+85|0]=0!=l[0|n],v[b+4>>2]=n+2,e[i+84|0]=0!=l[n+1|0],v[b+4>>2]=n+3,y=l[n+2|0],v[b+4>>2]=n+4,g=l[n+3|0],v[b+4>>2]=n+5,F=l[n+4|0],v[b+4>>2]=n+6,v[i+60>>2]=l[n+5|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+7,y=l[n+6|0],v[b+4>>2]=n+8,g=l[n+7|0],v[b+4>>2]=n+9,F=l[n+8|0],v[b+4>>2]=n+10,A=p[$+36>>2],p[i+64>>2]=A*(u(2,l[n+9|0]|(F|g<<8|y<<16)<<8),k()),v[b+4>>2]=n+11,y=l[n+10|0],v[b+4>>2]=n+12,g=l[n+11|0],v[b+4>>2]=n+13,F=l[n+12|0],v[b+4>>2]=n+14,p[i+68>>2]=A*(u(2,l[n+13|0]|(F|g<<8|y<<16)<<8),k()),v[b+4>>2]=n+15,y=l[n+14|0],v[b+4>>2]=n+16,g=l[n+15|0],v[b+4>>2]=n+17,F=l[n+16|0],v[b+4>>2]=n+18,v[i+72>>2]=l[n+17|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+19,y=l[n+18|0],v[b+4>>2]=n+20,g=l[n+19|0],v[b+4>>2]=n+21,F=l[n+20|0],v[b+4>>2]=n+22,v[i+76>>2]=l[n+21|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+23,y=l[n+22|0],v[b+4>>2]=n+24,g=l[n+23|0],v[b+4>>2]=n+25,F=l[n+24|0],v[b+4>>2]=n+26,v[i+80>>2]=l[n+25|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+27,y=l[n+26|0],v[b+4>>2]=n+28,g=l[n+27|0],v[b+4>>2]=n+29,F=l[n+28|0],v[b+4>>2]=n+30,v[i+44>>2]=l[n+29|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+31,y=l[n+30|0],v[b+4>>2]=n+32,g=l[n+31|0],v[b+4>>2]=n+33,F=l[n+32|0],v[b+4>>2]=n+34,v[i+48>>2]=l[n+33|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+35,y=l[n+34|0],v[b+4>>2]=n+36,g=l[n+35|0],v[b+4>>2]=n+37,F=l[n+36|0],v[b+4>>2]=n+38,v[i+52>>2]=l[n+37|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+39,y=l[n+38|0],v[b+4>>2]=n+40,g=l[n+39|0],v[b+4>>2]=n+41,F=l[n+40|0],v[b+4>>2]=n+42,v[i+56>>2]=l[n+41|0]|(F|g<<8|y<<16)<<8,v[v[f+128>>2]+(r<<2)>>2]=i,(0|s)==(0|(r=r+1|0)))break}if(s=zn(b,1),v[t+4>>2]=0,In(f+132|0,s,t+4|0),(0|s)>0)for(r=0;;){if(n=Si(b),i=lt(i=ht(76),n=$t(y=t+4|0,n,1)),Oe(n),Ta(i,zn(b,1)),n=v[b+4>>2],v[b+4>>2]=n+1,Pa(i,0!=l[0|n]),n=zn(b,1),v[t+4>>2]=0,In(i+24|0,n,y),(0|n)>0)for(y=0;g=zn(b,1),v[v[i+36>>2]+(y<<2)>>2]=v[v[f+28>>2]+(g<<2)>>2],(0|n)!=(0|(y=y+1|0)););if(n=zn(b,1),v[i+40>>2]=v[v[f+44>>2]+(n<<2)>>2],tr=i,ur=zn(b,1),v[tr+44>>2]=ur,tr=i,ur=zn(b,1),v[tr+48>>2]=ur,tr=i,ur=zn(b,1),v[tr+52>>2]=ur,n=v[b+4>>2],v[b+4>>2]=n+1,y=l[0|n],v[b+4>>2]=n+2,g=l[n+1|0],v[b+4>>2]=n+3,F=l[n+2|0],v[b+4>>2]=n+4,v[i+56>>2]=l[n+3|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+5,y=l[n+4|0],v[b+4>>2]=n+6,g=l[n+5|0],v[b+4>>2]=n+7,F=l[n+6|0],v[b+4>>2]=n+8,y=l[n+7|0]|(F|g<<8|y<<16)<<8,v[i+60>>2]=y,v[i+44>>2]||(p[i+60>>2]=p[$+36>>2]*(u(2,y),k())),v[b+4>>2]=n+9,y=l[n+8|0],v[b+4>>2]=n+10,g=l[n+9|0],v[b+4>>2]=n+11,F=l[n+10|0],v[b+4>>2]=n+12,y=l[n+11|0]|(F|g<<8|y<<16)<<8,v[i+64>>2]=y,d[i+48>>2]<=1&&(p[i+64>>2]=p[$+36>>2]*(u(2,y),k())),v[b+4>>2]=n+13,y=l[n+12|0],v[b+4>>2]=n+14,g=l[n+13|0],v[b+4>>2]=n+15,F=l[n+14|0],v[b+4>>2]=n+16,v[i+68>>2]=l[n+15|0]|(F|g<<8|y<<16)<<8,v[b+4>>2]=n+17,y=l[n+16|0],v[b+4>>2]=n+18,g=l[n+17|0],v[b+4>>2]=n+19,F=l[n+18|0],v[b+4>>2]=n+20,v[i+72>>2]=l[n+19|0]|(F|g<<8|y<<16)<<8,v[v[f+144>>2]+(r<<2)>>2]=i,(0|s)==(0|(r=r+1|0)))break}if(r=Q($,b,1,f,!!(0|h)),v[t>>2]=r,r&&(v[f+64>>2]=r,_n(f+48|0,t)),r=zn(b,1))for(n=f+48|0,y=0;;){if(i=Q($,b,0,f,!!(0|h)),v[t+4>>2]=i,!i){if(La[v[v[b>>2]+4>>2]](b),!f)break n;La[v[v[f>>2]+4>>2]](f);break n}if(_n(n,t+4|0),(0|r)==(0|(y=y+1|0)))break}if((0|(s=v[$+12>>2]))>0)for(y=0;;){if(n=v[v[$+20>>2]+(y<<2)>>2],!(r=v[n+12>>2]?wu(f,n+8|0):v[f+64>>2])){La[v[v[b>>2]+4>>2]](b),f&&La[v[v[f>>2]+4>>2]](f),lf($,8392,v[n+16>>2]);break n}if(!(r=Mt(r,v[n+20>>2],n+24|0))){La[v[v[b>>2]+4>>2]](b),f&&La[v[v[f>>2]+4>>2]](f),lf($,8409,v[n+32>>2]);break n}if(i=v[n+4>>2],v[i+56>>2]=l[n+36|0]?r:i,Zn(i,r),hr(v[n+4>>2]),r=v[$+4>>2],La[v[v[r>>2]+36>>2]](r,v[n+4>>2]),(0|s)==(0|(y=y+1|0)))break}if(jn($+8|0),v[$+12>>2]=0,s=zn(b,1),v[t+4>>2]=0,In(f+68|0,s,t+4|0),(0|s)>0)for(r=0;n=(n=zn(b,1))?v[(v[f+200>>2]+(n<<2)|0)-4>>2]:0,i=me(i=ht(56),n=$t(t+4|0,n,0)),Oe(n),tr=i,ur=zn(b,0),v[tr+16>>2]=ur,n=v[b+4>>2],v[b+4>>2]=n+1,h=l[0|n],v[b+4>>2]=n+2,y=l[n+1|0],v[b+4>>2]=n+3,g=l[n+2|0],v[b+4>>2]=n+4,v[i+20>>2]=l[n+3|0]|(g|y<<8|h<<16)<<8,Hi(i+24|0,Si(b)),Hi(i+36|0,Si(b)),v[i+40>>2]&&(n=v[b+4>>2],v[b+4>>2]=n+1,h=l[0|n],v[b+4>>2]=n+2,y=l[n+1|0],v[b+4>>2]=n+3,g=l[n+2|0],v[b+4>>2]=n+4,v[i+48>>2]=l[n+3|0]|(g|y<<8|h<<16)<<8,v[b+4>>2]=n+5,h=l[n+4|0],v[b+4>>2]=n+6,y=l[n+5|0],v[b+4>>2]=n+7,g=l[n+6|0],v[b+4>>2]=n+8,v[i+52>>2]=l[n+7|0]|(g|y<<8|h<<16)<<8),v[v[f+80>>2]+(r<<2)>>2]=i,(0|s)!=(0|(r=r+1|0)););if(nr=zn(b,1),v[t+4>>2]=0,In(f+84|0,nr,t+4|0),(0|nr)>0)for(y=0;;){fr=$t(t+4|0,Si(b),1),g=0,O=w(0),V=h=V+-64|0,v[h+60>>2]=0,v[h+52>>2]=0,v[h+56>>2]=0,v[h+48>>2]=1048,rr=p[$+36>>2];f:{i:if(!((0|(C=zn(b,1)))<=0))for(K=v[2341],L=v[2784],T=1;;){if(I=zn(b,1),F=0,(0|(z=zn(b,1)))>0)for(;;){r=v[b+4>>2],v[b+4>>2]=r+1,r=l[0|r],i=zn(b,1);e:{t:{u:{o:{switch(0|r){case 0:if(r=Hn(ht(40),i),v[r+4>>2]=I,n=0,(0|i)<=0)break t;for(;s=v[b+4>>2],v[b+4>>2]=s+1,R=l[0|s],v[b+4>>2]=s+2,S=l[s+1|0],v[b+4>>2]=s+3,P=l[s+2|0],v[b+4>>2]=s+4,u(2,l[s+3|0]|(P|S<<8|R<<16)<<8),Ye(r,n,A=k(),s=$t(P=h+32|0,s=(s=zn(b,1))?v[(v[f+200>>2]+(s<<2)|0)-4>>2]:0,0)),Oe(s),(0|i)!=(0|(n=n+1|0)););break t;case 1:if(s=ci(ht(40),i),v[s+20>>2]=I,R=i-1|0,n=0,(0|i)<=0)break u;for(;r=v[b+4>>2],v[b+4>>2]=r+1,S=l[0|r],v[b+4>>2]=r+2,P=l[r+1|0],v[b+4>>2]=r+3,_=l[r+2|0],v[b+4>>2]=r+4,M=l[r+3|0],v[b+4>>2]=r+5,J=l[r+4|0],v[b+4>>2]=r+6,x=l[r+5|0],v[b+4>>2]=r+7,B=l[r+6|0],v[b+4>>2]=r+8,Ji(s,n,(u(2,M|(_|P<<8|S<<16)<<8),k()),w(w(J>>>0)/w(255)),w(w(x>>>0)/w(255)),w(w(B>>>0)/w(255)),w(w(l[r+7|0])/w(255))),(0|n)<(0|R)&&_r(b,n,s),(0|i)!=(0|(n=n+1|0)););break u;case 2:if(s=Zf(ht(40),i),v[s+36>>2]=I,R=i-1|0,n=0,(0|i)<=0)break o;for(;r=v[b+4>>2],v[b+4>>2]=r+1,S=l[0|r],v[b+4>>2]=r+2,P=l[r+1|0],v[b+4>>2]=r+3,_=l[r+2|0],v[b+4>>2]=r+4,M=l[r+3|0],v[b+4>>2]=r+5,J=l[r+4|0],v[b+4>>2]=r+6,x=l[r+5|0],v[b+4>>2]=r+7,B=l[r+6|0],v[b+4>>2]=r+8,q=l[r+7|0],v[b+4>>2]=r+10,D=l[r+9|0],v[b+4>>2]=r+11,N=l[r+10|0],v[b+4>>2]=r+12,_f(s,n,(u(2,M|(_|P<<8|S<<16)<<8),k()),w(w(J>>>0)/w(255)),w(w(x>>>0)/w(255)),w(w(B>>>0)/w(255)),w(w(q>>>0)/w(255)),w(w(D>>>0)/w(255)),w(w(N>>>0)/w(255)),w(w(l[r+11|0])/w(255))),(0|n)<(0|R)&&_r(b,n,s),(0|i)!=(0|(n=n+1|0)););break o}if(jn(h+48|0),lf($,8057,v[v[v[f+44>>2]+(I<<2)>>2]+16>>2]),r=0,1&T)break f;break i}v[h+32>>2]=s,n=s+32|0,_n(h+48|0,h+32|0),r=m(R,L);break e}v[h+32>>2]=s,n=s+36|0,_n(h+48|0,h+32|0),r=m(R,K);break e}v[h+32>>2]=r,n=r+20|0,_n(h+48|0,h+32|0),r=i-1|0}if(O=(A=p[v[n>>2]+(r<<2)>>2])<O?O:A,(0|z)==(0|(F=F+1|0)))break}if(T=(0|C)>(0|(g=g+1|0)),(0|g)==(0|C))break}i:if(!((0|(C=zn(b,1)))<=0))for(g=0,K=v[2767],T=1;;){if(I=zn(b,1),F=0,(0|(L=zn(b,1)))>0)for(;;){r=v[b+4>>2],v[b+4>>2]=r+1,r=l[0|r],s=zn(b,1);e:{t:{u:{o:{a:switch(0|r){case 0:if(i=ki(ht(40),s),v[i+20>>2]=I,(0|s)>0)for(R=s-1|0,n=0;r=v[b+4>>2],v[b+4>>2]=r+1,z=l[0|r],v[b+4>>2]=r+2,S=l[r+1|0],v[b+4>>2]=r+3,P=l[r+2|0],v[b+4>>2]=r+4,_=l[r+3|0],v[b+4>>2]=r+5,M=l[r+4|0],v[b+4>>2]=r+6,J=l[r+5|0],v[b+4>>2]=r+7,x=l[r+6|0],v[b+4>>2]=r+8,it(i,n,(u(2,_|(P|S<<8|z<<16)<<8),k()),(u(2,l[r+7|0]|(x|J<<8|M<<16)<<8),k())),(0|n)<(0|R)&&_r(b,n,i),(0|s)!=(0|(n=n+1|0)););v[h+32>>2]=i,n=(s<<1)-2|0,_n(h+48|0,h+32|0),r=i+36|0;break e;case 2:A=w(1),i=Ou(ht(40),s);break o;case 3:A=w(1),i=Cu(ht(40),s);break o;case 1:break a;default:break u}A=rr,i=qf(ht(40),s)}if(v[i+36>>2]=I,R=s-1|0,n=0,(0|s)<=0)break t;for(;r=v[b+4>>2],v[b+4>>2]=r+1,z=l[0|r],v[b+4>>2]=r+2,S=l[r+1|0],v[b+4>>2]=r+3,P=l[r+2|0],v[b+4>>2]=r+4,_=l[r+3|0],v[b+4>>2]=r+5,M=l[r+4|0],v[b+4>>2]=r+6,J=l[r+5|0],v[b+4>>2]=r+7,x=l[r+6|0],v[b+4>>2]=r+8,B=l[r+7|0],v[b+4>>2]=r+9,q=l[r+8|0],v[b+4>>2]=r+10,D=l[r+9|0],v[b+4>>2]=r+11,N=l[r+10|0],v[b+4>>2]=r+12,ze(i,n,(u(2,_|(P|S<<8|z<<16)<<8),k()),w(A*(u(2,B|(x|J<<8|M<<16)<<8),k())),w(A*(u(2,l[r+11|0]|(N|D<<8|q<<16)<<8),k()))),(0|n)<(0|R)&&_r(b,n,i),(0|s)!=(0|(n=n+1|0)););break t}if(jn(h+48|0),lf($,8145,v[v[v[f+28>>2]+(I<<2)>>2]+16>>2]),r=0,1&T)break f;break i}n=m(R,K),v[h+32>>2]=i,_n(h+48|0,h+32|0),r=i+32|0}if(O=(A=p[v[r>>2]+(n<<2)>>2])<O?O:A,(0|L)==(0|(F=F+1|0)))break}if(T=(0|C)>(0|(g=g+1|0)),(0|g)==(0|C))break}if((0|(T=zn(b,1)))>0)for(I=v[2429],F=0;;){if(r=zn(b,1),i=zn(b,1),s=No(ht(40),i),v[s+36>>2]=r,g=i-1|0,n=0,(0|i)>0)for(;r=v[b+4>>2],v[b+4>>2]=r+1,C=l[0|r],v[b+4>>2]=r+2,R=l[r+1|0],v[b+4>>2]=r+3,K=l[r+2|0],v[b+4>>2]=r+4,L=l[r+3|0],v[b+4>>2]=r+5,z=l[r+4|0],v[b+4>>2]=r+6,S=l[r+5|0],v[b+4>>2]=r+7,P=l[r+6|0],v[b+4>>2]=r+8,_=l[r+7|0],v[b+4>>2]=r+9,M=l[r+8|0],v[b+4>>2]=r+10,J=l[r+9|0],v[b+4>>2]=r+11,x=l[r+10|0],v[b+4>>2]=r+12,A=p[$+36>>2],B=l[r+11|0],v[b+4>>2]=r+13,q=e[r+12|0],v[b+4>>2]=r+14,D=l[r+13|0],v[b+4>>2]=r+15,si(s,n,(u(2,L|(K|R<<8|C<<16)<<8),k()),(u(2,_|(P|S<<8|z<<16)<<8),k()),w(A*(u(2,B|(x|J<<8|M<<16)<<8),k())),q,!!(0|D),0!=l[r+14|0]),(0|n)<(0|g)&&_r(b,n,s),(0|i)!=(0|(n=n+1|0)););if(v[h+32>>2]=s,_n(h+48|0,h+32|0),O=(A=p[v[s+32>>2]+(m(g,I)<<2)>>2])<O?O:A,(0|T)==(0|(F=F+1|0)))break}if((0|(T=zn(b,1)))>0)for(I=v[2759],i=0;;){if(r=zn(b,1),s=zn(b,1),g=xo(ht(40),s),v[g+36>>2]=r,F=s-1|0,n=0,(0|s)>0)for(;r=v[b+4>>2],v[b+4>>2]=r+1,C=l[0|r],v[b+4>>2]=r+2,R=l[r+1|0],v[b+4>>2]=r+3,K=l[r+2|0],v[b+4>>2]=r+4,L=l[r+3|0],v[b+4>>2]=r+5,z=l[r+4|0],v[b+4>>2]=r+6,S=l[r+5|0],v[b+4>>2]=r+7,P=l[r+6|0],v[b+4>>2]=r+8,_=l[r+7|0],v[b+4>>2]=r+9,M=l[r+8|0],v[b+4>>2]=r+10,J=l[r+9|0],v[b+4>>2]=r+11,x=l[r+10|0],v[b+4>>2]=r+12,B=l[r+11|0],v[b+4>>2]=r+13,q=l[r+12|0],v[b+4>>2]=r+14,D=l[r+13|0],v[b+4>>2]=r+15,N=l[r+14|0],v[b+4>>2]=r+16,Z=l[r+15|0],v[b+4>>2]=r+17,Y=l[r+16|0],v[b+4>>2]=r+18,X=l[r+17|0],v[b+4>>2]=r+19,ir=l[r+18|0],v[b+4>>2]=r+20,Bi(g,n,(u(2,L|(K|R<<8|C<<16)<<8),k()),(u(2,_|(P|S<<8|z<<16)<<8),k()),(u(2,B|(x|J<<8|M<<16)<<8),k()),(u(2,Z|(N|D<<8|q<<16)<<8),k()),(u(2,l[r+19|0]|(X<<8|Y<<16|ir)<<8),k())),(0|n)<(0|F)&&_r(b,n,g),(0|s)!=(0|(n=n+1|0)););if(v[h+32>>2]=g,_n(h+48|0,h+32|0),O=(A=p[v[g+32>>2]+(m(F,I)<<2)>>2])<O?O:A,(0|T)==(0|(i=i+1|0)))break}if((0|(R=zn(b,1)))>0)for(T=0,K=v[2501],L=v[2493];;){if(g=zn(b,1),I=v[v[f+144>>2]+(g<<2)>>2],(0|(z=zn(b,1)))>0)for(F=0;;){r=v[b+4>>2],v[b+4>>2]=r+1,r=e[0|r],i=zn(b,1);i:{e:switch(0|r){case 0:case 1:if(s=ht(40),1!=(0|r)?(bi(s,i),r=!v[I+44>>2]):(Kt(s,i),r=d[I+48>>2]<2),v[s+36>>2]=g,C=i-1|0,n=0,(0|i)>0)for(A=r?rr:w(1);r=v[b+4>>2],v[b+4>>2]=r+1,S=l[0|r],v[b+4>>2]=r+2,P=l[r+1|0],v[b+4>>2]=r+3,_=l[r+2|0],v[b+4>>2]=r+4,M=l[r+3|0],v[b+4>>2]=r+5,J=l[r+4|0],v[b+4>>2]=r+6,x=l[r+5|0],v[b+4>>2]=r+7,B=l[r+6|0],v[b+4>>2]=r+8,ft(s,n,(u(2,M|(_|P<<8|S<<16)<<8),k()),w(A*(u(2,l[r+7|0]|(B|x<<8|J<<16)<<8),k()))),(0|n)<(0|C)&&_r(b,n,s),(0|i)!=(0|(n=n+1|0)););v[h+32>>2]=s,_n(h+48|0,h+32|0),O=(A=p[v[s+32>>2]+(m(C,K)<<2)>>2])<O?O:A;break i;case 2:break e;default:break i}if(s=Vo(ht(40),i),v[s+36>>2]=g,C=i-1|0,n=0,(0|i)>0)for(;r=v[b+4>>2],v[b+4>>2]=r+1,S=l[0|r],v[b+4>>2]=r+2,P=l[r+1|0],v[b+4>>2]=r+3,_=l[r+2|0],v[b+4>>2]=r+4,M=l[r+3|0],v[b+4>>2]=r+5,J=l[r+4|0],v[b+4>>2]=r+6,x=l[r+5|0],v[b+4>>2]=r+7,B=l[r+6|0],v[b+4>>2]=r+8,q=l[r+7|0],v[b+4>>2]=r+9,D=l[r+8|0],v[b+4>>2]=r+10,N=l[r+9|0],v[b+4>>2]=r+11,Z=l[r+10|0],v[b+4>>2]=r+12,ze(s,n,(u(2,M|(_|P<<8|S<<16)<<8),k()),(u(2,q|(B|x<<8|J<<16)<<8),k()),(u(2,l[r+11|0]|(Z|N<<8|D<<16)<<8),k())),(0|n)<(0|C)&&_r(b,n,s),(0|i)!=(0|(n=n+1|0)););v[h+32>>2]=s,_n(h+48|0,h+32|0),O=(A=p[v[s+32>>2]+(m(C,L)<<2)>>2])<O?O:A}if((0|z)==(0|(F=F+1|0)))break}if((0|R)==(0|(T=T+1|0)))break}i:if(!((0|(S=zn(b,1)))<=0))for(P=1,i=0;;){if(r=zn(b,1),J=v[v[f+60>>2]+(r<<2)>>2],T=0,(0|(x=zn(b,1)))>0)for(;;){if(F=0,_=zn(b,1),(0|(B=zn(b,1)))>0)for(;;){if(r=0,(n=zn(b,1))&&(r=v[(v[f+200>>2]+(n<<2)|0)-4>>2]),I=Mt(J,_,n=$t(h+32|0,r,0)),Oe(n),!I){if(jn(h+48|0),lf($,8369,r),r=0,P)break f;break i}if(r=v[I+40>>2],R=(M=v[I+24>>2])?(r>>>0)/3<<1:r,K=zn(b,1),C=Gr(ht(60),K),v[C+56>>2]=I,v[C+20>>2]=_,K)for(q=I+36|0,D=R<<2,z=K-1|0,g=0;;){r=v[b+4>>2],v[b+4>>2]=r+1,n=l[0|r],v[b+4>>2]=r+2,s=l[r+1|0],v[b+4>>2]=r+3,L=l[r+2|0],v[b+4>>2]=r+4,r=l[r+3|0],v[h+44>>2]=0,v[h+36>>2]=0,v[h+40>>2]=0,v[h+32>>2]=8796,u(2,r|(L|s<<8|n<<16)<<8),A=k();e:if(r=zn(b,1)){v[h+16>>2]=0,An(h+32|0,R,h+16|0),L=r+(n=zn(b,1))|0;t:if(rr==w(1)){if(!(n>>>0>=L>>>0))for(r=v[b+4>>2],N=v[h+44>>2];v[b+4>>2]=r+1,Z=l[0|r],v[b+4>>2]=r+2,Y=l[r+1|0],v[b+4>>2]=r+3,X=l[r+2|0],s=r+4|0,v[b+4>>2]=s,v[N+(n<<2)>>2]=l[r+3|0]|(X|Y<<8|Z<<16)<<8,r=s,(0|L)!=(0|(n=n+1|0)););}else{if(n>>>0>=L>>>0)break t;for(r=v[b+4>>2],N=v[h+44>>2];v[b+4>>2]=r+1,Z=l[0|r],v[b+4>>2]=r+2,Y=l[r+1|0],v[b+4>>2]=r+3,X=l[r+2|0],s=r+4|0,v[b+4>>2]=s,p[N+(n<<2)>>2]=rr*(u(2,l[r+3|0]|(X|Y<<8|Z<<16)<<8),k()),r=s,(0|L)!=(0|(n=n+1|0)););}if(!M&&(n=v[h+36>>2]))for(s=v[I+48>>2],r=0,L=v[h+44>>2];p[(Z=(N=r<<2)+L|0)>>2]=p[s+N>>2]+p[Z>>2],(0|n)!=(0|(r=r+1|0)););}else{if(M){if(v[h+16>>2]=0,An(h+32|0,R,h+16|0),!R)break e;Ze(v[h+44>>2],0,D);break e}v[h+36>>2]=0,te(h+32|0,q)}if(et(C,g,A,h+32|0),g>>>0<z>>>0&&_r(b,g,C),Lo(h+32|0),(0|K)==(0|(g=g+1|0)))break}else z=-1;if(v[h+32>>2]=C,_n(h+48|0,h+32|0),O=(A=p[v[C+36>>2]+(z<<2)>>2])<O?O:A,(0|B)==(0|(F=F+1|0)))break}if((0|x)==(0|(T=T+1|0)))break}if(P=(0|S)>(0|(i=i+1|0)),(0|i)==(0|S))break}if(T=zn(b,1)){for(I=Mr(ht(36),T),R=(i=v[f+36>>2])<<2,g=0;;){if(r=v[b+4>>2],v[b+4>>2]=r+1,K=l[0|r],v[b+4>>2]=r+2,L=l[r+1|0],v[b+4>>2]=r+3,z=l[r+2|0],v[b+4>>2]=r+4,S=l[r+3|0],C=zn(b,1),v[h+44>>2]=0,v[h+36>>2]=0,v[h+40>>2]=0,v[h+32>>2]=8764,v[h+16>>2]=0,Tn(h+32|0,i,h+16|0),(P=(0|i)<=0)||Ze(v[h+44>>2],255,R),r=0,v[h+28>>2]=0,v[h+20>>2]=0,v[h+24>>2]=0,v[h+16>>2]=8764,v[h+12>>2]=0,Tn(h+16|0,i-C|0,h+12|0),n=0,C)for(F=0,_=v[h+44>>2],M=v[h+28>>2];;){if((0|(s=zn(b,1)))!=(0|r)){for(;v[M+(n<<2)>>2]=r,n=n+1|0,(0|s)!=(0|(r=r+1|0)););r=s}if(tr=_+(zn(b,1)+r<<2)|0,ur=r,v[tr>>2]=ur,r=r+1|0,(0|C)==(0|(F=F+1|0)))break}if(r>>>0<i>>>0)for(s=v[h+28>>2];v[s+(n<<2)>>2]=r,n=n+1|0,(0|i)!=(0|(r=r+1|0)););if(!P)for(F=v[h+28>>2],C=v[h+44>>2],r=i;-1==v[(P=C+((s=r-1|0)<<2)|0)>>2]&&(n=n-1|0,v[P>>2]=v[F+(n<<2)>>2]),P=r>>>0>1,r=s,P;);if(r=h+32|0,_e(I,g,(u(2,S|(z|L<<8|K<<16)<<8),k()),r),Io(h+16|0),Io(r),(0|T)==(0|(g=g+1|0)))break}v[h+32>>2]=I,_n(h+48|0,h+32|0),O=(A=p[(v[I+16>>2]+(T<<2)|0)-4>>2])<O?O:A}if((0|(i=zn(b,1)))>0){for(s=Af(ht(36),i),g=0;r=v[b+4>>2],v[b+4>>2]=r+1,n=l[0|r],v[b+4>>2]=r+2,T=l[r+1|0],v[b+4>>2]=r+3,I=l[r+2|0],v[b+4>>2]=r+4,r=l[r+3|0],F=zn(b,1),F=v[v[f+80>>2]+(F<<2)>>2],tr=n=le(ht(40),(u(2,r|(I|T<<8|n<<16)<<8),k()),F),ur=zn(b,0),v[tr+12>>2]=ur,r=v[b+4>>2],v[b+4>>2]=r+1,T=l[0|r],v[b+4>>2]=r+2,I=l[r+1|0],v[b+4>>2]=r+3,C=l[r+2|0],v[b+4>>2]=r+4,v[n+16>>2]=l[r+3|0]|(C|I<<8|T<<16)<<8,v[b+4>>2]=r+5,sf(I=n+20|0,T=$t(T=h+32|0,r=(C=l[r+4|0])?Si(b):v[F+32>>2],0)),Oe(T),C&&(T=v[5316],La[v[v[T>>2]+20>>2]](T,r,8610,1050)),v[F+40>>2]&&(r=v[b+4>>2],v[b+4>>2]=r+1,F=l[0|r],v[b+4>>2]=r+2,T=l[r+1|0],v[b+4>>2]=r+3,I=l[r+2|0],v[b+4>>2]=r+4,v[n+32>>2]=l[r+3|0]|(I|T<<8|F<<16)<<8,v[b+4>>2]=r+5,F=l[r+4|0],v[b+4>>2]=r+6,T=l[r+5|0],v[b+4>>2]=r+7,I=l[r+6|0],v[b+4>>2]=r+8,v[n+36>>2]=l[r+7|0]|(I|T<<8|F<<16)<<8),nt(s,g,n),(0|i)!=(0|(g=g+1|0)););v[h+32>>2]=s,_n(h+48|0,h+32|0),O=(A=p[(v[s+16>>2]+(i<<2)|0)-4>>2])<O?O:A}r=Zr(r=ht(48),n=zf(h+32|0,fr),h+48|0,O),Oe(n)}if(Po(h+48|0),V=h- -64|0,!r){La[v[v[b>>2]+4>>2]](b),f&&La[v[v[f>>2]+4>>2]](f),Oe(fr);break n}if(v[v[f+96>>2]+(y<<2)>>2]=r,Oe(fr),(0|nr)==(0|(y=y+1|0)))break}La[v[v[b>>2]+4>>2]](b);break r}La[v[v[b>>2]+4>>2]](b),f&&La[v[v[f>>2]+4>>2]](f),lf($,6964,8610)}f=0}return V=t+16|0,v[$+28>>2]&&(v[a>>2]=v[$+32>>2],H(20976,17196,0|a)),er(f,r=o+48|0,n=o+32|0),gi($),V=a+48|0,yf(n),yf(r),V=o- -64|0,0|f},function(r,n,f){var i,e;r|=0,V=i=V-16|0,e=n|=0,n=Sf(i+4|0,4+(f|=0)|0,v[f>>2],0),La[0|r](e,n),Oe(n),V=i+16|0},function(r,n){r|=0,n|=0;var f,i=0;V=f=V-16|0,v[f+12>>2]=r;r:{if(r=v[5482])for(;;){if(se(r+4|0,n))break r;if(!(r=v[r+20>>2]))break}n:{f:{if(r=v[5482])for(;;){if(se(i=r+4|0,n))break f;if(!(r=v[r+20>>2]))break}r=ht(28),v[r>>2]=17320,i=Ut(r+4|0),v[r+20>>2]=0,v[r+24>>2]=0,sf(i,n),v[r+16>>2]=v[f+12>>2],(n=v[5482])&&(v[n+24>>2]=r,v[r+20>>2]=n),v[5482]=r,v[5483]=v[5483]+1;break n}sf(i,n),v[r+16>>2]=v[f+12>>2]}}V=f+16|0},function(r,n){var f;r|=0,V=f=V-16|0,n=Sf(f+4|0,4+(n|=0)|0,v[n>>2],0),La[0|r](n),Oe(n),V=f+16|0},function(r){r|=0;var n,f=0,i=0,t=0;V=n=V-32|0;r:if(f=v[5482]){for(;;){if(!se(f+4|0,r)){if(f=v[f+20>>2])continue;break r}break}n:if(f=v[5482]){for(;;){if(!se(f+4|0,r)){if(f=v[f+20>>2])continue;break n}break}t=v[f+16>>2]}v[n+28>>2]=t;n:if(i=v[5476]){for(f=i;;){if(v[f+4>>2]!=(0|t)){if(f=v[f+12>>2])continue;break n}break}if(v[i+4>>2]!=(0|t))for(;i=v[i+12>>2],v[i+4>>2]!=(0|t););if(t=v[i+8>>2],f=v[t+4>>2],v[n+20>>2]=0,v[n+16>>2]=f,v[n+4>>2]=17256,v[n+24>>2]=n+4,e[0|n]=1,f)for(;e[0|n]=0,v[n+24>>2]=f,(i=v[f+8>>2])&&(La[v[v[i>>2]+4>>2]](i),f=v[n+24>>2]),e[0|n]=1,f=v[f+12>>2];);La[v[v[t>>2]+4>>2]](t),pf(21900,n+28|0),t=v[n+28>>2]}t&&La[v[v[t>>2]+4>>2]](t);n:if(f=v[5482]){for(;;){if(!se(f+4|0,r)){if(f=v[f+20>>2])continue;break n}break}i=v[f+24>>2],r=v[f+20>>2],v[(i?i+20:21928)>>2]=r,r&&(v[r+24>>2]=i),La[v[v[f>>2]+4>>2]](f),v[5483]=v[5483]-1}}V=n+32|0},function(r,n){n|=0,La[0|(r|=0)](n)},Iu,zo,function(){return v[5484]},zo,function(){return v[5242]},function(){return v[5485]},zo,function(){return v[5486]},Y,Dr,qu,function(r){Ct(qu(r|=0))},Du,function(r){Ct(Du(r|=0))},Zu,function(r){Ct(Zu(r|=0))},ao,function(r){Ct(ao(r|=0))},Yi,function(r){Yi(r|=0),ar(r)},ma,Wa,Ia,function(r,n,f,i){var e;n|=0,f|=0,i|=0,V=e=V-16|0,(r=v[8+(r|=0)>>2])&&(v[e+12>>2]=i,v[e+4>>2]=f,v[e+8>>2]=n,on(r+8|0,e+4|0),4==(0|n)&&$r(r)),V=e+16|0},function(r,n,f,i){var e;n|=0,f|=0,i|=0,V=e=V-16|0,(r=v[8+(r|=0)>>2])&&(v[e+12>>2]=i,v[e+8>>2]=n,v[e+4>>2]=f,on(r+24|0,e+4|0)),V=e+16|0},function(){Oe(21888)},Wa,xu,function(r){Ct(xu(r|=0))},ro,function(r){Ct(ro(r|=0))},Xu,function(r){Ct(Xu(r|=0))},Wa,Wa,function(){vo(21900)},function(){ho(21912)},function(){po(21924)},Ri,function(r){Ct(Ri(r|=0))},function(r){return v[(r|=0)>>2]=17224,Oe(r+16|0),Oe(r+4|0),0|r},function(r){v[(r|=0)>>2]=17224,Oe(r+16|0),Oe(r+4|0),Ct(r)},_u,function(r){Ct(_u(r|=0))},Wa,vo,function(r){Ct(vo(r|=0))},ho,function(r){Ct(ho(r|=0))},po,function(r){Ct(po(r|=0))},function(r){return v[(r|=0)>>2]=17320,Oe(r+4|0),0|r},function(r){v[(r|=0)>>2]=17320,Oe(r+4|0),Ct(r)},ma,Ra,function(r,n){return(n|=0)?0|X(n):0},function(r,n){return r|=0,(n|=0)?(!(r=X(n))|!(3&l[r-4|0])||Ze(r,0,n),0|r):0},function(r,n,f,i,e){r|=0,n|=0,i|=0,e|=0;var t=0,u=0,o=0,a=0,c=0,b=0,k=0,s=0,l=0,h=0;if(!(f|=0))return ar(n),0;if(n)if(f>>>0>=4294967232)v[5488]=48,e=0;else{t=f>>>0<11?16:f+11&-8,r=0,e=-8&(a=v[4+(i=n-8|0)>>2]);r:if(3&a){u=i+e|0;n:if(e>>>0>=t>>>0){if((r=e-t|0)>>>0<16)break n;v[i+4>>2]=1&a|t|2,v[4+(e=i+t|0)>>2]=3|r,v[u+4>>2]=1|v[u+4>>2],br(e,r)}else if(v[5495]!=(0|u))if(v[5494]!=(0|u)){if(2&(o=v[u+4>>2]))break r;if((c=e+(-8&o)|0)>>>0<t>>>0)break r;k=c-t|0;f:if(o>>>0<=255){if((0|(r=v[u+12>>2]))==(0|(e=v[u+8>>2]))){l=21956,h=v[5489]&dt(o>>>3|0),v[l>>2]=h;break f}v[e+12>>2]=r,v[r+8>>2]=e}else{b=v[u+24>>2];i:if((0|u)==(0|(e=v[u+12>>2]))){e:{if(!(o=v[(r=u+20|0)>>2])){if(!(o=v[u+16>>2]))break e;r=u+16|0}for(;s=r,(o=v[(r=(e=o)+20|0)>>2])||(r=e+16|0,o=v[e+16>>2]););v[s>>2]=0;break i}e=0}else r=v[u+8>>2],v[r+12>>2]=e,v[e+8>>2]=r;if(b){r=v[u+28>>2];i:{if(v[(o=22260+(r<<2)|0)>>2]==(0|u)){if(v[o>>2]=e,e)break i;l=21960,h=v[5490]&dt(r),v[l>>2]=h;break f}if(v[(v[b+16>>2]==(0|u)?16:20)+b>>2]=e,!e)break f}v[e+24>>2]=b,(r=v[u+16>>2])&&(v[e+16>>2]=r,v[r+24>>2]=e),(r=v[u+20>>2])&&(v[e+20>>2]=r,v[r+24>>2]=e)}}k>>>0<=15?(v[i+4>>2]=1&a|c|2,v[4+(r=i+c|0)>>2]=1|v[r+4>>2]):(v[i+4>>2]=1&a|t|2,v[4+(r=i+t|0)>>2]=3|k,v[4+(e=i+c|0)>>2]=1|v[e+4>>2],br(r,k))}else{if((e=e+v[5491]|0)>>>0<t>>>0)break r;(r=e-t|0)>>>0>=16?(v[i+4>>2]=1&a|t|2,v[4+(o=i+t|0)>>2]=1|r,v[(e=i+e|0)>>2]=r,v[e+4>>2]=-2&v[e+4>>2]):(v[i+4>>2]=e|1&a|2,v[4+(r=i+e|0)>>2]=1|v[r+4>>2],r=0),v[5494]=o,v[5491]=r}else{if((e=e+v[5492]|0)>>>0<=t>>>0)break r;v[i+4>>2]=1&a|t|2,e=e-t|0,v[4+(r=i+t|0)>>2]=1|e,v[5492]=e,v[5495]=r}r=i}else{if(t>>>0<256)break r;if(e>>>0>=t+4>>>0&&(r=i,e-t>>>0<=v[5609]<<1>>>0))break r;r=0}e=r+8|0,r||(e=0,(i=X(f))&&(Me(i,n,(r=(3&(r=v[n-4>>2])?-4:-8)+(-8&r)|0)>>>0<f>>>0?r:f),ar(n),e=i))}else e=X(f);return 0|e},function(r,n){ar(n|=0)},function(){return 0},Wn],Ha.set=function(r,n){this[r]=n},Ha.get=function(r){return this[r]},Ha);function Ma(){return i.byteLength/65536|0}return{z:function(){Nt(21096,3662,21120),fu(21108,2042),fu(21120,3667),Nt(21132,5464,21512),Nt(21144,5364,21584),Nt(21156,1855,21596),Nt(21168,2017,21596),Nt(21180,5498,21216),Nt(21192,1749,21584),fu(21204,6406),Nt(21216,5620,21512),Nt(21228,5575,21216),Nt(21240,5512,21512),Nt(21252,5450,21512),Nt(21268,1728,21584),Nt(21280,6385,21204),Nt(21292,5429,21216),fu(21308,4305),Nt(21320,4282,21308),Nt(21332,4299,21308),Nt(21344,1999,21596),Nt(21356,1981,21596),Nt(21368,1745,21584),Nt(21380,6402,21204),Nt(21392,5375,21216),Nt(21404,5544,21216),Nt(21416,5590,21404),Nt(21428,1897,21108),Nt(21440,1944,21108),Nt(21452,5634,21216),Nt(21464,5667,21560),Nt(21476,5530,21560),fu(21512,5672),Nt(21524,1704,21584),Nt(21536,6361,21204),Nt(21548,5401,21216),Nt(21560,5649,21216),Nt(21572,5495,21216),fu(21584,5833),Nt(21596,1877,21108),fu(21612,2295),Nt(21624,2271,21612),Nt(21636,2290,21612),v[5412]=1691,v[5413]=0,Y(),Lu(21648),v[5414]=1692,v[5415]=0,Dr(),Lu(21656),v[5467]=0,v[5468]=0,v[5471]=0,v[5469]=0,v[5470]=0,Ut(21888),v[5476]=0,v[5477]=0,v[5475]=17272,v[5479]=0,v[5480]=0,v[5478]=17288,v[5482]=0,v[5483]=0,v[5481]=17304,v[5614]=1743,v[5615]=0,Wn(),v[5615]=v[5613],v[5613]=22456},A:La,B:X,C:ar,D:function(r){r|=0;var n,f=0,i=0,t=0,u=0;for(V=n=V-32|0,v[n+16>>2]=v[4436],f=v[4435],v[n+8>>2]=v[4434],v[n+12>>2]=f,f=v[4433],v[n>>2]=v[4432],v[n+4>>2]=f,i=7,t=2,u=1;(u&=!(i=r>>>((f=i)<<2)&15)&!!(0|f))||(e[n+t|0]=l[i+17696|0],t=t+1|0),i=f-1|0,f;);return e[n+t|0]=0,r=(f=X(r=cf(n)+1|0))?Me(f,n,r):0,V=n+32|0,0|r},E:function(){var r=0;if(r=v[5613])for(;La[v[r>>2]](),r=v[r+4>>2];);}}}(r)}(r)}function s(r){return{then:function(n){n({instance:new k(r)})}}}Object.assign(f,u),u=null,f.wasmBinary&&(c=f.wasmBinary),f.noExitRuntime;var v=Error,l={};c=[],"object"!=typeof l&&H("no native wasm support detected");var h,d,p,y,m,w,g,F,A,T=!1;function $(){var r=h.buffer;f.HEAP8=d=new Int8Array(r),f.HEAP16=y=new Int16Array(r),f.HEAP32=w=new Int32Array(r),f.HEAPU8=p=new Uint8Array(r),f.HEAPU16=m=new Uint16Array(r),f.HEAPU32=g=new Uint32Array(r),f.HEAPF32=F=new Float32Array(r),f.HEAPF64=A=new Float64Array(r)}var I=f.INITIAL_MEMORY||33554432;65536<=I||H("INITIAL_MEMORY should be larger than STACK_SIZE, was "+I+"! (STACK_SIZE=65536)"),h=f.wasmMemory?f.wasmMemory:new function(){this.buffer=new ArrayBuffer(I/65536*65536)},$(),I=h.buffer.byteLength;var C,P=[],E=[],O=[];function R(){var r=f.preRun.shift();P.unshift(r)}var S,W=0,G=null;function U(){W++,f.monitorRunDependencies&&f.monitorRunDependencies(W)}function j(){if(W--,f.monitorRunDependencies&&f.monitorRunDependencies(W),0==W&&G){var r=G;G=null,r()}}function H(r){throw f.onAbort&&f.onAbort(r),b(r="Aborted("+r+")"),T=!0,r=new v(r+". Build with -sASSERTIONS for more info."),e(r),r}function L(r){return r.startsWith("data:application/octet-stream;base64,")}function M(r){try{if(r==S&&c)return new Uint8Array(c);throw"both async and sync fetching of the wasm failed"}catch(r){H(r)}}function _(r){return c||"function"!=typeof fetch?Promise.resolve().then((()=>M(r))):fetch(r,{credentials:"same-origin"}).then((n=>{if(!n.ok)throw"failed to load wasm binary file at '"+r+"'";return n.arrayBuffer()})).catch((()=>M(r)))}function z(r,n,f){return _(r).then((()=>s(n))).then((r=>r)).then(f,(r=>{b("failed to asynchronously prepare wasm: "+r),H(r)}))}function x(r,n){var f=S;return c||"function"!=typeof l.instantiateStreaming||L(f)||"function"!=typeof fetch?z(f,r,n):fetch(f,{credentials:"same-origin"}).then((i=>l.instantiateStreaming(i,r).then(n,(function(i){return b("wasm streaming compile failed: "+i),b("falling back to ArrayBuffer instantiation"),z(f,r,n)}))))}L(S="spine.wasm")||(S=a(S));var J="spine.js.mem";function K(){L(J)||(J=a(J)),U();var r=r=>{r.byteLength&&(r=new Uint8Array(r)),p.set(r,1024),f.memoryInitializerRequest&&delete f.memoryInitializerRequest.response,j()},n=()=>{t(r,(()=>{e(Error("could not load memory initializer "+J))}))};if(f.memoryInitializerRequest){var i=()=>{var i=f.memoryInitializerRequest,e=i.response;200!==i.status&&0!==i.status?(console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+i.status+", retrying "+J),n()):r(e)};f.memoryInitializerRequest.response?setTimeout(i,0):f.memoryInitializerRequest.addEventListener("load",i)}else n()}var B={20976:r=>{console.warn("[Spine]",r?Vr(r):"")}};function N(r){for(;0<r.length;)r.shift()(f)}function q(r){switch(r){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${r}`)}}var D=void 0;function V(r){for(var n="";p[r];)n+=D[p[r++]];return n}var Z={},Y={},X={};function Q(r){if(void 0===r)return"_unknown";var n=(r=r.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=n&&57>=n?`_${r}`:r}function rr(r,n){return r=Q(r),{[r]:function(){return n.apply(this,arguments)}}[r]}function nr(r){var n=Error,f=rr(r,(function(n){this.name=r,this.message=n,void 0!==(n=Error(n).stack)&&(this.stack=this.toString()+"\n"+n.replace(/^Error(:[^\n]*)?\n/,""))}));return f.prototype=Object.create(n.prototype),f.prototype.constructor=f,f.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},f}var fr=void 0;function ir(r){throw new fr(r)}var er=void 0;function tr(r){throw new er(r)}function ur(r,n,f){function i(n){(n=f(n)).length!==r.length&&tr("Mismatched type converter count");for(var i=0;i<r.length;++i)or(r[i],n[i])}r.forEach((function(r){X[r]=n}));var e=Array(n.length),t=[],u=0;n.forEach(((r,n)=>{Y.hasOwnProperty(r)?e[n]=Y[r]:(t.push(r),Z.hasOwnProperty(r)||(Z[r]=[]),Z[r].push((()=>{e[n]=Y[r],++u===t.length&&i(e)})))})),0===t.length&&i(e)}function or(r,n){if(!("argPackAdvance"in n))throw new TypeError("registerType registeredInstance requires argPackAdvance");var f=n.name;if(r||ir(`type "${f}" must have a positive integer typeid pointer`),Y.hasOwnProperty(r)){if({}.qa)return;ir(`Cannot register type '${f}' twice`)}Y[r]=n,delete X[r],Z.hasOwnProperty(r)&&(n=Z[r],delete Z[r],n.forEach((r=>r())))}function ar(r){ir(r.F.I.G.name+" instance already deleted")}var cr=!1;function br(){}function kr(r){--r.count.value,0===r.count.value&&(r.L?r.M.S(r.L):r.I.G.S(r.H))}function sr(r,n,f){return n===f?r:void 0===f.J||null===(r=sr(r,n,f.J))?null:f.ha(r)}var vr={},lr=[];function hr(){for(;lr.length;){var r=lr.pop();r.F.U=!1,r.delete()}}var dr=void 0,pr={};function yr(r,n){for(void 0===n&&ir("ptr should not be undefined");r.J;)n=r.W(n),r=r.J;return pr[n]}function mr(r,n){return n.I&&n.H||tr("makeClassHandle requires ptr and ptrType"),!!n.M!=!!n.L&&tr("Both smartPtrType and smartPtr must be specified"),n.count={value:1},wr(Object.create(r,{F:{value:n}}))}function wr(r){return"undefined"==typeof FinalizationRegistry?(wr=r=>r,r):(cr=new FinalizationRegistry((r=>{kr(r.F)})),br=r=>{cr.unregister(r)},(wr=r=>{var n=r.F;return n.L&&cr.register(r,{F:n},r),r})(r))}function gr(){}function Fr(r,n,f){if(void 0===r[n].K){var i=r[n];r[n]=function(){return r[n].K.hasOwnProperty(arguments.length)||ir(`Function '${f}' called with an invalid number of arguments (${arguments.length}) - expects one of (${r[n].K})!`),r[n].K[arguments.length].apply(this,arguments)},r[n].K=[],r[n].K[i.T]=i}}function Ar(r,n){f.hasOwnProperty(r)?(ir(`Cannot register public name '${r}' twice`),Fr(f,r,r),f.hasOwnProperty(void 0)&&ir("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"),f[r].K[void 0]=n):f[r]=n}function Tr(r,n,f,i,e,t,u,o){this.name=r,this.constructor=n,this.P=f,this.S=i,this.J=e,this.ia=t,this.W=u,this.ha=o,this.la=[]}function $r(r,n,f){for(;n!==f;)n.W||ir(`Expected null or instance of ${f.name}, got an instance of ${n.name}`),r=n.W(r),n=n.J;return r}function Ir(r,n){return null===n?(this.$&&ir(`null is not a valid ${this.name}`),0):(n.F||ir(`Cannot pass "${Br(n)}" as a ${this.name}`),n.F.H||ir(`Cannot pass deleted object as a pointer of type ${this.name}`),$r(n.F.H,n.F.I.G,this.G))}function Cr(r,n){if(null===n){if(this.$&&ir(`null is not a valid ${this.name}`),this.Z){var f=this.ma();return null!==r&&r.push(this.S,f),f}return 0}if(n.F||ir(`Cannot pass "${Br(n)}" as a ${this.name}`),n.F.H||ir(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.Y&&n.F.I.Y&&ir(`Cannot convert argument of type ${n.F.M?n.F.M.name:n.F.I.name} to parameter type ${this.name}`),f=$r(n.F.H,n.F.I.G,this.G),this.Z)switch(void 0===n.F.L&&ir("Passing raw pointer to smart pointer is illegal"),this.pa){case 0:n.F.M===this?f=n.F.L:ir(`Cannot convert argument of type ${n.F.M?n.F.M.name:n.F.I.name} to parameter type ${this.name}`);break;case 1:f=n.F.L;break;case 2:if(n.F.M===this)f=n.F.L;else{var i=n.clone();f=this.na(f,Kr((function(){i.delete()}))),null!==r&&r.push(this.S,f)}break;default:ir("Unsupporting sharing policy")}return f}function Pr(r,n){return null===n?(this.$&&ir(`null is not a valid ${this.name}`),0):(n.F||ir(`Cannot pass "${Br(n)}" as a ${this.name}`),n.F.H||ir(`Cannot pass deleted object as a pointer of type ${this.name}`),n.F.I.Y&&ir(`Cannot convert argument of type ${n.F.I.name} to parameter type ${this.name}`),$r(n.F.H,n.F.I.G,this.G))}function Er(r){return this.fromWireType(w[r>>2])}function Or(r,n,f,i){this.name=r,this.G=n,this.$=f,this.Y=i,this.Z=!1,this.S=this.na=this.ma=this.da=this.pa=this.ka=void 0,void 0!==n.J?this.toWireType=Cr:(this.toWireType=i?Ir:Pr,this.O=null)}function Rr(r,n){f.hasOwnProperty(r)||tr("Replacing nonexistant public symbol"),f[r]=n,f[r].T=void 0}function Sr(r,n){var i=[];return function(){if(i.length=0,Object.assign(i,arguments),r.includes("j")){var e=f["dynCall_"+r];e=i.length?e.apply(null,[n].concat(i)):e.call(null,n)}else e=C.get(n).apply(null,i);return e}}function Wr(r,n){var f=(r=V(r)).includes("j")?Sr(r,n):C.get(n);return"function"!=typeof f&&ir(`unknown function pointer with signature ${r}: ${n}`),f}var Gr=void 0;function Ur(r){var n=V(r=an(r));return on(r),n}function jr(r,n){var f=[],i={};throw n.forEach((function r(n){i[n]||Y[n]||(X[n]?X[n].forEach(r):(f.push(n),i[n]=!0))})),new Gr(`${r}: `+f.map(Ur).join([", "]))}function Hr(r){for(;r.length;){var n=r.pop();r.pop()(n)}}function Lr(r,n,f,i,e){var t=n.length;2>t&&ir("argTypes array size mismatch! Must at least get return value and 'this' types!");var u=null!==n[1]&&null!==f,o=!1;for(f=1;f<n.length;++f)if(null!==n[f]&&void 0===n[f].O){o=!0;break}var a="void"!==n[0].name,c=t-2,b=Array(c),k=[],s=[];return function(){if(arguments.length!==c&&ir(`function ${r} called with ${arguments.length} arguments, expected ${c} args!`),s.length=0,k.length=u?2:1,k[0]=e,u){var f=n[1].toWireType(s,this);k[1]=f}for(var t=0;t<c;++t)b[t]=n[t+2].toWireType(s,arguments[t]),k.push(b[t]);if(t=i.apply(null,k),o)Hr(s);else for(var v=u?1:2;v<n.length;v++){var l=1===v?f:b[v-2];null!==n[v].O&&n[v].O(l)}return a?n[0].fromWireType(t):void 0}}function Mr(r,n){for(var f=[],i=0;i<r;i++)f.push(g[n+4*i>>2]);return f}function _r(r,n,f){return r instanceof Object||ir(`${f} with invalid "this": ${r}`),r instanceof n.G.constructor||ir(`${f} incompatible with "this" of type ${r.constructor.name}`),r.F.H||ir(`cannot call emscripten binding method ${f} on deleted object`),$r(r.F.H,r.F.I.G,n.G)}var zr=new function(){this.N=[void 0],this.aa=[],this.get=function(r){return this.N[r]},this.has=function(r){return void 0!==this.N[r]},this.ea=function(r){var n=this.aa.pop()||this.N.length;return this.N[n]=r,n},this.fa=function(r){this.N[r]=void 0,this.aa.push(r)}};function xr(r){r>=zr.ba&&0==--zr.get(r).oa&&zr.fa(r)}var Jr=r=>(r||ir("Cannot use deleted val. handle = "+r),zr.get(r).value),Kr=r=>{switch(r){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return zr.ea({oa:1,value:r})}};function Br(r){if(null===r)return"null";var n=typeof r;return"object"===n||"array"===n||"function"===n?r.toString():""+r}function Nr(r,n){switch(n){case 2:return function(r){return this.fromWireType(F[r>>2])};case 3:return function(r){return this.fromWireType(A[r>>3])};default:throw new TypeError("Unknown float type: "+r)}}function qr(r,n,f){switch(n){case 0:return f?function(r){return d[r]}:function(r){return p[r]};case 1:return f?function(r){return y[r>>1]}:function(r){return m[r>>1]};case 2:return f?function(r){return w[r>>2]}:function(r){return g[r>>2]};default:throw new TypeError("Unknown integer type: "+r)}}var Dr="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function Vr(r,n){var f=p,i=r+n;for(n=r;f[n]&&!(n>=i);)++n;if(16<n-r&&f.buffer&&Dr)return Dr.decode(f.subarray(r,n));for(i="";r<n;){var e=f[r++];if(128&e){var t=63&f[r++];if(192==(224&e))i+=String.fromCharCode((31&e)<<6|t);else{var u=63&f[r++];65536>(e=224==(240&e)?(15&e)<<12|t<<6|u:(7&e)<<18|t<<12|u<<6|63&f[r++])?i+=String.fromCharCode(e):(e-=65536,i+=String.fromCharCode(55296|e>>10,56320|1023&e))}}else i+=String.fromCharCode(e)}return i}function Zr(r,n){var f=Y[r];return void 0===f&&ir(n+" has unknown type "+Ur(r)),f}var Yr={},Xr=[];function Qr(){var r=f.SpineWasmUtil,n=r.getCurrentListenerID(),i=r.getCurrentTrackEntry(),e=r.getCurrentEvent();r=r.getCurrentEventType(),globalThis.TrackEntryListeners.emitListener(n,i,e,r)}function rn(){var r=f.SpineWasmUtil,n=r.getCurrentListenerID(),i=r.getCurrentEventType(),e=r.getCurrentTrackEntry();r=r.getCurrentEvent(),globalThis.TrackEntryListeners.emitTrackEntryListener(n,e,r,i)}f._spineListenerCallBackFromJS=Qr,f._spineTrackListenerCallback=rn;for(var nn=Array(256),fn=0;256>fn;++fn)nn[fn]=String.fromCharCode(fn);D=nn,fr=f.BindingError=nr("BindingError"),er=f.InternalError=nr("InternalError"),gr.prototype.isAliasOf=function(r){if(!(this instanceof gr&&r instanceof gr))return!1;var n=this.F.I.G,f=this.F.H,i=r.F.I.G;for(r=r.F.H;n.J;)f=n.W(f),n=n.J;for(;i.J;)r=i.W(r),i=i.J;return n===i&&f===r},gr.prototype.clone=function(){if(this.F.H||ar(this),this.F.V)return this.F.count.value+=1,this;var r=wr,n=Object,f=n.create,i=Object.getPrototypeOf(this),e=this.F;return(r=r(f.call(n,i,{F:{value:{count:e.count,U:e.U,V:e.V,H:e.H,I:e.I,L:e.L,M:e.M}}}))).F.count.value+=1,r.F.U=!1,r},gr.prototype.delete=function(){this.F.H||ar(this),this.F.U&&!this.F.V&&ir("Object already scheduled for deletion"),br(this),kr(this.F),this.F.V||(this.F.L=void 0,this.F.H=void 0)},gr.prototype.isDeleted=function(){return!this.F.H},gr.prototype.deleteLater=function(){return this.F.H||ar(this),this.F.U&&!this.F.V&&ir("Object already scheduled for deletion"),lr.push(this),1===lr.length&&dr&&dr(hr),this.F.U=!0,this},f.getInheritedInstanceCount=function(){return Object.keys(pr).length},f.getLiveInheritedInstances=function(){var r,n=[];for(r in pr)pr.hasOwnProperty(r)&&n.push(pr[r]);return n},f.flushPendingDeletes=hr,f.setDelayFunction=function(r){dr=r,lr.length&&dr&&dr(hr)},Or.prototype.ja=function(r){return this.da&&(r=this.da(r)),r},Or.prototype.ca=function(r){this.S&&this.S(r)},Or.prototype.argPackAdvance=8,Or.prototype.readValueFromPointer=Er,Or.prototype.deleteObject=function(r){null!==r&&r.delete()},Or.prototype.fromWireType=function(r){function n(){return this.Z?mr(this.G.P,{I:this.ka,H:f,M:this,L:r}):mr(this.G.P,{I:this,H:r})}var f=this.ja(r);if(!f)return this.ca(r),null;var i=yr(this.G,f);if(void 0!==i)return 0===i.F.count.value?(i.F.H=f,i.F.L=r,i.clone()):(i=i.clone(),this.ca(r),i);if(i=this.G.ia(f),!(i=vr[i]))return n.call(this);i=this.Y?i.ga:i.pointerType;var e=sr(f,this.G,i.G);return null===e?n.call(this):this.Z?mr(i.G.P,{I:i,H:e,M:this,L:r}):mr(i.G.P,{I:i,H:e})},Gr=f.UnboundTypeError=nr("UnboundTypeError"),zr.N.push({value:void 0},{value:null},{value:!0},{value:!1}),zr.ba=zr.N.length,f.count_emval_handles=function(){for(var r=0,n=zr.ba;n<zr.N.length;++n)void 0!==zr.N[n]&&++r;return r};var en,tn={r:function(){},u:function(r,n,f,i,e){var t=q(f);or(r,{name:n=V(n),fromWireType:function(r){return!!r},toWireType:function(r,n){return n?i:e},argPackAdvance:8,readValueFromPointer:function(r){if(1===f)var i=d;else if(2===f)i=y;else{if(4!==f)throw new TypeError("Unknown boolean type size: "+n);i=w}return this.fromWireType(i[r>>t])},O:null})},d:function(r,n,f,i,e,t,u,o,a,c,b,k,s){b=V(b),t=Wr(e,t),o&&(o=Wr(u,o)),c&&(c=Wr(a,c)),s=Wr(k,s);var v=Q(b);Ar(v,(function(){jr(`Cannot construct ${b} due to unbound types`,[i])})),ur([r,n,f],i?[i]:[],(function(n){if(n=n[0],i)var f=n.G,e=f.P;else e=gr.prototype;n=rr(v,(function(){if(Object.getPrototypeOf(this)!==u)throw new fr("Use 'new' to construct "+b);if(void 0===a.R)throw new fr(b+" has no accessible constructor");var r=a.R[arguments.length];if(void 0===r)throw new fr(`Tried to invoke ctor of ${b} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(a.R).toString()}) parameters instead!`);return r.apply(this,arguments)}));var u=Object.create(e,{constructor:{value:n}});n.prototype=u;var a=new Tr(b,n,u,s,f,t,o,c);a.J&&(void 0===a.J.X&&(a.J.X=[]),a.J.X.push(a)),f=new Or(b,a,!0,!1),e=new Or(b+"*",a,!1,!1);var k=new Or(b+" const*",a,!1,!0);return vr[r]={pointerType:e,ga:k},Rr(v,n),[f,e,k]}))},h:function(r,n,f,i,e,t,u){var o=Mr(f,i);n=V(n),t=Wr(e,t),ur([],[r],(function(r){function i(){jr(`Cannot call ${e} due to unbound types`,o)}var e=`${(r=r[0]).name}.${n}`;n.startsWith("@@")&&(n=Symbol[n.substring(2)]);var a=r.G.constructor;return void 0===a[n]?(i.T=f-1,a[n]=i):(Fr(a,n,e),a[n].K[f-1]=i),ur([],o,(function(i){if(i=Lr(e,[i[0],null].concat(i.slice(1)),null,t,u),void 0===a[n].K?(i.T=f-1,a[n]=i):a[n].K[f-1]=i,r.G.X)for(const f of r.G.X)f.constructor.hasOwnProperty(n)||(f.constructor[n]=i);return[]})),[]}))},i:function(r,n,f,i,e,t,u,o){n=V(n),t=Wr(e,t),ur([],[r],(function(r){var e=`${(r=r[0]).name}.${n}`,a={get:function(){jr(`Cannot access ${e} due to unbound types`,[f])},enumerable:!0,configurable:!0};return a.set=o?()=>{jr(`Cannot access ${e} due to unbound types`,[f])}:()=>{ir(`${e} is a read-only property`)},Object.defineProperty(r.G.constructor,n,a),ur([],[f],(function(f){f=f[0];var e={get:function(){return f.fromWireType(t(i))},enumerable:!0};return o&&(o=Wr(u,o),e.set=r=>{var n=[];o(i,f.toWireType(n,r)),Hr(n)}),Object.defineProperty(r.G.constructor,n,e),[]})),[]}))},e:function(r,n,f,i,e,t){0<n||H();var u=Mr(n,f);e=Wr(i,e),ur([],[r],(function(r){var f=`constructor ${(r=r[0]).name}`;if(void 0===r.G.R&&(r.G.R=[]),void 0!==r.G.R[n-1])throw new fr(`Cannot register multiple constructors with identical number of parameters (${n-1}) for class '${r.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return r.G.R[n-1]=()=>{jr(`Cannot construct ${r.name} due to unbound types`,u)},ur([],u,(function(i){return i.splice(1,0,null),r.G.R[n-1]=Lr(f,i,null,e,t),[]})),[]}))},b:function(r,n,f,i,e,t,u,o){var a=Mr(f,i);n=V(n),t=Wr(e,t),ur([],[r],(function(r){function i(){jr(`Cannot call ${e} due to unbound types`,a)}var e=`${(r=r[0]).name}.${n}`;n.startsWith("@@")&&(n=Symbol[n.substring(2)]),o&&r.G.la.push(n);var c=r.G.P,b=c[n];return void 0===b||void 0===b.K&&b.className!==r.name&&b.T===f-2?(i.T=f-2,i.className=r.name,c[n]=i):(Fr(c,n,e),c[n].K[f-2]=i),ur([],a,(function(i){return i=Lr(e,i,r,t,u),void 0===c[n].K?(i.T=f-2,c[n]=i):c[n].K[f-2]=i,[]})),[]}))},c:function(r,n,f,i,e,t,u,o,a,c){n=V(n),e=Wr(i,e),ur([],[r],(function(r){var i=`${(r=r[0]).name}.${n}`,b={get:function(){jr(`Cannot access ${i} due to unbound types`,[f,u])},enumerable:!0,configurable:!0};return b.set=a?()=>{jr(`Cannot access ${i} due to unbound types`,[f,u])}:()=>{ir(i+" is a read-only property")},Object.defineProperty(r.G.P,n,b),ur([],a?[f,u]:[f],(function(f){var u=f[0],b={get:function(){var n=_r(this,r,i+" getter");return u.fromWireType(e(t,n))},enumerable:!0};if(a){a=Wr(o,a);var k=f[1];b.set=function(n){var f=_r(this,r,i+" setter"),e=[];a(c,f,k.toWireType(e,n)),Hr(e)}}return Object.defineProperty(r.G.P,n,b),[]})),[]}))},t:function(r,n){or(r,{name:n=V(n),fromWireType:function(r){var n=Jr(r);return xr(r),n},toWireType:function(r,n){return Kr(n)},argPackAdvance:8,readValueFromPointer:Er,O:null})},p:function(r,n,f){f=q(f),or(r,{name:n=V(n),fromWireType:function(r){return r},toWireType:function(r,n){return n},argPackAdvance:8,readValueFromPointer:Nr(n,f),O:null})},g:function(r,n,f,i,e){n=V(n),-1===e&&(e=4294967295),e=q(f);var t=r=>r;if(0===i){var u=32-8*f;t=r=>r<<u>>>u}f=n.includes("unsigned")?function(r,n){return n>>>0}:function(r,n){return n},or(r,{name:n,fromWireType:t,toWireType:f,argPackAdvance:8,readValueFromPointer:qr(n,e,0!==i),O:null})},y:function(r,n){var f="std::string"===(n=V(n));or(r,{name:n,fromWireType:function(r){var n=g[r>>2],i=r+4;if(f)for(var e=i,t=0;t<=n;++t){var u=i+t;if(t==n||0==p[u]){if(e=e?Vr(e,u-e):"",void 0===o)var o=e;else o+=String.fromCharCode(0),o+=e;e=u+1}}else{for(o=Array(n),t=0;t<n;++t)o[t]=String.fromCharCode(p[i+t]);o=o.join("")}return on(r),o},toWireType:function(r,n){n instanceof ArrayBuffer&&(n=new Uint8Array(n));var i,e,t="string"==typeof n;if(t||n instanceof Uint8Array||n instanceof Uint8ClampedArray||n instanceof Int8Array||ir("Cannot pass non-string to std::string"),f&&t)for(i=e=0;i<n.length;++i){var u=n.charCodeAt(i);127>=u?e++:2047>=u?e+=2:55296<=u&&57343>=u?(e+=4,++i):e+=3}else e=n.length;if(u=(e=un(4+(i=e)+1))+4,g[e>>2]=i,f&&t){if(t=u,u=i+1,i=p,0<u){u=t+u-1;for(var o=0;o<n.length;++o){var a=n.charCodeAt(o);if(55296<=a&&57343>=a&&(a=65536+((1023&a)<<10)|1023&n.charCodeAt(++o)),127>=a){if(t>=u)break;i[t++]=a}else{if(2047>=a){if(t+1>=u)break;i[t++]=192|a>>6}else{if(65535>=a){if(t+2>=u)break;i[t++]=224|a>>12}else{if(t+3>=u)break;i[t++]=240|a>>18,i[t++]=128|a>>12&63}i[t++]=128|a>>6&63}i[t++]=128|63&a}}i[t]=0}}else if(t)for(t=0;t<i;++t)255<(o=n.charCodeAt(t))&&(on(u),ir("String has UTF-16 code units that do not fit in 8 bits")),p[u+t]=o;else for(t=0;t<i;++t)p[u+t]=n[t];return null!==r&&r.push(on,e),e},argPackAdvance:8,readValueFromPointer:Er,O:function(r){on(r)}})},v:function(r,n){or(r,{ra:!0,name:n=V(n),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},k:function(r,n,f){r=Jr(r),n=Zr(n,"emval::as");var i=[],e=Kr(i);return g[f>>2]=e,n.toWireType(i,r)},f:xr,l:function(r,n){return r=Jr(r),n=Jr(n),Kr(r[n])},m:function(r){var n=Yr[r];return Kr(void 0===n?V(r):n)},j:function(r){Hr(Jr(r)),xr(r)},q:function(r,n){return r=(r=Zr(r,"_emval_take_value")).readValueFromPointer(n),Kr(r)},o:function(){H("")},n:function(r,n,f){var i;for(Xr.length=0,f>>=2;i=p[n++];)f+=105!=i&f,Xr.push(105==i?w[f]:A[f++>>1]),++f;return B[r].apply(null,Xr)},s:function(r){var n=p.length;if(2147483648<(r>>>=0))return!1;for(var f=1;4>=f;f*=2){var i=n*(1+.2/f);i=Math.min(i,r+100663296);var e=Math;i=Math.max(r,i);r:{e=e.min.call(e,2147483648,i+(65536-i%65536)%65536)-h.buffer.byteLength+65535>>>16;try{h.grow(e),$();var t=1;break r}catch(r){}t=void 0}if(t)return!0}return!1},a:h,x:Qr,w:rn};function un(){return(un=f.asm.B).apply(null,arguments)}function on(){return(on=f.asm.C).apply(null,arguments)}function an(){return(an=f.asm.D).apply(null,arguments)}function cn(){function r(){if(!en&&(en=!0,f.calledRun=!0,!T)){if(N(E),i(f),f.onRuntimeInitialized&&f.onRuntimeInitialized(),f.postRun)for("function"==typeof f.postRun&&(f.postRun=[f.postRun]);f.postRun.length;){var r=f.postRun.shift();O.unshift(r)}N(O)}}if(!(0<W)){if(f.preRun)for("function"==typeof f.preRun&&(f.preRun=[f.preRun]);f.preRun.length;)R();N(P),0<W||(f.setStatus?(f.setStatus("Running..."),setTimeout((function(){setTimeout((function(){f.setStatus("")}),1),r()}),1)):r())}}if(function(){function r(r){return r=r.exports,f.asm=r,K(),C=f.asm.A,E.unshift(f.asm.z),j(),r}var n={a:tn};if(U(),f.instantiateWasm)try{return f.instantiateWasm(n,r)}catch(r){b("Module.instantiateWasm callback failed with error: "+r),e(r)}x(n,(function(n){r(n.instance)})).catch(e)}(),f.__embind_initialize_bindings=function(){return(f.__embind_initialize_bindings=f.asm.E).apply(null,arguments)},G=function r(){en||cn(),en||(G=r)},f.preInit)for("function"==typeof f.preInit&&(f.preInit=[f.preInit]);0<f.preInit.length;)f.preInit.pop()();return cn(),r.ready}))}}}));
