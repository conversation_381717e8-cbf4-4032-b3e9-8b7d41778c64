System.register(["./_virtual_cc-D1oNkISf.js"],(function(n){"use strict";var t;return{setters:[function(n){t=n._}],execute:function(){var e;n("default",(e="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(n){var r,i,o;void 0===n&&(n={}),r||(r=void 0!==n?n:{}),r.ready=new Promise((function(n,t){i=n,o=t}));var a=Object.assign({},r),u="";"undefined"!=typeof document&&document.currentScript&&(u=document.currentScript.src),e&&(u=e),u=0!==u.indexOf("blob:")?u.substr(0,u.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var s,c=r.printErr||console.error.bind(console);Object.assign(r,a),a=null,r.wasmBinary&&(s=r.wasmBinary),r.noExitRuntime,"object"!=typeof WebAssembly&&I("no native wasm support detected");var f,l,h,p,d,v,y,m,g,b=!1;function w(){var n=f.buffer;r.HEAP8=l=new Int8Array(n),r.HEAP16=p=new Int16Array(n),r.HEAP32=v=new Int32Array(n),r.HEAPU8=h=new Uint8Array(n),r.HEAPU16=d=new Uint16Array(n),r.HEAPU32=y=new Uint32Array(n),r.HEAPF32=m=new Float32Array(n),r.HEAPF64=g=new Float64Array(n)}var F,T=[],C=[],P=[];function A(){var n=r.preRun.shift();T.unshift(n)}var k,W=0,O=null;function I(n){throw r.onAbort&&r.onAbort(n),c(n="Aborted("+n+")"),b=!0,n=new WebAssembly.RuntimeError(n+". Build with -sASSERTIONS for more info."),o(n),n}function E(n){return n.startsWith("data:application/octet-stream;base64,")}if(!E(k="spine.wasm")){var S=k;k=r.locateFile?r.locateFile(S,u):u+S}function G(n){try{if(n==k&&s)return new Uint8Array(s);throw"both async and sync fetching of the wasm failed"}catch(n){I(n)}}function R(n){return s||"function"!=typeof fetch?Promise.resolve().then((function(){return G(n)})):fetch(n,{credentials:"same-origin"}).then((function(t){if(!t.ok)throw"failed to load wasm binary file at '"+n+"'";return t.arrayBuffer()})).catch((function(){return G(n)}))}function j(n,t,e){return R(n).then((function(n){return WebAssembly.instantiate(n,t)})).then((function(n){return n})).then(e,(function(n){c("failed to asynchronously prepare wasm: "+n),I(n)}))}function H(n,t){var e=k;return s||"function"!=typeof WebAssembly.instantiateStreaming||E(e)||"function"!=typeof fetch?j(e,n,t):fetch(e,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(t,(function(r){return c("wasm streaming compile failed: "+r),c("falling back to ArrayBuffer instantiation"),j(e,n,t)}))}))}var U={20976:function(n){console.warn("[Spine]",n?xn(n):"")}};function L(n){for(;0<n.length;)n.shift()(r)}function _(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var M=void 0;function J(n){for(var t="";h[n];)t+=M[h[n++]];return t}var x={},K={},N={};function V(n){if(void 0===n)return"_unknown";var t=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=t&&57>=t?"_"+n:n}function D(n,t){var e;return(e={},e[n=V(n)]=function(){return t.apply(this,arguments)},e)[n]}function z(n){var t=Error,e=D(n,(function(t){this.name=n,this.message=t,void 0!==(t=Error(t).stack)&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},e}var B=void 0;function Z(n){throw new B(n)}var X=void 0;function Y(n){throw new X(n)}function $(n,t,e){function r(t){(t=e(t)).length!==n.length&&Y("Mismatched type converter count");for(var r=0;r<n.length;++r)q(n[r],t[r])}n.forEach((function(n){N[n]=t}));var i=Array(t.length),o=[],a=0;t.forEach((function(n,t){K.hasOwnProperty(n)?i[t]=K[n]:(o.push(n),x.hasOwnProperty(n)||(x[n]=[]),x[n].push((function(){i[t]=K[n],++a===o.length&&r(i)})))})),0===o.length&&r(i)}function q(n,t){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var e=t.name;if(n||Z('type "'+e+'" must have a positive integer typeid pointer'),K.hasOwnProperty(n)){if({}.qa)return;Z("Cannot register type '"+e+"' twice")}K[n]=t,delete N[n],x.hasOwnProperty(n)&&(t=x[n],delete x[n],t.forEach((function(n){return n()})))}function Q(n){Z(n.F.I.G.name+" instance already deleted")}var nn=!1;function tn(){}function en(n){--n.count.value,0===n.count.value&&(n.L?n.M.S(n.L):n.I.G.S(n.H))}function rn(n,t,e){return t===e?n:void 0===e.J||null===(n=rn(n,t,e.J))?null:e.ha(n)}var on={},an=[];function un(){for(;an.length;){var n=an.pop();n.F.U=!1,n.delete()}}var sn=void 0,cn={};function fn(n,t){for(void 0===t&&Z("ptr should not be undefined");n.J;)t=n.W(t),n=n.J;return cn[t]}function ln(n,t){return t.I&&t.H||Y("makeClassHandle requires ptr and ptrType"),!!t.M!=!!t.L&&Y("Both smartPtrType and smartPtr must be specified"),t.count={value:1},hn(Object.create(n,{F:{value:t}}))}function hn(n){return"undefined"==typeof FinalizationRegistry?(hn=function(n){return n},n):(nn=new FinalizationRegistry((function(n){en(n.F)})),tn=function(n){nn.unregister(n)},(hn=function(n){var t=n.F;return t.L&&nn.register(n,{F:t},n),n})(n))}function pn(){}function dn(n,t,e){if(void 0===n[t].K){var r=n[t];n[t]=function(){return n[t].K.hasOwnProperty(arguments.length)||Z("Function '"+e+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[t].K+")!"),n[t].K[arguments.length].apply(this,arguments)},n[t].K=[],n[t].K[r.T]=r}}function vn(n,t){r.hasOwnProperty(n)?(Z("Cannot register public name '"+n+"' twice"),dn(r,n,n),r.hasOwnProperty(void 0)&&Z("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"),r[n].K[void 0]=t):r[n]=t}function yn(n,t,e,r,i,o,a,u){this.name=n,this.constructor=t,this.P=e,this.S=r,this.J=i,this.ia=o,this.W=a,this.ha=u,this.la=[]}function mn(n,t,e){for(;t!==e;)t.W||Z("Expected null or instance of "+e.name+", got an instance of "+t.name),n=t.W(n),t=t.J;return n}function gn(n,t){return null===t?(this.$&&Z("null is not a valid "+this.name),0):(t.F||Z('Cannot pass "'+Ln(t)+'" as a '+this.name),t.F.H||Z("Cannot pass deleted object as a pointer of type "+this.name),mn(t.F.H,t.F.I.G,this.G))}function bn(n,t){if(null===t){if(this.$&&Z("null is not a valid "+this.name),this.Z){var e=this.ma();return null!==n&&n.push(this.S,e),e}return 0}if(t.F||Z('Cannot pass "'+Ln(t)+'" as a '+this.name),t.F.H||Z("Cannot pass deleted object as a pointer of type "+this.name),!this.Y&&t.F.I.Y&&Z("Cannot convert argument of type "+(t.F.M?t.F.M.name:t.F.I.name)+" to parameter type "+this.name),e=mn(t.F.H,t.F.I.G,this.G),this.Z)switch(void 0===t.F.L&&Z("Passing raw pointer to smart pointer is illegal"),this.pa){case 0:t.F.M===this?e=t.F.L:Z("Cannot convert argument of type "+(t.F.M?t.F.M.name:t.F.I.name)+" to parameter type "+this.name);break;case 1:e=t.F.L;break;case 2:if(t.F.M===this)e=t.F.L;else{var r=t.clone();e=this.na(e,Un((function(){r.delete()}))),null!==n&&n.push(this.S,e)}break;default:Z("Unsupporting sharing policy")}return e}function wn(n,t){return null===t?(this.$&&Z("null is not a valid "+this.name),0):(t.F||Z('Cannot pass "'+Ln(t)+'" as a '+this.name),t.F.H||Z("Cannot pass deleted object as a pointer of type "+this.name),t.F.I.Y&&Z("Cannot convert argument of type "+t.F.I.name+" to parameter type "+this.name),mn(t.F.H,t.F.I.G,this.G))}function Fn(n){return this.fromWireType(v[n>>2])}function Tn(n,t,e,r){this.name=n,this.G=t,this.$=e,this.Y=r,this.Z=!1,this.S=this.na=this.ma=this.da=this.pa=this.ka=void 0,void 0!==t.J?this.toWireType=bn:(this.toWireType=r?gn:wn,this.O=null)}function Cn(n,t){r.hasOwnProperty(n)||Y("Replacing nonexistant public symbol"),r[n]=t,r[n].T=void 0}function Pn(n,t){var e=[];return function(){if(e.length=0,Object.assign(e,arguments),n.includes("j")){var i=r["dynCall_"+n];i=e.length?i.apply(null,[t].concat(e)):i.call(null,t)}else i=F.get(t).apply(null,e);return i}}function An(n,t){var e=(n=J(n)).includes("j")?Pn(n,t):F.get(t);return"function"!=typeof e&&Z("unknown function pointer with signature "+n+": "+t),e}var kn=void 0;function Wn(n){var t=J(n=Qn(n));return qn(n),t}function On(n,t){var e=[],r={};throw t.forEach((function n(t){r[t]||K[t]||(N[t]?N[t].forEach(n):(e.push(t),r[t]=!0))})),new kn(n+": "+e.map(Wn).join([", "]))}function In(n){for(;n.length;){var t=n.pop();n.pop()(t)}}function En(n,t,e,r,i){var o=t.length;2>o&&Z("argTypes array size mismatch! Must at least get return value and 'this' types!");var a=null!==t[1]&&null!==e,u=!1;for(e=1;e<t.length;++e)if(null!==t[e]&&void 0===t[e].O){u=!0;break}var s="void"!==t[0].name,c=o-2,f=Array(c),l=[],h=[];return function(){if(arguments.length!==c&&Z("function "+n+" called with "+arguments.length+" arguments, expected "+c+" args!"),h.length=0,l.length=a?2:1,l[0]=i,a){var e=t[1].toWireType(h,this);l[1]=e}for(var o=0;o<c;++o)f[o]=t[o+2].toWireType(h,arguments[o]),l.push(f[o]);if(o=r.apply(null,l),u)In(h);else for(var p=a?1:2;p<t.length;p++){var d=1===p?e:f[p-2];null!==t[p].O&&t[p].O(d)}return s?t[0].fromWireType(o):void 0}}function Sn(n,t){for(var e=[],r=0;r<n;r++)e.push(y[t+4*r>>2]);return e}function Gn(n,t,e){return n instanceof Object||Z(e+' with invalid "this": '+n),n instanceof t.G.constructor||Z(e+' incompatible with "this" of type '+n.constructor.name),n.F.H||Z("cannot call emscripten binding method "+e+" on deleted object"),mn(n.F.H,n.F.I.G,t.G)}var Rn=new function(){this.N=[void 0],this.aa=[],this.get=function(n){return this.N[n]},this.has=function(n){return void 0!==this.N[n]},this.ea=function(n){var t=this.aa.pop()||this.N.length;return this.N[t]=n,t},this.fa=function(n){this.N[n]=void 0,this.aa.push(n)}};function jn(n){n>=Rn.ba&&0==--Rn.get(n).oa&&Rn.fa(n)}var Hn=function(n){return n||Z("Cannot use deleted val. handle = "+n),Rn.get(n).value},Un=function(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return Rn.ea({oa:1,value:n})}};function Ln(n){if(null===n)return"null";var t=typeof n;return"object"===t||"array"===t||"function"===t?n.toString():""+n}function _n(n,t){switch(t){case 2:return function(n){return this.fromWireType(m[n>>2])};case 3:return function(n){return this.fromWireType(g[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function Mn(n,t,e){switch(t){case 0:return e?function(n){return l[n]}:function(n){return h[n]};case 1:return e?function(n){return p[n>>1]}:function(n){return d[n>>1]};case 2:return e?function(n){return v[n>>2]}:function(n){return y[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var Jn="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function xn(n,t){var e=h,r=n+t;for(t=n;e[t]&&!(t>=r);)++t;if(16<t-n&&e.buffer&&Jn)return Jn.decode(e.subarray(n,t));for(r="";n<t;){var i=e[n++];if(128&i){var o=63&e[n++];if(192==(224&i))r+=String.fromCharCode((31&i)<<6|o);else{var a=63&e[n++];65536>(i=224==(240&i)?(15&i)<<12|o<<6|a:(7&i)<<18|o<<12|a<<6|63&e[n++])?r+=String.fromCharCode(i):(i-=65536,r+=String.fromCharCode(55296|i>>10,56320|1023&i))}}else r+=String.fromCharCode(i)}return r}function Kn(n,t){var e=K[n];return void 0===e&&Z(t+" has unknown type "+Wn(n)),e}var Nn={},Vn=[];function Dn(){var n=r.SpineWasmUtil,t=n.getCurrentListenerID(),e=n.getCurrentTrackEntry(),i=n.getCurrentEvent();n=n.getCurrentEventType(),globalThis.TrackEntryListeners.emitListener(t,e,i,n)}function zn(){var n=r.SpineWasmUtil,t=n.getCurrentListenerID(),e=n.getCurrentEventType(),i=n.getCurrentTrackEntry();n=n.getCurrentEvent(),globalThis.TrackEntryListeners.emitTrackEntryListener(t,i,n,e)}r._spineListenerCallBackFromJS=Dn,r._spineTrackListenerCallback=zn;for(var Bn=Array(256),Zn=0;256>Zn;++Zn)Bn[Zn]=String.fromCharCode(Zn);M=Bn,B=r.BindingError=z("BindingError"),X=r.InternalError=z("InternalError"),pn.prototype.isAliasOf=function(n){if(!(this instanceof pn&&n instanceof pn))return!1;var t=this.F.I.G,e=this.F.H,r=n.F.I.G;for(n=n.F.H;t.J;)e=t.W(e),t=t.J;for(;r.J;)n=r.W(n),r=r.J;return t===r&&e===n},pn.prototype.clone=function(){if(this.F.H||Q(this),this.F.V)return this.F.count.value+=1,this;var n=hn,t=Object,e=t.create,r=Object.getPrototypeOf(this),i=this.F;return(n=n(e.call(t,r,{F:{value:{count:i.count,U:i.U,V:i.V,H:i.H,I:i.I,L:i.L,M:i.M}}}))).F.count.value+=1,n.F.U=!1,n},pn.prototype.delete=function(){this.F.H||Q(this),this.F.U&&!this.F.V&&Z("Object already scheduled for deletion"),tn(this),en(this.F),this.F.V||(this.F.L=void 0,this.F.H=void 0)},pn.prototype.isDeleted=function(){return!this.F.H},pn.prototype.deleteLater=function(){return this.F.H||Q(this),this.F.U&&!this.F.V&&Z("Object already scheduled for deletion"),an.push(this),1===an.length&&sn&&sn(un),this.F.U=!0,this},r.getInheritedInstanceCount=function(){return Object.keys(cn).length},r.getLiveInheritedInstances=function(){var n,t=[];for(n in cn)cn.hasOwnProperty(n)&&t.push(cn[n]);return t},r.flushPendingDeletes=un,r.setDelayFunction=function(n){sn=n,an.length&&sn&&sn(un)},Tn.prototype.ja=function(n){return this.da&&(n=this.da(n)),n},Tn.prototype.ca=function(n){this.S&&this.S(n)},Tn.prototype.argPackAdvance=8,Tn.prototype.readValueFromPointer=Fn,Tn.prototype.deleteObject=function(n){null!==n&&n.delete()},Tn.prototype.fromWireType=function(n){function t(){return this.Z?ln(this.G.P,{I:this.ka,H:e,M:this,L:n}):ln(this.G.P,{I:this,H:n})}var e=this.ja(n);if(!e)return this.ca(n),null;var r=fn(this.G,e);if(void 0!==r)return 0===r.F.count.value?(r.F.H=e,r.F.L=n,r.clone()):(r=r.clone(),this.ca(n),r);if(r=this.G.ia(e),!(r=on[r]))return t.call(this);r=this.Y?r.ga:r.pointerType;var i=rn(e,this.G,r.G);return null===i?t.call(this):this.Z?ln(r.G.P,{I:r,H:i,M:this,L:n}):ln(r.G.P,{I:r,H:i})},kn=r.UnboundTypeError=z("UnboundTypeError"),Rn.N.push({value:void 0},{value:null},{value:!0},{value:!1}),Rn.ba=Rn.N.length,r.count_emval_handles=function(){for(var n=0,t=Rn.ba;t<Rn.N.length;++t)void 0!==Rn.N[t]&&++n;return n};var Xn,Yn={q:function(){},t:function(n,t,e,r,i){var o=_(e);q(n,{name:t=J(t),fromWireType:function(n){return!!n},toWireType:function(n,t){return t?r:i},argPackAdvance:8,readValueFromPointer:function(n){if(1===e)var r=l;else if(2===e)r=p;else{if(4!==e)throw new TypeError("Unknown boolean type size: "+t);r=v}return this.fromWireType(r[n>>o])},O:null})},c:function(n,t,e,r,i,o,a,u,s,c,f,l,h){f=J(f),o=An(i,o),u&&(u=An(a,u)),c&&(c=An(s,c)),h=An(l,h);var p=V(f);vn(p,(function(){On("Cannot construct "+f+" due to unbound types",[r])})),$([n,t,e],r?[r]:[],(function(t){if(t=t[0],r)var e=t.G,i=e.P;else i=pn.prototype;t=D(p,(function(){if(Object.getPrototypeOf(this)!==a)throw new B("Use 'new' to construct "+f);if(void 0===s.R)throw new B(f+" has no accessible constructor");var n=s.R[arguments.length];if(void 0===n)throw new B("Tried to invoke ctor of "+f+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(s.R).toString()+") parameters instead!");return n.apply(this,arguments)}));var a=Object.create(i,{constructor:{value:t}});t.prototype=a;var s=new yn(f,t,a,h,e,o,u,c);s.J&&(void 0===s.J.X&&(s.J.X=[]),s.J.X.push(s)),e=new Tn(f,s,!0,!1),i=new Tn(f+"*",s,!1,!1);var l=new Tn(f+" const*",s,!1,!0);return on[n]={pointerType:i,ga:l},Cn(p,t),[e,i,l]}))},g:function(n,e,r,i,o,a,u){var s=Sn(r,i);e=J(e),a=An(o,a),$([],[n],(function(n){function i(){On("Cannot call "+o+" due to unbound types",s)}var o=(n=n[0]).name+"."+e;e.startsWith("@@")&&(e=Symbol[e.substring(2)]);var c=n.G.constructor;return void 0===c[e]?(i.T=r-1,c[e]=i):(dn(c,e,o),c[e].K[r-1]=i),$([],s,(function(i){if(i=En(o,[i[0],null].concat(i.slice(1)),null,a,u),void 0===c[e].K?(i.T=r-1,c[e]=i):c[e].K[r-1]=i,n.G.X)for(var s,f=t(n.G.X);!(s=f()).done;){var l=s.value;l.constructor.hasOwnProperty(e)||(l.constructor[e]=i)}return[]})),[]}))},h:function(n,t,e,r,i,o,a,u){t=J(t),o=An(i,o),$([],[n],(function(n){var i=(n=n[0]).name+"."+t,s={get:function(){On("Cannot access "+i+" due to unbound types",[e])},enumerable:!0,configurable:!0};return s.set=u?function(){On("Cannot access "+i+" due to unbound types",[e])}:function(){Z(i+" is a read-only property")},Object.defineProperty(n.G.constructor,t,s),$([],[e],(function(e){e=e[0];var i={get:function(){return e.fromWireType(o(r))},enumerable:!0};return u&&(u=An(a,u),i.set=function(n){var t=[];u(r,e.toWireType(t,n)),In(t)}),Object.defineProperty(n.G.constructor,t,i),[]})),[]}))},d:function(n,t,e,r,i,o){0<t||I();var a=Sn(t,e);i=An(r,i),$([],[n],(function(n){var e="constructor "+(n=n[0]).name;if(void 0===n.G.R&&(n.G.R=[]),void 0!==n.G.R[t-1])throw new B("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+n.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return n.G.R[t-1]=function(){On("Cannot construct "+n.name+" due to unbound types",a)},$([],a,(function(r){return r.splice(1,0,null),n.G.R[t-1]=En(e,r,null,i,o),[]})),[]}))},a:function(n,t,e,r,i,o,a,u){var s=Sn(e,r);t=J(t),o=An(i,o),$([],[n],(function(n){function r(){On("Cannot call "+i+" due to unbound types",s)}var i=(n=n[0]).name+"."+t;t.startsWith("@@")&&(t=Symbol[t.substring(2)]),u&&n.G.la.push(t);var c=n.G.P,f=c[t];return void 0===f||void 0===f.K&&f.className!==n.name&&f.T===e-2?(r.T=e-2,r.className=n.name,c[t]=r):(dn(c,t,i),c[t].K[e-2]=r),$([],s,(function(r){return r=En(i,r,n,o,a),void 0===c[t].K?(r.T=e-2,c[t]=r):c[t].K[e-2]=r,[]})),[]}))},b:function(n,t,e,r,i,o,a,u,s,c){t=J(t),i=An(r,i),$([],[n],(function(n){var r=(n=n[0]).name+"."+t,f={get:function(){On("Cannot access "+r+" due to unbound types",[e,a])},enumerable:!0,configurable:!0};return f.set=s?function(){On("Cannot access "+r+" due to unbound types",[e,a])}:function(){Z(r+" is a read-only property")},Object.defineProperty(n.G.P,t,f),$([],s?[e,a]:[e],(function(e){var a=e[0],f={get:function(){var t=Gn(this,n,r+" getter");return a.fromWireType(i(o,t))},enumerable:!0};if(s){s=An(u,s);var l=e[1];f.set=function(t){var e=Gn(this,n,r+" setter"),i=[];s(c,e,l.toWireType(i,t)),In(i)}}return Object.defineProperty(n.G.P,t,f),[]})),[]}))},s:function(n,t){q(n,{name:t=J(t),fromWireType:function(n){var t=Hn(n);return jn(n),t},toWireType:function(n,t){return Un(t)},argPackAdvance:8,readValueFromPointer:Fn,O:null})},o:function(n,t,e){e=_(e),q(n,{name:t=J(t),fromWireType:function(n){return n},toWireType:function(n,t){return t},argPackAdvance:8,readValueFromPointer:_n(t,e),O:null})},f:function(n,t,e,r,i){t=J(t),-1===i&&(i=4294967295),i=_(e);var o=function(n){return n};if(0===r){var a=32-8*e;o=function(n){return n<<a>>>a}}e=t.includes("unsigned")?function(n,t){return t>>>0}:function(n,t){return t},q(n,{name:t,fromWireType:o,toWireType:e,argPackAdvance:8,readValueFromPointer:Mn(t,i,0!==r),O:null})},x:function(n,t){var e="std::string"===(t=J(t));q(n,{name:t,fromWireType:function(n){var t=y[n>>2],r=n+4;if(e)for(var i=r,o=0;o<=t;++o){var a=r+o;if(o==t||0==h[a]){if(i=i?xn(i,a-i):"",void 0===u)var u=i;else u+=String.fromCharCode(0),u+=i;i=a+1}}else{for(u=Array(t),o=0;o<t;++o)u[o]=String.fromCharCode(h[r+o]);u=u.join("")}return qn(n),u},toWireType:function(n,t){t instanceof ArrayBuffer&&(t=new Uint8Array(t));var r,i,o="string"==typeof t;if(o||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||Z("Cannot pass non-string to std::string"),e&&o)for(r=i=0;r<t.length;++r){var a=t.charCodeAt(r);127>=a?i++:2047>=a?i+=2:55296<=a&&57343>=a?(i+=4,++r):i+=3}else i=t.length;if(a=(i=$n(4+(r=i)+1))+4,y[i>>2]=r,e&&o){if(o=a,a=r+1,r=h,0<a){a=o+a-1;for(var u=0;u<t.length;++u){var s=t.charCodeAt(u);if(55296<=s&&57343>=s&&(s=65536+((1023&s)<<10)|1023&t.charCodeAt(++u)),127>=s){if(o>=a)break;r[o++]=s}else{if(2047>=s){if(o+1>=a)break;r[o++]=192|s>>6}else{if(65535>=s){if(o+2>=a)break;r[o++]=224|s>>12}else{if(o+3>=a)break;r[o++]=240|s>>18,r[o++]=128|s>>12&63}r[o++]=128|s>>6&63}r[o++]=128|63&s}}r[o]=0}}else if(o)for(o=0;o<r;++o)255<(u=t.charCodeAt(o))&&(qn(a),Z("String has UTF-16 code units that do not fit in 8 bits")),h[a+o]=u;else for(o=0;o<r;++o)h[a+o]=t[o];return null!==n&&n.push(qn,i),i},argPackAdvance:8,readValueFromPointer:Fn,O:function(n){qn(n)}})},u:function(n,t){q(n,{ra:!0,name:t=J(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},j:function(n,t,e){n=Hn(n),t=Kn(t,"emval::as");var r=[],i=Un(r);return y[e>>2]=i,t.toWireType(r,n)},e:jn,k:function(n,t){return n=Hn(n),t=Hn(t),Un(n[t])},l:function(n){var t=Nn[n];return Un(void 0===t?J(n):t)},i:function(n){In(Hn(n)),jn(n)},p:function(n,t){return n=(n=Kn(n,"_emval_take_value")).readValueFromPointer(t),Un(n)},n:function(){I("")},m:function(n,t,e){var r;for(Vn.length=0,e>>=2;r=h[t++];)e+=105!=r&e,Vn.push(105==r?v[e]:g[e++>>1]),++e;return U[n].apply(null,Vn)},r:function(n){var t=h.length;if(2147483648<(n>>>=0))return!1;for(var e=1;4>=e;e*=2){var r=t*(1+.2/e);r=Math.min(r,n+100663296);var i=Math;r=Math.max(n,r);n:{i=i.min.call(i,2147483648,r+(65536-r%65536)%65536)-f.buffer.byteLength+65535>>>16;try{f.grow(i),w();var o=1;break n}catch(n){}o=void 0}if(o)return!0}return!1},w:Dn,v:zn};function $n(){return($n=r.asm.B).apply(null,arguments)}function qn(){return(qn=r.asm.C).apply(null,arguments)}function Qn(){return(Qn=r.asm.D).apply(null,arguments)}function nt(){function n(){if(!Xn&&(Xn=!0,r.calledRun=!0,!b)){if(L(C),i(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;){var n=r.postRun.shift();P.unshift(n)}L(P)}}if(!(0<W)){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)A();L(T),0<W||(r.setStatus?(r.setStatus("Running..."),setTimeout((function(){setTimeout((function(){r.setStatus("")}),1),n()}),1)):n())}}if(function(){function n(n){if(n=n.exports,r.asm=n,f=r.asm.y,w(),F=r.asm.A,C.unshift(r.asm.z),W--,r.monitorRunDependencies&&r.monitorRunDependencies(W),0==W&&O){var t=O;O=null,t()}return n}var t={a:Yn};if(W++,r.monitorRunDependencies&&r.monitorRunDependencies(W),r.instantiateWasm)try{return r.instantiateWasm(t,n)}catch(n){c("Module.instantiateWasm callback failed with error: "+n),o(n)}H(t,(function(t){n(t.instance)})).catch(o)}(),r.__embind_initialize_bindings=function(){return(r.__embind_initialize_bindings=r.asm.E).apply(null,arguments)},O=function n(){Xn||nt(),Xn||(O=n)},r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);0<r.preInit.length;)r.preInit.pop()();return nt(),n.ready}))}}}));
