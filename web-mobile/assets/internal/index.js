System.register("chunks:///_virtual/builtin-pipeline-settings.ts",["./rollupPluginModLoBabelHelpers.js","cc","./builtin-pipeline-types.ts"],(function(t){var e,o,i,n,r,s,a,p,l,g,c,y,u,m,d,b,h,f;return{setters:[function(t){e=t.applyDecoratedDescriptor,o=t.inheritsLoose,i=t.initializerDefineProperty,n=t.assertThisInitialized,r=t.createClass},function(t){s=t.cclegacy,a=t._decorator,p=t.Camera,l=t.CCBoolean,g=t.CCInteger,c=t.CCFloat,y=t.Material,u=t.Texture2D,m=t.rendering,d=t.Component},function(t){b=t.BloomType,h=t.fillRequiredPipelineSettings,f=t.makePipelineSettings}],execute:function(){var P,_,M,S,w,O,k,E,D,G,B,C,A,j,v,F,x,R,T,I,L,X,z,H,q,Y,N,Q,Z,J,K,U,V;s._RF.push({},"de1c2EHcMhAIYRZY5nyTQHG","builtin-pipeline-settings",void 0);var W=a.ccclass,$=a.disallowMultiple,tt=a.executeInEditMode,et=a.menu,ot=a.property,it=a.requireComponent,nt=a.type;t("BuiltinPipelineSettings",(P=W("BuiltinPipelineSettings"),_=et("Rendering/BuiltinPipelineSettings"),M=it(p),S=ot(l),w=ot({displayName:"Editor Preview (Experimental)",type:l}),O=ot({group:{id:"MSAA",name:"Multisample Anti-Aliasing"},type:l}),k=ot({group:{id:"MSAA",name:"Multisample Anti-Aliasing",style:"section"},type:g,range:[2,4,2]}),E=ot({group:{id:"ShadingScale",name:"ShadingScale",style:"section"},type:l}),D=ot({tooltip:"i18n:postprocess.shadingScale",group:{id:"ShadingScale",name:"ShadingScale"},type:c,range:[.01,4,.01],slide:!0}),G=ot({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:l}),B=nt(b),C=ot({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"}}),A=ot({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:y}),j=ot({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:y}),v=ot({tooltip:"i18n:bloom.enableAlphaMask",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:l}),F=ot({tooltip:"i18n:bloom.iterations",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:g,range:[1,6,1],slide:!0}),x=ot({tooltip:"i18n:bloom.threshold",group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"},type:c,min:0}),R=nt(c),T=ot({group:{id:"Bloom",name:"Bloom (PostProcessing)",style:"section"}}),I=ot({group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:l}),L=ot({group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:y}),X=ot({tooltip:"i18n:color_grading.contribute",group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:c,range:[0,1,.01],slide:!0}),z=ot({tooltip:"i18n:color_grading.originalMap",group:{id:"Color Grading",name:"ColorGrading (LDR) (PostProcessing)",style:"section"},type:u}),H=ot({group:{id:"FXAA",name:"Fast Approximate Anti-Aliasing (PostProcessing)",style:"section"},type:l}),q=ot({group:{id:"FXAA",name:"Fast Approximate Anti-Aliasing (PostProcessing)",style:"section"},type:y}),Y=ot({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:l}),N=ot({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:y}),Q=ot({group:{id:"FSR",name:"FidelityFX Super Resolution",style:"section"},type:c,range:[0,1,.01],slide:!0}),Z=ot({group:{id:"ToneMapping",name:"ToneMapping",style:"section"},type:y}),P(J=_(J=M(J=$(J=tt((U=e((K=function(t){function e(){for(var e,o=arguments.length,r=new Array(o),s=0;s<o;s++)r[s]=arguments[s];return e=t.call.apply(t,[this].concat(r))||this,i(e,"_settings",U,n(e)),i(e,"_editorPreview",V,n(e)),e}o(e,t);var s=e.prototype;return s.getPipelineSettings=function(){return this._settings},s.onEnable=function(){h(this._settings),this.getComponent(p).camera.pipelineSettings=this._settings},s.onDisable=function(){var t=this.getComponent(p).camera;t&&(t.pipelineSettings=null)},s._tryEnableEditorPreview=function(){void 0!==m&&(this._editorPreview?m.setEditorPipelineSettings(this._settings):this._disableEditorPreview())},s._disableEditorPreview=function(){void 0!==m&&(m.getEditorPipelineSettings()===this._settings&&m.setEditorPipelineSettings(null))},r(e,[{key:"editorPreview",get:function(){return this._editorPreview},set:function(t){this._editorPreview=t}},{key:"MsaaEnable",get:function(){return this._settings.msaa.enabled},set:function(t){this._settings.msaa.enabled=t}},{key:"msaaSampleCount",get:function(){return this._settings.msaa.sampleCount},set:function(t){t=Math.pow(2,Math.ceil(Math.log2(Math.max(t,2)))),t=Math.min(t,4),this._settings.msaa.sampleCount=t}},{key:"shadingScaleEnable",get:function(){return this._settings.enableShadingScale},set:function(t){this._settings.enableShadingScale=t}},{key:"shadingScale",get:function(){return this._settings.shadingScale},set:function(t){this._settings.shadingScale=t}},{key:"bloomEnable",get:function(){return this._settings.bloom.enabled},set:function(t){this._settings.bloom.enabled=t}},{key:"bloomType",get:function(){return this._settings.bloom.type},set:function(t){this._settings.bloom.type=t}},{key:"kawaseBloomMaterial",get:function(){return this._settings.bloom.kawaseFilterMaterial},set:function(t){this._settings.bloom.kawaseFilterMaterial!==t&&(this._settings.bloom.kawaseFilterMaterial=t)}},{key:"mipmapBloomMaterial",get:function(){return this._settings.bloom.mipmapFilterMaterial},set:function(t){this._settings.bloom.mipmapFilterMaterial!==t&&(this._settings.bloom.mipmapFilterMaterial=t)}},{key:"bloomEnableAlphaMask",get:function(){return this._settings.bloom.enableAlphaMask},set:function(t){this._settings.bloom.enableAlphaMask=t}},{key:"bloomIterations",get:function(){return this._settings.bloom.iterations},set:function(t){this._settings.bloom.iterations=t}},{key:"bloomThreshold",get:function(){return this._settings.bloom.threshold},set:function(t){this._settings.bloom.threshold=t}},{key:"bloomIntensity",get:function(){return this._settings.bloom.intensity},set:function(t){this._settings.bloom.intensity=t}},{key:"colorGradingEnable",get:function(){return this._settings.colorGrading.enabled},set:function(t){this._settings.colorGrading.enabled=t}},{key:"colorGradingMaterial",get:function(){return this._settings.colorGrading.material},set:function(t){this._settings.colorGrading.material!==t&&(this._settings.colorGrading.material=t)}},{key:"colorGradingContribute",get:function(){return this._settings.colorGrading.contribute},set:function(t){this._settings.colorGrading.contribute=t}},{key:"colorGradingMap",get:function(){return this._settings.colorGrading.colorGradingMap},set:function(t){this._settings.colorGrading.colorGradingMap=t}},{key:"fxaaEnable",get:function(){return this._settings.fxaa.enabled},set:function(t){this._settings.fxaa.enabled=t}},{key:"fxaaMaterial",get:function(){return this._settings.fxaa.material},set:function(t){this._settings.fxaa.material!==t&&(this._settings.fxaa.material=t)}},{key:"fsrEnable",get:function(){return this._settings.fsr.enabled},set:function(t){this._settings.fsr.enabled=t}},{key:"fsrMaterial",get:function(){return this._settings.fsr.material},set:function(t){this._settings.fsr.material!==t&&(this._settings.fsr.material=t)}},{key:"fsrSharpness",get:function(){return this._settings.fsr.sharpness},set:function(t){this._settings.fsr.sharpness=t}},{key:"toneMappingMaterial",get:function(){return this._settings.toneMapping.material},set:function(t){this._settings.toneMapping.material!==t&&(this._settings.toneMapping.material=t)}}]),e}(d)).prototype,"_settings",[ot],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return f()}}),V=e(K.prototype,"_editorPreview",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),e(K.prototype,"editorPreview",[w],Object.getOwnPropertyDescriptor(K.prototype,"editorPreview"),K.prototype),e(K.prototype,"MsaaEnable",[O],Object.getOwnPropertyDescriptor(K.prototype,"MsaaEnable"),K.prototype),e(K.prototype,"msaaSampleCount",[k],Object.getOwnPropertyDescriptor(K.prototype,"msaaSampleCount"),K.prototype),e(K.prototype,"shadingScaleEnable",[E],Object.getOwnPropertyDescriptor(K.prototype,"shadingScaleEnable"),K.prototype),e(K.prototype,"shadingScale",[D],Object.getOwnPropertyDescriptor(K.prototype,"shadingScale"),K.prototype),e(K.prototype,"bloomEnable",[G],Object.getOwnPropertyDescriptor(K.prototype,"bloomEnable"),K.prototype),e(K.prototype,"bloomType",[B,C],Object.getOwnPropertyDescriptor(K.prototype,"bloomType"),K.prototype),e(K.prototype,"kawaseBloomMaterial",[A],Object.getOwnPropertyDescriptor(K.prototype,"kawaseBloomMaterial"),K.prototype),e(K.prototype,"mipmapBloomMaterial",[j],Object.getOwnPropertyDescriptor(K.prototype,"mipmapBloomMaterial"),K.prototype),e(K.prototype,"bloomEnableAlphaMask",[v],Object.getOwnPropertyDescriptor(K.prototype,"bloomEnableAlphaMask"),K.prototype),e(K.prototype,"bloomIterations",[F],Object.getOwnPropertyDescriptor(K.prototype,"bloomIterations"),K.prototype),e(K.prototype,"bloomThreshold",[x],Object.getOwnPropertyDescriptor(K.prototype,"bloomThreshold"),K.prototype),e(K.prototype,"bloomIntensity",[R,T],Object.getOwnPropertyDescriptor(K.prototype,"bloomIntensity"),K.prototype),e(K.prototype,"colorGradingEnable",[I],Object.getOwnPropertyDescriptor(K.prototype,"colorGradingEnable"),K.prototype),e(K.prototype,"colorGradingMaterial",[L],Object.getOwnPropertyDescriptor(K.prototype,"colorGradingMaterial"),K.prototype),e(K.prototype,"colorGradingContribute",[X],Object.getOwnPropertyDescriptor(K.prototype,"colorGradingContribute"),K.prototype),e(K.prototype,"colorGradingMap",[z],Object.getOwnPropertyDescriptor(K.prototype,"colorGradingMap"),K.prototype),e(K.prototype,"fxaaEnable",[H],Object.getOwnPropertyDescriptor(K.prototype,"fxaaEnable"),K.prototype),e(K.prototype,"fxaaMaterial",[q],Object.getOwnPropertyDescriptor(K.prototype,"fxaaMaterial"),K.prototype),e(K.prototype,"fsrEnable",[Y],Object.getOwnPropertyDescriptor(K.prototype,"fsrEnable"),K.prototype),e(K.prototype,"fsrMaterial",[N],Object.getOwnPropertyDescriptor(K.prototype,"fsrMaterial"),K.prototype),e(K.prototype,"fsrSharpness",[Q],Object.getOwnPropertyDescriptor(K.prototype,"fsrSharpness"),K.prototype),e(K.prototype,"toneMappingMaterial",[Z],Object.getOwnPropertyDescriptor(K.prototype,"toneMappingMaterial"),K.prototype),J=K))||J)||J)||J)||J)||J));s._RF.pop()}}}));

System.register("chunks:///_virtual/builtin-pipeline-types.ts",["cc"],(function(e){var a,l,n;return{setters:[function(e){a=e.cclegacy,l=e.gfx,n=e.ccenum}],execute:function(){e({fillRequiredBloom:d,fillRequiredColorGrading:p,fillRequiredFSR:c,fillRequiredFXAA:f,fillRequiredHBAO:function(e){void 0===e.enabled&&(e.enabled=!1);void 0===e.radiusScale&&(e.radiusScale=1);void 0===e.angleBiasDegree&&(e.angleBiasDegree=10);void 0===e.blurSharpness&&(e.blurSharpness=3);void 0===e.aoSaturation&&(e.aoSaturation=1);void 0===e.needBlur&&(e.needBlur=!1)},fillRequiredMSAA:t,fillRequiredPipelineSettings:function(e){e.msaa?t(e.msaa):e.msaa=r();void 0===e.enableShadingScale&&(e.enableShadingScale=!1);void 0===e.shadingScale&&(e.shadingScale=.5);e.bloom?d(e.bloom):e.bloom=u();e.toneMapping?v(e.toneMapping):e.toneMapping={material:null};e.colorGrading?p(e.colorGrading):e.colorGrading={enabled:!1,material:null,contribute:1,colorGradingMap:null};e.fsr?c(e.fsr):e.fsr={enabled:!1,material:null,sharpness:.8};e.fxaa?f(e.fxaa):e.fxaa={enabled:!1,material:null}},fillRequiredToneMapping:v,makeBloom:u,makeColorGrading:s,makeFSR:m,makeFXAA:b,makeHBAO:function(){return{enabled:!1,radiusScale:1,angleBiasDegree:10,blurSharpness:3,aoSaturation:1,needBlur:!1}},makeMSAA:r,makePipelineSettings:function(){return{msaa:r(),enableShadingScale:!1,shadingScale:.5,bloom:u(),toneMapping:{material:null},colorGrading:{enabled:!1,material:null,contribute:1,colorGradingMap:null},fsr:{enabled:!1,material:null,sharpness:.8},fxaa:{enabled:!1,material:null}}},makeToneMapping:g}),a._RF.push({},"cbf30kCUX9A3K+QpVC6wnzx","builtin-pipeline-types",void 0);var i=l.SampleCount;function r(){return{enabled:!1,sampleCount:i.X4}}function t(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.sampleCount&&(e.sampleCount=i.X4)}var o=e("BloomType",function(e){return e[e.KawaseDualFilter=0]="KawaseDualFilter",e[e.MipmapFilter=1]="MipmapFilter",e}({}));function u(){return{enabled:!1,type:o.KawaseDualFilter,material:null,kawaseFilterMaterial:null,mipmapFilterMaterial:null,enableAlphaMask:!1,iterations:3,threshold:.8,intensity:1}}function d(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.type&&(e.type=o.KawaseDualFilter),void 0===e.material&&(e.material=null),void 0===e.kawaseFilterMaterial&&(e.kawaseFilterMaterial=e.material||null),void 0===e.mipmapFilterMaterial&&(e.mipmapFilterMaterial=null),void 0===e.enableAlphaMask&&(e.enableAlphaMask=!1),void 0===e.iterations&&(e.iterations=3),void 0===e.threshold&&(e.threshold=.8),void 0===e.intensity&&(e.intensity=1)}function s(){return{enabled:!1,material:null,contribute:1,colorGradingMap:null}}function p(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null),void 0===e.contribute&&(e.contribute=1),void 0===e.colorGradingMap&&(e.colorGradingMap=null)}function m(){return{enabled:!1,material:null,sharpness:.8}}function c(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null),void 0===e.sharpness&&(e.sharpness=.8)}function b(){return{enabled:!1,material:null}}function f(e){void 0===e.enabled&&(e.enabled=!1),void 0===e.material&&(e.material=null)}function g(){return{material:null}}function v(e){void 0===e.material&&(e.material=null)}n(o),a._RF.pop()}}}));

System.register("chunks:///_virtual/builtin-pipeline.ts",["./rollupPluginModLoBabelHelpers.js","cc","./builtin-pipeline-types.ts"],(function(e){var a,t,i,r,s,n,o,d,l,h,c,u,p,g,m,S,f,_;return{setters:[function(e){a=e.createForOfIteratorHelperLoose},function(e){t=e.cclegacy,i=e.geometry,r=e.gfx,s=e.renderer,n=e.Vec2,o=e.Vec4,d=e.rendering,l=e.assert,h=e.clamp,c=e.Vec3,u=e.Material,p=e.Layers,g=e.PipelineEventType,m=e.sys,S=e.pipeline},function(e){f=e.makePipelineSettings,_=e.BloomType}],execute:function(){e("getPingPongRenderTarget",G),t._RF.push({},"ff9b0GZzgRM/obMbHGfCNbk","builtin-pipeline",void 0);var w=i.AABB,b=i.Sphere,P=i.intersect,T=r.ClearFlagBit,R=r.Color,M=r.Format,v=r.FormatFeatureBit,E=r.LoadOp,C=r.StoreOp,x=r.TextureType,A=r.Viewport,L=s.scene,D=L.CameraUsage,F=L.CSMLevel,N=L.LightType;function O(e){return!!(e.clearFlag&(T.COLOR|T.STENCIL<<1))}function y(e,a,t,i,r,s){e.shadowFixedArea||e.csmLevel===F.LEVEL_1?(r.left=0,r.top=0,r.width=Math.trunc(a),r.height=Math.trunc(t)):(r.left=Math.trunc(i%2*.5*a),r.top=s>0?Math.trunc(.5*(1-Math.floor(i/2))*t):Math.trunc(.5*Math.floor(i/2)*t),r.width=Math.trunc(.5*a),r.height=Math.trunc(.5*t)),r.left=Math.max(0,r.left),r.top=Math.max(0,r.top),r.width=Math.max(1,r.width),r.height=Math.max(1,r.height)}var Q=e("PipelineConfigs",(function(){this.isWeb=!1,this.isWebGL1=!1,this.isWebGPU=!1,this.isMobile=!1,this.isHDR=!1,this.useFloatOutput=!1,this.toneMappingType=0,this.shadowEnabled=!1,this.shadowMapFormat=M.R32F,this.shadowMapSize=new n(1,1),this.usePlanarShadow=!1,this.screenSpaceSignY=1,this.supportDepthSample=!1,this.mobileMaxSpotLightShadowMaps=1,this.platform=new o(0,0,0,0)}));function B(e,a){var t=v.SAMPLED_TEXTURE|v.LINEAR_FILTER,i=e.device;a.isWeb=!m.isNative,a.isWebGL1=i.gfxAPI===r.API.WEBGL,a.isWebGPU=i.gfxAPI===r.API.WEBGPU,a.isMobile=m.isMobile,a.isHDR=e.pipelineSceneData.isHDR,a.useFloatOutput=e.getMacroBool("CC_USE_FLOAT_OUTPUT"),a.toneMappingType=e.pipelineSceneData.postSettings.toneMappingType;var n=e.pipelineSceneData.shadows;a.shadowEnabled=n.enabled,a.shadowMapFormat=S.supportsR32FloatTexture(e.device)?M.R32F:M.RGBA8,a.shadowMapSize.set(n.size),a.usePlanarShadow=n.enabled&&n.type===s.scene.ShadowType.Planar,a.screenSpaceSignY=e.device.capabilities.screenSpaceSignY,a.supportDepthSample=(e.device.getFormatFeatures(M.DEPTH_STENCIL)&t)===t;var o=i.capabilities.screenSpaceSignY;a.platform.x=a.isMobile?1:0,a.platform.w=.5*o+.5<<1|.5*i.capabilities.clipSpaceSignY+.5}var H=f(),z=e("CameraConfigs",(function(){this.settings=H,this.isMainGameWindow=!1,this.renderWindowId=0,this.colorName="",this.depthStencilName="",this.enableFullPipeline=!1,this.enableProfiler=!1,this.remainingPasses=0,this.enableShadingScale=!1,this.shadingScale=1,this.nativeWidth=1,this.nativeHeight=1,this.width=1,this.height=1,this.enableHDR=!1,this.radianceFormat=r.Format.RGBA8,this.copyAndTonemapMaterial=null,this.enableStoreSceneDepth=!1})),I=new R(0,0,0,0);function W(e,a,t,i){l(!!t.copyAndTonemapMaterial);var r=e.addRenderPass(t.nativeWidth,t.nativeHeight,"cc-tone-mapping");return r.addRenderTarget(t.colorName,E.CLEAR,C.STORE,I),r.addTexture(i,"inputTexture"),r.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(t.copyAndTonemapMaterial,1),r}function G(e,a,t){return e.startsWith(a)?""+a+(1-Number(e.charAt(a.length)))+"_"+t:a+"0_"+t}var U=function(){function e(){this.lights=[],this.shadowEnabledSpotLights=[],this._sphere=b.create(0,0,0,1),this._boundingBox=new w,this._rangedDirLightBoundingBox=new w(0,0,0,.5,.5,.5)}var t=e.prototype;return t.cullLights=function(e,t,i){this.lights.length=0,this.shadowEnabledSpotLights.length=0;for(var r,s=a(e.spotLights);!(r=s()).done;){var n=r.value;n.baked||(b.set(this._sphere,n.position.x,n.position.y,n.position.z,n.range),P.sphereFrustum(this._sphere,t)&&(n.shadowEnabled?this.shadowEnabledSpotLights.push(n):this.lights.push(n)))}for(var o,d=a(e.sphereLights);!(o=d()).done;){var l=o.value;l.baked||(b.set(this._sphere,l.position.x,l.position.y,l.position.z,l.range),P.sphereFrustum(this._sphere,t)&&this.lights.push(l))}for(var h,u=a(e.pointLights);!(h=u()).done;){var p=h.value;p.baked||(b.set(this._sphere,p.position.x,p.position.y,p.position.z,p.range),P.sphereFrustum(this._sphere,t)&&this.lights.push(p))}for(var g,m=a(e.rangedDirLights);!(g=m()).done;){var S=g.value;w.transform(this._boundingBox,this._rangedDirLightBoundingBox,S.node.getWorldMatrix()),P.aabbFrustum(this._boundingBox,t)&&this.lights.push(S)}i&&this.shadowEnabledSpotLights.sort((function(e,a){return c.squaredDistance(i,e.position)-c.squaredDistance(i,a.position)}))},t._addLightQueues=function(e,t){for(var i,r=a(this.lights);!(i=r()).done;){var s=i.value,n=t.addQueue(d.QueueHint.BLEND,"forward-add");switch(s.type){case N.SPHERE:n.name="sphere-light";break;case N.SPOT:n.name="spot-light";break;case N.POINT:n.name="point-light";break;case N.RANGED_DIRECTIONAL:n.name="ranged-directional-light";break;default:n.name="unknown-light"}n.addScene(e,d.SceneFlags.BLEND,s)}},t.addSpotlightShadowPasses=function(e,t,i){for(var r,s=0,n=a(this.shadowEnabledSpotLights);!(r=n()).done;){var o=r.value,l=e.pipelineSceneData.shadows.size,h=e.addRenderPass(l.x,l.y,"default");if(h.name="SpotLightShadowPass"+s,h.addRenderTarget("SpotShadowMap"+s,E.CLEAR,C.STORE,new R(1,1,1,1)),h.addDepthStencil("SpotShadowDepth"+s,E.CLEAR,C.DISCARD),h.addQueue(d.QueueHint.NONE,"shadow-caster").addScene(t,d.SceneFlags.OPAQUE|d.SceneFlags.MASK|d.SceneFlags.SHADOW_CASTER).useLightFrustum(o),++s>=i)break}},t.addLightQueues=function(e,t,i){this._addLightQueues(t,e);for(var r,s=0,n=a(this.shadowEnabledSpotLights);!(r=n()).done;){var o=r.value;if(e.addTexture("SpotShadowMap"+s,"cc_spotShadowMap"),e.addQueue(d.QueueHint.BLEND,"forward-add").addScene(t,d.SceneFlags.BLEND,o),++s>=i)break}},t.addLightPasses=function(e,t,i,r,s,n,o,l,h,c){this._addLightQueues(o,c);for(var u,p=0,g=h.pipelineSceneData.shadows.size,m=a(this.shadowEnabledSpotLights);!(u=m()).done;){var S=u.value,f=h.addRenderPass(g.x,g.y,"default");f.name="SpotlightShadowPass",f.addRenderTarget("ShadowMap"+r,E.CLEAR,C.STORE,new R(1,1,1,1)),f.addDepthStencil("ShadowDepth"+r,E.CLEAR,C.DISCARD),f.addQueue(d.QueueHint.NONE,"shadow-caster").addScene(o,d.SceneFlags.OPAQUE|d.SceneFlags.MASK|d.SceneFlags.SHADOW_CASTER).useLightFrustum(S);var _=++p===this.shadowEnabledSpotLights.length?i:C.STORE;(c=h.addRenderPass(s,n,"default")).name="SpotlightWithShadowMap",c.setViewport(l),c.addRenderTarget(e,E.LOAD),c.addDepthStencil(t,E.LOAD,_),c.addTexture("ShadowMap"+r,"cc_spotShadowMap"),c.addQueue(d.QueueHint.BLEND,"forward-add").addScene(o,d.SceneFlags.BLEND,S)}return c},t.isMultipleLightPassesNeeded=function(){return this.shadowEnabledSpotLights.length>0},e}(),V=e("BuiltinForwardPassBuilder",function(){function e(){this.forwardLighting=new U,this._viewport=new A,this._clearColor=new R(0,0,0,1),this._reflectionProbeClearColor=new c(0,0,0)}var i=e.prototype;return i.getConfigOrder=function(){return e.ConfigOrder},i.getRenderOrder=function(){return e.RenderOrder},i.configCamera=function(e,a,t){t.enableMainLightShadowMap=a.shadowEnabled&&!a.usePlanarShadow&&!!e.scene&&!!e.scene.mainLight&&e.scene.mainLight.shadowEnabled,t.enableMainLightPlanarShadowMap=a.shadowEnabled&&a.usePlanarShadow&&!!e.scene&&!!e.scene.mainLight&&e.scene.mainLight.shadowEnabled,t.enablePlanarReflectionProbe=t.isMainGameWindow||e.cameraUsage===D.SCENE_VIEW||e.cameraUsage===D.GAME_VIEW,t.enableMSAA=t.settings.msaa.enabled&&!t.enableStoreSceneDepth&&!a.isWeb&&!a.isWebGL1,t.enableSingleForwardPass=a.isMobile||t.enableMSAA,++t.remainingPasses},i.windowResize=function(e,a,t,i,r,s,n){var o=d.ResourceFlags,l=d.ResourceResidency,h=i.renderWindowId,c=t.settings,u=t.enableShadingScale?Math.max(Math.floor(s*t.shadingScale),1):s,p=t.enableShadingScale?Math.max(Math.floor(n*t.shadingScale),1):n;if(t.enableMSAA&&(t.enableHDR?e.addTexture("MsaaRadiance"+h,x.TEX2D,t.radianceFormat,u,p,1,1,1,c.msaa.sampleCount,o.COLOR_ATTACHMENT,l.MEMORYLESS):e.addTexture("MsaaRadiance"+h,x.TEX2D,M.RGBA8,u,p,1,1,1,c.msaa.sampleCount,o.COLOR_ATTACHMENT,l.MEMORYLESS),e.addTexture("MsaaDepthStencil"+h,x.TEX2D,M.DEPTH_STENCIL,u,p,1,1,1,c.msaa.sampleCount,o.DEPTH_STENCIL_ATTACHMENT,l.MEMORYLESS)),e.addRenderTarget("ShadowMap"+h,a.shadowMapFormat,a.shadowMapSize.x,a.shadowMapSize.y),e.addDepthStencil("ShadowDepth"+h,M.DEPTH_STENCIL,a.shadowMapSize.x,a.shadowMapSize.y),t.enableSingleForwardPass)for(var g=a.mobileMaxSpotLightShadowMaps,m=0;m!==g;++m)e.addRenderTarget("SpotShadowMap"+m,a.shadowMapFormat,a.shadowMapSize.x,a.shadowMapSize.y),e.addDepthStencil("SpotShadowDepth"+m,M.DEPTH_STENCIL,a.shadowMapSize.x,a.shadowMapSize.y)},i.setup=function(e,a,t,i,r){e.setVec4("g_platform",a.platform);var s=i.window.renderWindowId,n=i.scene,o=n.mainLight;--t.remainingPasses,l(t.remainingPasses>=0),this.forwardLighting.cullLights(n,i.frustum),t.enableMainLightShadowMap&&(l(!!o),this._addCascadedShadowMapPass(e,a,s,o,i)),t.enableSingleForwardPass&&this.forwardLighting.addSpotlightShadowPasses(e,i,a.mobileMaxSpotLightShadowMaps),this._tryAddReflectionProbePasses(e,t,s,o,i.scene),t.remainingPasses>0||t.enableShadingScale?(r.colorName=t.enableShadingScale?"ScaledRadiance0_"+s:"Radiance0_"+s,r.depthStencilName=t.enableShadingScale?"ScaledSceneDepth_"+s:"SceneDepth_"+s):(r.colorName=t.colorName,r.depthStencilName=t.depthStencilName);var d=this._addForwardRadiancePasses(e,a,t,s,i,t.width,t.height,o,r.colorName,r.depthStencilName,!t.enableMSAA,t.enableStoreSceneDepth?C.STORE:C.DISCARD);return t.enableStoreSceneDepth||(r.depthStencilName=""),0===t.remainingPasses&&t.enableShadingScale?W(e,0,t,r.colorName):d},i._addCascadedShadowMapPass=function(e,a,t,i,r){var s=d.QueueHint,n=d.SceneFlags,o=e.pipelineSceneData.shadows.size,l=o.x,h=o.y,c=this._viewport;c.left=c.top=0,c.width=l,c.height=h;var u=e.addRenderPass(l,h,"default");u.name="CascadedShadowMap",u.addRenderTarget("ShadowMap"+t,E.CLEAR,C.STORE,new R(1,1,1,1)),u.addDepthStencil("ShadowDepth"+t,E.CLEAR,C.DISCARD);for(var p=e.pipelineSceneData.csmSupported?i.csmLevel:1,g=0;g!==p;++g){y(i,l,h,g,this._viewport,a.screenSpaceSignY);var m=u.addQueue(s.NONE,"shadow-caster");a.isWebGPU||m.setViewport(this._viewport),m.addScene(r,n.OPAQUE|n.MASK|n.SHADOW_CASTER).useLightFrustum(i,g)}},i._tryAddReflectionProbePasses=function(e,i,n,o,l){var h=t.internal.reflectionProbeManager;if(h)for(var c,u=d.ResourceResidency,p=h.getProbes(),g=0,m=a(p);!(c=m()).done;){var S=c.value;if(S.needRender){var f=S.renderArea(),_=Math.max(Math.floor(f.x),1),w=Math.max(Math.floor(f.y),1);if(S.probeType===s.scene.ProbeType.PLANAR){if(!i.enablePlanarReflectionProbe)continue;var b=S.realtimePlanarTexture.window,P="PlanarProbeRT"+g,T="PlanarProbeDS"+g;e.addRenderWindow(P,i.radianceFormat,_,w,b),e.addDepthStencil(T,r.Format.DEPTH_STENCIL,_,w,u.MEMORYLESS);var R=e.addRenderPass(_,w,"default");R.name="PlanarReflectionProbe"+g,this._buildReflectionProbePass(R,i,n,S.camera,P,T,o,l)}if(4===++g)break}}},i._buildReflectionProbePass=function(e,a,t,i,r,s,n,o){void 0===o&&(o=null);var l=d.QueueHint,h=d.SceneFlags,c=a.enableMSAA?C.DISCARD:C.STORE;if(O(i)){this._reflectionProbeClearColor.x=i.clearColor.x,this._reflectionProbeClearColor.y=i.clearColor.y,this._reflectionProbeClearColor.z=i.clearColor.z;var u=d.packRGBE(this._reflectionProbeClearColor);this._clearColor.x=u.x,this._clearColor.y=u.y,this._clearColor.z=u.z,this._clearColor.w=u.w,e.addRenderTarget(r,E.CLEAR,c,this._clearColor)}else e.addRenderTarget(r,E.LOAD,c);i.clearFlag&T.DEPTH_STENCIL?e.addDepthStencil(s,E.CLEAR,C.DISCARD,i.clearDepth,i.clearStencil,i.clearFlag&T.DEPTH_STENCIL):e.addDepthStencil(s,E.LOAD,C.DISCARD),a.enableMainLightShadowMap&&e.addTexture("ShadowMap"+t,"cc_shadowMap"),e.addQueue(l.NONE,"reflect-map").addScene(i,h.OPAQUE|h.MASK|h.REFLECTION_PROBE,n||void 0,o||void 0)},i._addForwardRadiancePasses=function(e,a,t,i,r,s,n,o,h,c,u,p){void 0===u&&(u=!1),void 0===p&&(p=C.DISCARD);var g=d.QueueHint,m=d.SceneFlags,S=r.clearColor;this._clearColor.x=S.x,this._clearColor.y=S.y,this._clearColor.z=S.z,this._clearColor.w=S.w;var f=r.viewport;this._viewport.left=Math.round(f.x*s),this._viewport.top=Math.round(f.y*n),this._viewport.width=Math.max(Math.round(f.width*s),1),this._viewport.height=Math.max(Math.round(f.height*n),1);var _=!u&&t.enableMSAA;l(!_||t.enableSingleForwardPass);var w=t.enableSingleForwardPass?this._addForwardSingleRadiancePass(e,a,t,i,r,_,s,n,o,h,c,p):this._addForwardMultipleRadiancePasses(e,t,i,r,s,n,o,h,c,p);t.enableMainLightPlanarShadowMap&&this._addPlanarShadowQueue(r,o,w);var b=m.BLEND|(r.geometryRenderer?m.GEOMETRY:m.NONE);return w.addQueue(g.BLEND).addScene(r,b,o||void 0),w},i._addForwardSingleRadiancePass=function(e,a,t,i,r,s,n,o,d,h,c,u){var p;if(l(t.enableSingleForwardPass),s){var g="MsaaRadiance"+i,m="MsaaDepthStencil"+i,S=t.settings.msaa.sampleCount,f=e.addMultisampleRenderPass(n,o,S,0,"default");f.name="MsaaForwardPass",this._buildForwardMainLightPass(f,t,i,r,g,m,C.DISCARD,d),f.resolveRenderTarget(g,h),p=f}else(p=e.addRenderPass(n,o,"default")).name="ForwardPass",this._buildForwardMainLightPass(p,t,i,r,h,c,u,d);return l(void 0!==p),this.forwardLighting.addLightQueues(p,r,a.mobileMaxSpotLightShadowMaps),p},i._addForwardMultipleRadiancePasses=function(e,a,t,i,r,s,n,o,d,h){l(!a.enableSingleForwardPass);var c=e.addRenderPass(r,s,"default");c.name="ForwardPass";var u=this.forwardLighting.isMultipleLightPassesNeeded()?C.STORE:h;return this._buildForwardMainLightPass(c,a,t,i,o,d,u,n),c=this.forwardLighting.addLightPasses(o,d,h,t,r,s,i,this._viewport,e,c)},i._buildForwardMainLightPass=function(e,a,t,i,r,s,n,o,l){void 0===l&&(l=null);var h=d.QueueHint,c=d.SceneFlags;e.setViewport(this._viewport);var u=a.enableMSAA?C.DISCARD:C.STORE;O(i)?e.addRenderTarget(r,E.CLEAR,u,this._clearColor):e.addRenderTarget(r,E.LOAD,u),i.clearFlag&T.DEPTH_STENCIL?e.addDepthStencil(s,E.CLEAR,n,i.clearDepth,i.clearStencil,i.clearFlag&T.DEPTH_STENCIL):e.addDepthStencil(s,E.LOAD,n),a.enableMainLightShadowMap&&e.addTexture("ShadowMap"+t,"cc_shadowMap"),e.addQueue(h.NONE).addScene(i,c.OPAQUE|c.MASK,o||void 0,l||void 0)},i._addPlanarShadowQueue=function(e,a,t){var i=d.QueueHint,r=d.SceneFlags;t.addQueue(i.BLEND,"planar-shadow").addScene(e,r.SHADOW_CASTER|r.PLANAR_SHADOW|r.BLEND,a||void 0)},e}());function k(e,a){return Math.max(Math.floor(e*a),1)}V.ConfigOrder=100,V.RenderOrder=100;var Y=e("BuiltinBloomPassBuilder",function(){function e(){this._clearColorTransparentBlack=new R(0,0,0,0),this._bloomParams=new o(0,0,0,0),this._bloomTexSize=new o(0,0,0,0),this._bloomWidths=[],this._bloomHeights=[],this._bloomTexNames=[],this._bloomUpSampleTexDescs=[],this._bloomDownSampleTexDescs=[],this._prefilterTexDesc={name:"",width:0,height:0},this._originalColorDesc={name:"",width:0,height:0}}var a=e.prototype;return a.getConfigOrder=function(){return 0},a.getRenderOrder=function(){return 200},a.configCamera=function(e,a,t){var i=t.settings.bloom,r=i.type===_.KawaseDualFilter&&!!i.kawaseFilterMaterial||i.type===_.MipmapFilter&&!!i.mipmapFilterMaterial;t.enableBloom=i.enabled&&r,t.enableBloom&&++t.remainingPasses},a.windowResize=function(e,a,t,i){if(t.enableBloom){var r=t.width,s=t.height,n=t.settings.bloom,o=i.renderWindowId,d=t.radianceFormat;if(n.type===_.KawaseDualFilter)for(var l=t.width,h=t.height,c=0;c!==n.iterations+1;++c)l=Math.max(Math.floor(l/2),1),h=Math.max(Math.floor(h/2),1),e.addRenderTarget("BloomTex"+o+"_"+c,d,l,h);else if(n.type===_.MipmapFilter){for(var u=n.iterations,p=0;p!==u+1;++p){if(p<u){var g=Math.pow(.5,p+2);this._bloomDownSampleTexDescs[p]=this.createTexture(e,"DownSampleColor"+o+p,k(r,g),k(s,g),d)}if(p<u-1){var m=Math.pow(.5,u-p-1);this._bloomUpSampleTexDescs[p]=this.createTexture(e,"UpSampleColor"+o+p,k(r,m),k(s,m),d)}}this._originalColorDesc=this.createTexture(e,"OriginalColor"+o,r,s,d),this._prefilterTexDesc=this.createTexture(e,"PrefilterColor"+o,k(r,.5),k(s,.5),d)}}},a.createTexture=function(e,a,t,i,r){var s={name:a,width:t,height:i};return e.addRenderTarget(s.name,r,s.width,s.height),s},a.setup=function(e,a,t,i,r,s){if(!t.enableBloom)return s;--t.remainingPasses,l(t.remainingPasses>=0);var n=t.settings.bloom,o=i.window.renderWindowId;switch(n.type){case _.KawaseDualFilter:var d=n.kawaseFilterMaterial;return l(!!d),this._addKawaseDualFilterBloomPasses(e,a,t,t.settings,d,o,t.width,t.height,r.colorName);case _.MipmapFilter:var h=n.mipmapFilterMaterial;return l(!!h),this._addMipmapFilterBloomPasses(e,a,t,t.settings,h,o,t.width,t.height,r.colorName);default:return s}},a._addKawaseDualFilterBloomPasses=function(e,a,t,i,r,s,n,o,l){var h=d.QueueHint,c=i.bloom.iterations,u=c+1;this._bloomWidths.length=u,this._bloomHeights.length=u,this._bloomWidths[0]=Math.max(Math.floor(n/2),1),this._bloomHeights[0]=Math.max(Math.floor(o/2),1);for(var p=1;p!==u;++p)this._bloomWidths[p]=Math.max(Math.floor(this._bloomWidths[p-1]/2),1),this._bloomHeights[p]=Math.max(Math.floor(this._bloomHeights[p-1]/2),1);this._bloomTexNames.length=u;for(var g=0;g!==u;++g)this._bloomTexNames[g]="BloomTex"+s+"_"+g;this._bloomParams.x=a.useFloatOutput?1:0,this._bloomParams.y=0,this._bloomParams.z=i.bloom.threshold,this._bloomParams.w=i.bloom.enableAlphaMask?1:0;var m=e.addRenderPass(this._bloomWidths[0],this._bloomHeights[0],"cc-bloom-prefilter");m.addRenderTarget(this._bloomTexNames[0],E.CLEAR,C.STORE,this._clearColorTransparentBlack),m.addTexture(l,"inputTexture"),m.setVec4("bloomParams",this._bloomParams),m.addQueue(h.OPAQUE).addFullscreenQuad(r,0);for(var S=1;S!==u;++S){var f=e.addRenderPass(this._bloomWidths[S],this._bloomHeights[S],"cc-bloom-downsample");f.addRenderTarget(this._bloomTexNames[S],E.CLEAR,C.STORE,this._clearColorTransparentBlack),f.addTexture(this._bloomTexNames[S-1],"bloomTexture"),this._bloomTexSize.x=this._bloomWidths[S-1],this._bloomTexSize.y=this._bloomHeights[S-1],f.setVec4("bloomTexSize",this._bloomTexSize),f.addQueue(h.OPAQUE).addFullscreenQuad(r,1)}for(var _=c;_-- >0;){var w=e.addRenderPass(this._bloomWidths[_],this._bloomHeights[_],"cc-bloom-upsample");w.addRenderTarget(this._bloomTexNames[_],E.CLEAR,C.STORE,this._clearColorTransparentBlack),w.addTexture(this._bloomTexNames[_+1],"bloomTexture"),this._bloomTexSize.x=this._bloomWidths[_+1],this._bloomTexSize.y=this._bloomHeights[_+1],w.setVec4("bloomTexSize",this._bloomTexSize),w.addQueue(h.OPAQUE).addFullscreenQuad(r,2)}this._bloomParams.w=i.bloom.intensity;var b=e.addRenderPass(n,o,"cc-bloom-combine");return b.addRenderTarget(l,E.LOAD,C.STORE),b.addTexture(this._bloomTexNames[0],"bloomTexture"),b.setVec4("bloomParams",this._bloomParams),b.addQueue(h.BLEND).addFullscreenQuad(r,3),0===t.remainingPasses?W(e,0,t,l):b},a._addPass=function(e,a,t,i,r,s,n,o,l,h){void 0===o&&(o=E.CLEAR),void 0===l&&(l=I),void 0===h&&(h=d.QueueHint.OPAQUE);var c=e.addRenderPass(a,t,i);return c.addRenderTarget(r,o,C.STORE,l),c.addQueue(h).addFullscreenQuad(s,n),c},a._addMipmapFilterBloomPasses=function(e,a,t,i,r,s,n,o,d){this._bloomParams.x=a.useFloatOutput?1:0,this._bloomParams.x=0,this._bloomParams.z=i.bloom.threshold,this._bloomParams.w=i.bloom.intensity;var l=this._prefilterTexDesc,h=this._addPass(e,l.width,l.height,"cc-bloom-mipmap-prefilter",l.name,r,0);h.addTexture(d,"mainTexture"),h.setVec4("bloomParams",this._bloomParams);for(var c=this._bloomDownSampleTexDescs,u=0;u<c.length;++u){var p=c[u],g=0===u?l:c[u-1],m=g.name;this._bloomTexSize.x=1/g.width,this._bloomTexSize.y=1/g.height,(h=this._addPass(e,p.width,p.height,"cc-bloom-mipmap-downsample",p.name,r,1)).addTexture(m,"mainTexture"),h.setVec4("bloomParams",this._bloomTexSize)}for(var S=c.length-1,f=this._bloomUpSampleTexDescs,_=0;_<f.length;_++){var w=f[_],b=0===_?c[S]:f[_-1],P=b.name;this._bloomTexSize.x=1/b.width,this._bloomTexSize.y=1/b.height,(h=this._addPass(e,w.width,w.height,"cc-bloom-mipmap-upsample",w.name,r,2)).addTexture(P,"mainTexture"),h.addTexture(c[S-1-_].name,"downsampleTexture"),h.setVec4("bloomParams",this._bloomTexSize)}var T=this._addPass(e,n,o,"cc-bloom-mipmap-combine",d,r,3,E.LOAD);return T.addTexture(f[f.length-1].name,"bloomTexture"),T.setVec4("bloomParams",this._bloomParams),0===t.remainingPasses?W(e,0,t,d):T},e}()),K=e("BuiltinToneMappingPassBuilder",function(){function e(){this._colorGradingTexSize=new n(0,0)}var a=e.prototype;return a.getConfigOrder=function(){return 0},a.getRenderOrder=function(){return 300},a.configCamera=function(e,a,t){var i=t.settings;t.enableColorGrading=i.colorGrading.enabled&&!!i.colorGrading.material&&!!i.colorGrading.colorGradingMap,t.enableToneMapping=t.enableHDR||t.enableColorGrading,t.enableToneMapping&&++t.remainingPasses},a.windowResize=function(e,a,t){t.enableColorGrading&&(l(!!t.settings.colorGrading.material),t.settings.colorGrading.material.setProperty("colorGradingMap",t.settings.colorGrading.colorGradingMap))},a.setup=function(e,a,t,i,r,s){if(!t.enableToneMapping)return s;if(--t.remainingPasses,l(t.remainingPasses>=0),0===t.remainingPasses)return this._addCopyAndTonemapPass(e,a,t,t.width,t.height,r.colorName,t.colorName);var n=t.renderWindowId,o=t.enableShadingScale?"ScaledLdrColor":"LdrColor",d=G(r.colorName,o,n),h=r.colorName;return r.colorName=d,this._addCopyAndTonemapPass(e,a,t,t.width,t.height,h,d)},a._addCopyAndTonemapPass=function(e,a,t,i,r,s,n){var o,h=t.settings;if(t.enableColorGrading){l(!!h.colorGrading.material),l(!!h.colorGrading.colorGradingMap);var c=h.colorGrading.colorGradingMap;this._colorGradingTexSize.x=c.width,this._colorGradingTexSize.y=c.height;var u=c.width===c.height;(o=u?e.addRenderPass(i,r,"cc-color-grading-8x8"):e.addRenderPass(i,r,"cc-color-grading-nx1")).addRenderTarget(n,E.CLEAR,C.STORE,I),o.addTexture(s,"sceneColorMap"),o.setVec2("lutTextureSize",this._colorGradingTexSize),o.setFloat("contribute",h.colorGrading.contribute),o.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(h.colorGrading.material,u?1:0)}else(o=e.addRenderPass(i,r,"cc-tone-mapping")).addRenderTarget(n,E.CLEAR,C.STORE,I),o.addTexture(s,"inputTexture"),h.toneMapping.material?o.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(h.toneMapping.material,0):(l(!!t.copyAndTonemapMaterial),o.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(t.copyAndTonemapMaterial,0));return o},e}()),X=e("BuiltinFXAAPassBuilder",function(){function e(){this._fxaaParams=new o(0,0,0,0)}var a=e.prototype;return a.getConfigOrder=function(){return 0},a.getRenderOrder=function(){return 400},a.configCamera=function(e,a,t){t.enableFXAA=t.settings.fxaa.enabled&&!!t.settings.fxaa.material,t.enableFXAA&&++t.remainingPasses},a.setup=function(e,a,t,i,r,s){if(!t.enableFXAA)return s;--t.remainingPasses,l(t.remainingPasses>=0);var n=t.renderWindowId,o=t.enableShadingScale?"ScaledLdrColor":"LdrColor",d=G(r.colorName,o,n);if(l(!!t.settings.fxaa.material),0===t.remainingPasses)return t.enableShadingScale?(this._addFxaaPass(e,a,t.settings.fxaa.material,t.width,t.height,r.colorName,d),W(e,0,t,d)):(l(t.width===t.nativeWidth),l(t.height===t.nativeHeight),this._addFxaaPass(e,a,t.settings.fxaa.material,t.width,t.height,r.colorName,t.colorName));var h=r.colorName;return r.colorName=d,this._addFxaaPass(e,a,t.settings.fxaa.material,t.width,t.height,h,d)},a._addFxaaPass=function(e,a,t,i,r,s,n){this._fxaaParams.x=i,this._fxaaParams.y=r,this._fxaaParams.z=1/i,this._fxaaParams.w=1/r;var o=e.addRenderPass(i,r,"cc-fxaa");return o.addRenderTarget(n,E.CLEAR,C.STORE,I),o.addTexture(s,"sceneColorMap"),o.setVec4("texSize",this._fxaaParams),o.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(t,0),o},e}()),q=e("BuiltinFsrPassBuilder",function(){function e(){this._fsrParams=new o(0,0,0,0),this._fsrTexSize=new o(0,0,0,0)}var a=e.prototype;return a.getConfigOrder=function(){return 0},a.getRenderOrder=function(){return 500},a.configCamera=function(e,a,t){t.enableFSR=t.settings.fsr.enabled&&!!t.settings.fsr.material&&t.enableShadingScale&&t.shadingScale<1,t.enableFSR&&++t.remainingPasses},a.setup=function(e,a,t,i,r,s){if(!t.enableFSR)return s;--t.remainingPasses;var n=r.colorName,o=0===t.remainingPasses?t.colorName:G(r.colorName,"UiColor",t.renderWindowId);return r.colorName=o,l(!!t.settings.fsr.material),this._addFsrPass(e,a,t,t.settings,t.settings.fsr.material,t.renderWindowId,t.width,t.height,n,t.nativeWidth,t.nativeHeight,o)},a._addFsrPass=function(e,a,t,i,r,s,n,o,l,c,u,p){this._fsrTexSize.x=n,this._fsrTexSize.y=o,this._fsrTexSize.z=c,this._fsrTexSize.w=u,this._fsrParams.x=h(1-i.fsr.sharpness,.02,.98);var g=G(p,"UiColor",s),m=e.addRenderPass(c,u,"cc-fsr-easu");m.addRenderTarget(g,E.CLEAR,C.STORE,I),m.addTexture(l,"outputResultMap"),m.setVec4("fsrTexSize",this._fsrTexSize),m.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(r,0);var S=e.addRenderPass(c,u,"cc-fsr-rcas");return S.addRenderTarget(p,E.CLEAR,C.STORE,I),S.addTexture(g,"outputResultMap"),S.setVec4("fsrTexSize",this._fsrTexSize),S.setVec4("fsrParams",this._fsrParams),S.addQueue(d.QueueHint.OPAQUE).addFullscreenQuad(r,1),S},e}()),j=e("BuiltinUiPassBuilder",function(){function e(){}var a=e.prototype;return a.getConfigOrder=function(){return 0},a.getRenderOrder=function(){return 1e3},a.setup=function(e,a,t,i,r,s){l(!!s);var n=d.SceneFlags.UI;return t.enableProfiler&&(n|=d.SceneFlags.PROFILER,s.showStatistics=!0),s.addQueue(d.QueueHint.BLEND,"default","default").addScene(i,n),s},e}());if(d){var Z=d.QueueHint,J=d.SceneFlags,$=function(){function e(){this._pipelineEvent=t.director.root.pipelineEvent,this._forwardPass=new V,this._bloomPass=new Y,this._toneMappingPass=new K,this._fxaaPass=new X,this._fsrPass=new q,this._uiPass=new j,this._clearColor=new R(0,0,0,1),this._viewport=new A,this._configs=new Q,this._cameraConfigs=new z,this._copyAndTonemapMaterial=new u,this._initialized=!1,this._passBuilders=[]}var i=e.prototype;return i._setupPipelinePreview=function(e,a){if(e.cameraUsage===D.SCENE_VIEW||e.cameraUsage===D.PREVIEW){var t=d.getEditorPipelineSettings();a.settings=t||H}else e.pipelineSettings?a.settings=e.pipelineSettings:a.settings=H},i._preparePipelinePasses=function(e){var t=this._passBuilders;t.length=0;var i=e.settings;if(i._passes){for(var r,s=a(i._passes);!(r=s()).done;){var n=r.value;t.push(n)}l(t.length===i._passes.length)}t.push(this._forwardPass),i.bloom.enabled&&t.push(this._bloomPass),t.push(this._toneMappingPass),i.fxaa.enabled&&t.push(this._fxaaPass),i.fsr.enabled&&t.push(this._fsrPass),t.push(this._uiPass)},i._setupBuiltinCameraConfigs=function(e,a,t,i){var s=a.window,n=a.cameraUsage===D.GAME&&!!s.swapchain,o=n||a.cameraUsage===D.GAME_VIEW;i.isMainGameWindow=n,i.renderWindowId=s.renderWindowId,i.colorName=s.colorName,i.depthStencilName=s.depthStencilName,i.enableFullPipeline=0!=(a.visibility&p.Enum.DEFAULT),i.enableProfiler=e.profiler&&o,i.remainingPasses=0,i.shadingScale=i.settings.shadingScale,i.enableShadingScale=i.settings.enableShadingScale&&1!==i.shadingScale,i.nativeWidth=Math.max(Math.floor(s.width),1),i.nativeHeight=Math.max(Math.floor(s.height),1),i.width=i.enableShadingScale?Math.max(Math.floor(i.nativeWidth*i.shadingScale),1):i.nativeWidth,i.height=i.enableShadingScale?Math.max(Math.floor(i.nativeHeight*i.shadingScale),1):i.nativeHeight,i.enableHDR=i.enableFullPipeline&&t.useFloatOutput,i.radianceFormat=i.enableHDR?r.Format.RGBA16F:r.Format.RGBA8,i.copyAndTonemapMaterial=this._copyAndTonemapMaterial,i.enableStoreSceneDepth=!1},i._setupCameraConfigs=function(e,t,i,r){this._setupPipelinePreview(t,r),this._preparePipelinePasses(r),this._passBuilders.sort((function(e,a){return e.getConfigOrder()-a.getConfigOrder()})),this._setupBuiltinCameraConfigs(e,t,i,r);for(var s,n=a(this._passBuilders);!(s=n()).done;){var o=s.value;o.configCamera&&o.configCamera(t,i,r)}},i.windowResize=function(e,t,i,r,s){B(e,this._configs),this._setupCameraConfigs(e,i,this._configs,this._cameraConfigs);var n=t.renderWindowId;e.addRenderWindow(this._cameraConfigs.colorName,M.RGBA8,r,s,t,this._cameraConfigs.depthStencilName);var o=this._cameraConfigs.width,d=this._cameraConfigs.height;this._cameraConfigs.enableShadingScale?(e.addDepthStencil("ScaledSceneDepth_"+n,M.DEPTH_STENCIL,o,d),e.addRenderTarget("ScaledRadiance0_"+n,this._cameraConfigs.radianceFormat,o,d),e.addRenderTarget("ScaledRadiance1_"+n,this._cameraConfigs.radianceFormat,o,d),e.addRenderTarget("ScaledLdrColor0_"+n,M.RGBA8,o,d),e.addRenderTarget("ScaledLdrColor1_"+n,M.RGBA8,o,d)):(e.addDepthStencil("SceneDepth_"+n,M.DEPTH_STENCIL,o,d),e.addRenderTarget("Radiance0_"+n,this._cameraConfigs.radianceFormat,o,d),e.addRenderTarget("Radiance1_"+n,this._cameraConfigs.radianceFormat,o,d),e.addRenderTarget("LdrColor0_"+n,M.RGBA8,o,d),e.addRenderTarget("LdrColor1_"+n,M.RGBA8,o,d)),e.addRenderTarget("UiColor0_"+n,M.RGBA8,r,s),e.addRenderTarget("UiColor1_"+n,M.RGBA8,r,s);for(var l,h=a(this._passBuilders);!(l=h()).done;){var c=l.value;c.windowResize&&c.windowResize(e,this._configs,this._cameraConfigs,t,i,r,s)}},i.setup=function(e,t){if(!this._initMaterials(t))for(var i,r=a(e);!(i=r()).done;){var s=i.value;s.scene&&s.window&&(this._setupCameraConfigs(t,s,this._configs,this._cameraConfigs),this._pipelineEvent.emit(g.RENDER_CAMERA_BEGIN,s),this._cameraConfigs.enableFullPipeline?this._buildForwardPipeline(t,s,s.scene,this._passBuilders):this._buildSimplePipeline(t,s),this._pipelineEvent.emit(g.RENDER_CAMERA_END,s))}},i._buildSimplePipeline=function(e,a){var t=Math.max(Math.floor(a.window.width),1),i=Math.max(Math.floor(a.window.height),1),r=this._cameraConfigs.colorName,s=this._cameraConfigs.depthStencilName,n=a.viewport;this._viewport.left=Math.round(n.x*t),this._viewport.top=Math.round(n.y*i),this._viewport.width=Math.max(Math.round(n.width*t),1),this._viewport.height=Math.max(Math.round(n.height*i),1);var o=a.clearColor;this._clearColor.x=o.x,this._clearColor.y=o.y,this._clearColor.z=o.z,this._clearColor.w=o.w;var d=e.addRenderPass(t,i,"default");O(a)?d.addRenderTarget(r,E.CLEAR,C.STORE,this._clearColor):d.addRenderTarget(r,E.LOAD,C.STORE),a.clearFlag&T.DEPTH_STENCIL?d.addDepthStencil(s,E.CLEAR,C.DISCARD,a.clearDepth,a.clearStencil,a.clearFlag&T.DEPTH_STENCIL):d.addDepthStencil(s,E.LOAD,C.DISCARD),d.setViewport(this._viewport),d.addQueue(Z.OPAQUE).addScene(a,J.OPAQUE);var l=J.BLEND|J.UI;this._cameraConfigs.enableProfiler&&(l|=J.PROFILER,d.showStatistics=!0),d.addQueue(Z.BLEND).addScene(a,l)},i._buildForwardPipeline=function(e,t,i,r){!function(e){e.sort((function(e,a){return e.getRenderOrder()-a.getRenderOrder()}))}(r);for(var s,n={colorName:"",depthStencilName:""},o=void 0,d=a(r);!(s=d()).done;){var h=s.value;h.setup&&(o=h.setup(e,this._configs,this._cameraConfigs,t,n,o))}l(0===this._cameraConfigs.remainingPasses)},i._initMaterials=function(e){return this._initialized?0:(B(e,this._configs),this._copyAndTonemapMaterial._uuid="builtin-pipeline-tone-mapping-material",this._copyAndTonemapMaterial.initialize({effectName:"pipeline/post-process/tone-mapping"}),this._copyAndTonemapMaterial.effectAsset&&(this._initialized=!0),this._initialized?0:1)},e}();d.setCustomPipeline("Builtin",new $)}t._RF.pop()}}}));

System.register("chunks:///_virtual/internal",["./builtin-pipeline-settings.ts","./builtin-pipeline-types.ts","./builtin-pipeline.ts"],(function(){return{setters:[null,null,null],execute:function(){}}}));

(function(r) {
  r('virtual:///prerequisite-imports/internal', 'chunks:///_virtual/internal'); 
})(function(mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
    return {
        setters: [function(_m) {
            var _exportObj = {};

            for (var _key in _m) {
              if (_key !== "default" && _key !== "__esModule") _exportObj[_key] = _m[_key];
            }
      
            _export(_exportObj);
        }],
        execute: function () { }
    };
    });
});