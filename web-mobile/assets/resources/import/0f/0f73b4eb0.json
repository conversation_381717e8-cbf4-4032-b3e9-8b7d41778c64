[1, ["58CtPV6UFACYOMTfNzbBDh@f9941", "41hG9zL09ClL02kJI3+K7w@f9941", "0fVMPrR+FBK4lSUnSM+jfN@f9941", "7dj5uJT9FMn6OrOOx83tfK@f9941", "c5s23igK1Ej7jHU1pHATSI@f9941", "0dXOalQnJNXpWRk6NHz2F3@f9941", "77UaYsjzpO7Ykpw1crHb+9@f9941", "fbVN1ZgchLkJf5QxyEsMRK@f9941", "8d2bXDVopI/IR8WLTkNClE@f9941", "67EkHuq0BOubNqft+JKWGc@f9941", "d7A2gPJ7pHvoJiHLI+YK6y@f9941", "716PdrfilABreT35hlp4w+@f9941", "7dj5uJT9FMn6OrOOx83tfK@6c48a"], ["node", "_spriteFrame", "root", "tFaceSprite", "pFaceSprite", "aFaceSprite", "tWorkerNode", "pWorkerNode", "aWorkerNode", "bg2Node", "bg1Node", "data", "_textureSource"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame", "_color"], 2, 1, 4, 6, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["ebe84eajHJK9ICTl/+uVu0t", ["node", "__prefab", "bg1Node", "bg2Node", "aWorkerNode", "pWorkerNode", "tWorkerNode", "rankNodes", "bgNodes", "faceSprites", "defaultFaceSprites", "aFaceSprite", "pFaceSprite", "tFaceSprite"], 3, 1, 4, 1, 1, 1, 1, 1, 2, 2, 3, 3, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[6, 0, 2], [5, 0, 1, 2, 1], [9, 0, 1, 2, 3, 4, 5, 5], [1, 1, 2, 3, 1], [0, 0, 1, 5, 6, 3, 4, 7, 3], [0, 0, 2, 1, 5, 3, 4, 7, 4], [4, 0, 1, 2, 3, 4, 5, 3], [1, 1, 2, 1], [0, 0, 2, 1, 5, 3, 4, 4], [7, 0, 1, 2, 3, 4, 4], [1, 0, 1, 2, 3, 2], [3, 0, 2], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 1], [1, 0, 1, 2, 4, 3, 2]], [[[[11, "RankPopup"], [12, "RankPopup", 33554432, [-18, -19, -20, -21, -22, -23, -24], [[1, -2, [0, "3dUqiqbKJMk7gOGivZ3EX6"], [5, 540, 430]], [9, 45, 100, 100, -3, [0, "59gPRgGo9O8LtwAfi3uCbi"]], [14, -17, [0, "06jcxbKR9MPYijXOtL1SfZ"], -16, -15, -14, -13, -12, [-9, -10, -11], [-7, -8], [9, 10, 11], [12, 13, 14], -6, -5, -4]], [2, "91k3O8usJF9YItVL6raRwS", null, null, null, -1, 0]], [8, "bg_final_02", false, 33554432, 1, [[1, -25, [0, "7fO46Uha1D2oHMl14WAxZ+"], [5, 540, 430]], [10, 0, -26, [0, "e6fjmdBT5HcqlBe1c/8dnC"], 1]], [2, "b5Xqfm8GFCFbYMaf2sei/D", null, null, null, 1, 0]], [8, "bg_final_01", false, 33554432, 1, [[1, -27, [0, "c9jUNT3AtGT67iQPTsXCDH"], [5, 540, 430]], [10, 0, -28, [0, "2byr2P4rNF1aZtoslpRtLc"], 2]], [2, "b5Ld+XDVJIWZeNoIoierDE", null, null, null, 1, 0]], [4, "Node", 33554432, 1, [-30, -31, -32], [[1, -29, [0, "15BRlFcLZGaKEfAiwvIqjc"], [5, 438, 100]]], [2, "b8ZaPU6XJNy4svoifK95Gg", null, null, null, 1, 0], [1, 0, -163.854, 0]], [4, "a_worker_final", 33554432, 4, [-35], [[1, -33, [0, "fa8PkVzXJK3qfFs1rwaoc3"], [5, 138, 387]], [3, -34, [0, "47OLv3GsVDSIhetKFBXPyG"], 6]], [2, "30ujsxFFxMaq7bItwKCGCm", null, null, null, 1, 0], [1, 400, 0, 0]], [4, "p_worker_final", 33554432, 4, [-38], [[1, -36, [0, "e1K7xEDlZC/7zR3QCfBem8"], [5, 138, 387]], [3, -37, [0, "0e5MJyeshGyL0juBOVmINI"], 7]], [2, "67IitHchlE1bHRgVg4f+rP", null, null, null, 1, 0], [1, 400, 0, 0]], [4, "t_worker_final", 33554432, 4, [-41], [[1, -39, [0, "b9OkPcimNDBadu7uAbZtuC"], [5, 138, 387]], [3, -40, [0, "45LdZKvuFGfZE5fYa49H18"], 8]], [2, "92PpOckUVEdYcSoxL7oLGi", null, null, null, 1, 0], [1, 400, 0, 0]], [13, "Sprite", 33554432, 1, [[1, -42, [0, "dfBTOWARJBLoX2S74fgM8t"], [5, 540, 430]], [15, 0, -43, [0, "46FqiA7sVJWr71md1qeZxb"], [4, 3774873600], 0], [9, 45, 40, 36, -44, [0, "838TTpjMhM84mMVRXVTJNq"]]], [2, "dfG9+GHpVNR4ydeSSFRQG7", null, null, null, 1, 0]], [5, "1st", false, 33554432, 1, [[1, -45, [0, "1f2ZJLLWZP4q04CdqnhI3m"], [5, 80, 42]], [3, -46, [0, "80PvNIo5BKzo07jeoBFKHL"], 3]], [2, "81A4fAAbJGxrUIZtBNVPWL", null, null, null, 1, 0], [1, -138.922, 151.021, 0]], [5, "2nd", false, 33554432, 1, [[1, -47, [0, "fdhftfh+1E9LzAyHliKHC1"], [5, 81, 42]], [3, -48, [0, "9eRW8vXXVLU699W5EgkHO7"], 4]], [2, "5ft7uMOdZM6Zf86hmCKTW8", null, null, null, 1, 0], [1, 0, 129.464, 0]], [5, "3rd", false, 33554432, 1, [[1, -49, [0, "75MdZypy9O+osCSg4RhkOt"], [5, 81, 42]], [3, -50, [0, "68Sy0fowRPk4WBguIi+P+c"], 5]], [2, "de6KqoVmtM06Vsyy0jJxIr", null, null, null, 1, 0], [1, 137.725, 62.398, 0]], [6, "2nd_face", 33554432, 5, [[[1, -51, [0, "8fGogaYbxMPKN9baGWskkK"], [5, 40, 42]], -52], 4, 1], [2, "abM2+3wXFC+JNizqlKJDvt", null, null, null, 1, 0], [1, 0, 115, 0]], [6, "2nd_face", 33554432, 6, [[[1, -53, [0, "f2lsTFXp9LVoctoiUPbdje"], [5, 40, 42]], -54], 4, 1], [2, "7fzezGRR9A5ZKZBhfD7oaI", null, null, null, 1, 0], [1, 0, 115, 0]], [6, "2nd_face", 33554432, 7, [[[1, -55, [0, "72dBbeBy5Bio1KgeteKPxf"], [5, 40, 42]], -56], 4, 1], [2, "7byvp+w6RIIJLAqlbsDNN6", null, null, null, 1, 0], [1, 0, 115, 0]], [7, 12, [0, "2afSSrlkxGH5NixheOTlHZ"]], [7, 13, [0, "41g/gtiwBJS6gatuZHeT7P"]], [7, 14, [0, "41nt1m4btHIoZwAA1cIYyt"]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 3, 17, 0, 4, 16, 0, 5, 15, 0, -1, 3, 0, -2, 2, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, 6, 7, 0, 7, 6, 0, 8, 5, 0, 9, 3, 0, 10, 2, 0, 0, 1, 0, -1, 8, 0, -2, 2, 0, -3, 3, 0, -4, 9, 0, -5, 10, 0, -6, 11, 0, -7, 4, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 6, 0, -1, 13, 0, 0, 7, 0, 0, 7, 0, -1, 14, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -2, 15, 0, 0, 13, 0, -2, 16, 0, 0, 14, 0, -2, 17, 0, 11, 1, 56], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 16, 17], [1, 1, 1, 1, 1, 1, 1, 1, 1, -1, -2, -3, -1, -2, -3, 1, 1, 1], [3, 4, 5, 6, 7, 8, 9, 10, 11, 1, 0, 2, 1, 0, 2, 0, 0, 0]], [[{"name": "default_sprite_splash", "rect": {"x": 0, "y": 0, "width": 2, "height": 2}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2, "height": 2}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-1, -1, 0, 1, -1, 0, -1, 1, 0, 1, 1, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2, 2, 2, 0, 0, 2, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -1, "y": -1, "z": 0}, "maxPos": {"x": 1, "y": 1, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [12], [12]]]]