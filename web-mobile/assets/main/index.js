System.register("chunks:///_virtual/AuthManager.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var e,a,n,i,o;return{setters:[function(t){e=t.inheritsLoose,a=t.extends},function(t){n=t.cclegacy,i=t._decorator,o=t.Component}],execute:function(){var r,s;n._RF.push({},"504f3rFk6NCKK7vR+UQW98b","AuthManager",void 0);var u=i.ccclass;t("AuthManager",u("AuthManager")(((s=function(t){function n(){for(var e,a=arguments.length,n=new Array(a),i=0;i<a;i++)n[i]=arguments[i];return(e=t.call.apply(t,[this].concat(n))||this)._authData={token:"",playerId:"",sessionId:"",balance:0,isAuthenticated:!1},e._config={defaultPlayerId:"player_1",sessionTimeout:36e5,defaultToken:"GEdR9jerB1iLv3EwwwWaQ94xRygrtRchNOqbqU4Ctq3%2btDYtl9MU%2bS5pYqku8mV41gx4KKEgk4gI7t4iQCjo5gsVvieLH7b7hX%2bCf7lZw%2b0%3d"},e}e(n,t),n.getInstance=function(){return this._instance};var i=n.prototype;return i.start=function(){n._instance=this,console.log("[AuthManager] Initialized")},i.extractTokenFromURL=function(){try{var t=new URLSearchParams(window.location.search).get("token");return t?(console.log("[AuthManager] Token extracted from URL"),decodeURIComponent(t)):(console.log("[AuthManager] No token found in URL parameters"),"")}catch(t){return console.error("[AuthManager] Failed to extract token from URL:",t),""}},i.initializeWithToken=function(t,e){return t&&0!==t.trim().length?(console.log("[AuthManager] Initializing with provided token"),this.initializeAuth(t.trim(),e||this._config.defaultPlayerId)):{success:!1,error:"Token parameter is required"}},i.extractTokenFromParameter=function(t){try{return t.startsWith("token=")?t.substring(6):t}catch(t){return console.error("[AuthManager] Failed to extract token from parameter:",t),""}},i.validateToken=function(t){if(!t||0===t.trim().length)return{valid:!1,error:"Token is empty"};if(t.length<50||t.length>200)return{valid:!1,error:"Token length is invalid"};try{return atob(t).length<10?{valid:!1,error:"Decoded token is too short"}:(console.log("[AuthManager] Token validation successful"),{valid:!0})}catch(t){return{valid:!1,error:"Token is not valid base64"}}},i.decodeToken=function(t){try{var e=atob(t);try{return{success:!0,data:JSON.parse(e)}}catch(t){return{success:!0,data:{raw:e,length:e.length,type:"binary"}}}}catch(t){return{success:!1,error:"Failed to decode token: "+t.message}}},i.initializeAuth=function(t,e){var a=this.validateToken(t);return a.valid?(this._authData.token=t,this._authData.playerId=e||"",this._authData.isAuthenticated=!1,console.log("[AuthManager] Authentication initialized"),{success:!0}):{success:!1,error:a.error}},i.setAuthenticationStatus=function(t,e,a){this._authData.isAuthenticated=t,e&&(this._authData.sessionId=e),void 0!==a&&(this._authData.balance=a),console.log("[AuthManager] Authentication status updated:",t)},i.updateBalance=function(t){this._authData.balance=t,console.log("[AuthManager] Balance updated:",t)},i.updateUserProfile=function(t,e){t&&(this._authData.userProfile=t,this._authData.playerId=t.id||this._authData.playerId,"number"==typeof t.balance&&(this._authData.balance=t.balance)),e&&(this._authData.bettingLimits=e),console.log("[AuthManager] User profile updated:",{playerId:this._authData.playerId,nickname:null==t?void 0:t.nickname,balance:this._authData.balance,currency:null==t?void 0:t.currency})},i.getAuthData=function(){return a({},this._authData)},i.getToken=function(){return this._authData.token},i.isAuthenticated=function(){return this._authData.isAuthenticated&&this._authData.token.length>0},i.getUserProfile=function(){return this._authData.userProfile},i.getBettingLimits=function(){return this._authData.bettingLimits},i.getBalance=function(){return this._authData.balance},i.getUserNickname=function(){var t;return(null==(t=this._authData.userProfile)?void 0:t.nickname)||this._authData.playerId||"Unknown"},i.clearAuth=function(){this._authData={token:"",playerId:"",sessionId:"",balance:0,isAuthenticated:!1},console.log("[AuthManager] Authentication data cleared")},i.getAuthHeaders=function(){return{Authorization:"Bearer "+this._authData.token,"X-Player-ID":this._authData.playerId,"X-Session-ID":this._authData.sessionId}},i.createAuthPayload=function(){return{token:this._authData.token,playerId:this._authData.playerId,sessionId:this._authData.sessionId,timestamp:(new Date).toISOString()}},i.autoInitialize=function(){var t=this.extractTokenFromURL();if(t)return console.log("[AuthManager] Using token from URL"),this.initializeAuth(t);if(console.log("[AuthManager] No token found in URL parameters, trying default token"),this._config.defaultToken){console.log("[AuthManager] Using default token");var e=decodeURIComponent(this._config.defaultToken),a=this.initializeAuth(e);return a.success?a:(console.error("[AuthManager] Default token validation failed:",a.error),{success:!1,error:"Default token validation failed: "+a.error})}return console.log("[AuthManager] No default token configured"),{success:!1,error:"No token found in URL and no default token configured"}},i.validateSession=function(){return!(!this._authData.token||!this._authData.isAuthenticated)&&this._authData.sessionId.length>0},i.refreshAuth=function(){var t=this;return new Promise((function(e){e(t.isAuthenticated())}))},i.getAuthSummary=function(){return{hasToken:this._authData.token.length>0,tokenLength:this._authData.token.length,playerId:this._authData.playerId,sessionId:this._authData.sessionId?this._authData.sessionId.substring(0,8)+"...":"",balance:this._authData.balance,isAuthenticated:this._authData.isAuthenticated,timestamp:(new Date).toISOString()}},i.setToken=function(t,e){var a=this.validateToken(t);return a.valid?this.initializeAuth(t,e||this._config.defaultPlayerId):{success:!1,error:a.error}},i.logout=function(){this.clearAuth(),console.log("[AuthManager] User logged out")},i.isSessionValid=function(){return!(!this._authData.isAuthenticated||!this._authData.token)&&this._authData.isAuthenticated},i.getConfig=function(){return{defaultPlayerId:this._config.defaultPlayerId,sessionTimeout:this._config.sessionTimeout,defaultToken:this._config.defaultToken?this._config.defaultToken.substring(0,20)+"...":""}},i.updateConfig=function(t){this._config=a({},this._config,t),console.log("[AuthManager] Configuration updated")},n}(o))._instance=null,r=s))||r);n._RF.pop()}}}));

System.register("chunks:///_virtual/CardSkin.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(a){var e,i,r,t,n,o,s,d,l,c,u,p,h;return{setters:[function(a){e=a.applyDecoratedDescriptor,i=a.inheritsLoose,r=a.initializerDefineProperty,t=a.assertThisInitialized},function(a){n=a.cclegacy,o=a._decorator,s=a.Sprite,d=a.SpriteFrame,l=a.warn,c=a.tween,u=a.Vec3,p=a.Color,h=a.Component}],execute:function(){var m,f,C,S,k,b,g,v,y,w,B,F,A,T,D,z,I,x,G,M,N,_,J;n._RF.push({},"3f15aJ3bVdGp6gST8FaZTOS","CardSkin",void 0);var L=o.ccclass,P=o.property;a("default",(m=L("CardSkin"),f=P(s),C=P(d),S=P(d),k=P(d),b=P(d),g=P(d),v=P(d),y=P(d),m((F=e((B=function(a){function e(){for(var e,i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];return e=a.call.apply(a,[this].concat(n))||this,r(e,"cardSprite",F,t(e)),r(e,"useSprite",A,t(e)),r(e,"cardBack",T,t(e)),r(e,"cardDragonBack",D,t(e)),r(e,"cardTigerBack",z,t(e)),r(e,"heartCards",I,t(e)),r(e,"diamondCards",x,t(e)),r(e,"clubCards",G,t(e)),r(e,"spadeCards",M,t(e)),e.currentCard=null,e.isCardBack=!0,r(e,"enableAnimation",N,t(e)),r(e,"defaultFlipDuration",_,t(e)),r(e,"flipDirection",J,t(e)),e.currentTween=null,e.isAnimating=!1,e.animationCallbacks=[],e}i(e,a);var n=e.prototype;return n.onLoad=function(){!this.cardSprite&&this.getComponent(s)&&(this.cardSprite=this.getComponent(s))},n.SetCard=function(a){if(a&&this.useSprite&&this.cardSprite){this.currentCard=a,this.isCardBack=!1;var e=this.privateGetCardSpriteFrame(a);e?(this.cardSprite.spriteFrame=e,console.log("[CardSkin] Set card: "+a.display)):l("[CardSkin] Sprite frame not found for card: "+a.display)}else l("[CardSkin] Invalid card data or sprite not available")},n.SetCardByName=function(a,e){var i=this.privateCreateGameCard(a,e);i?this.SetCard(i):l("[CardSkin] Invalid card name: "+a+"_"+e)},n.SetCardByIndex=function(a){if(a<0||a>51)l("[CardSkin] Card index out of range (0-51)");else{var e=Math.floor(a/13),i=a%13;this.SetCardByName(["hearts","diamonds","clubs","spades"][e],["2","3","4","5","6","7","8","9","T","J","Q","K","A"][i])}},n.ShowCardBack=function(a){var e,i,r;if(void 0===a&&(a="normal"),this.useSprite&&this.cardSprite){this.isCardBack=!0,this.currentCard=null;var t,n=null;switch(console.log("[CardSkin] "+this.node.name+" showCardBack called with backType: "+a),console.log("[CardSkin] "+this.node.name+" available frames - dragon: "+!!this.cardDragonBack+", tiger: "+!!this.cardTigerBack+", normal: "+!!this.cardBack),a){case"dragon":n=this.cardDragonBack,console.log("[CardSkin] "+this.node.name+" selected dragon back frame: "+((null==(e=n)?void 0:e.name)||"null"));break;case"tiger":n=this.cardTigerBack,console.log("[CardSkin] "+this.node.name+" selected tiger back frame: "+((null==(i=n)?void 0:i.name)||"null"));break;default:n=this.cardBack,console.log("[CardSkin] "+this.node.name+" selected normal back frame: "+((null==(r=n)?void 0:r.name)||"null"))}if(n)this.cardSprite.spriteFrame=n,console.log("[CardSkin] "+this.node.name+" applied "+a+" card back - final frame: "+(null==(t=this.cardSprite.spriteFrame)?void 0:t.name));else l("[CardSkin] "+this.node.name+" "+a+" card back sprite frame not assigned")}},n.privateGetCardSpriteFrame=function(a){var e=[];switch(a.suit){case"hearts":e=this.heartCards;break;case"diamonds":e=this.diamondCards;break;case"clubs":e=this.clubCards;break;case"spades":e=this.spadeCards;break;default:return null}var i=this.privateGetCardIndex(a.value);return i>=0&&i<e.length&&e[i]?e[i]:null},n.privateGetCardIndex=function(a){var e={2:0,3:1,4:2,5:3,6:4,7:5,8:6,9:7,10:8,T:8,11:9,J:9,12:10,Q:10,13:11,K:11,a:12,A:12,1:12};return console.log('[CardSkin] getCardIndex for value: "'+a+'" -> index: '+(void 0!==e[a]?e[a]:-1)),void 0!==e[a]?e[a]:-1},n.privateCreateGameCard=function(a,e){var i={heart:"hearts",hearts:"hearts",dia:"diamonds",diamond:"diamonds",diamonds:"diamonds",clover:"clubs",club:"clubs",clubs:"clubs",spade:"spades",spades:"spades"}[a.toLowerCase()];if(!i)return null;var r,t=e.toLowerCase();switch(t){case"a":r=1;break;case"11":case"j":r=11;break;case"12":case"q":r=12;break;case"13":case"k":r=13;break;default:if(r=parseInt(t),isNaN(r)||r<2||r>10)return null}return{suit:i,value:t,display:""+("a"===t?"A":"11"===t?"J":"12"===t?"Q":"13"===t?"K":t)+{hearts:"♥",diamonds:"♦",clubs:"♣",spades:"♠"}[i],numericValue:r}},n.RandomCard=function(){var a=Math.floor(52*Math.random());this.SetCardByIndex(a)},n.IsShowingBack=function(){return this.isCardBack},n.GetCurrentCard=function(){return this.currentCard},n.GetCardCount=function(){return 52},n.privatePerformFlip=function(a,e,i,r){if(void 0===e&&(e=this.defaultFlipDuration),void 0===r&&(r=!1),!this.cardSprite||!this.enableAnimation)return this.cardSprite.spriteFrame=a,this.isCardBack=r,void(i&&i());this.StopFlip(),this.isAnimating=!0;var t=this.cardSprite.node;this.performSimpleFlip(t,a,e,i,r)},n.performSimpleFlip=function(a,e,i,r,t){var n=this;void 0===t&&(t=!1);var o=a.scale.clone();this.currentTween=c(a).to(i/2,{scale:"horizontal"===this.flipDirection?new u(0,o.y,o.z):new u(o.x,0,o.z)},{easing:"quadOut"}).call((function(){n.cardSprite.spriteFrame=e,n.isCardBack=t})).to(i/2,{scale:o},{easing:"quadIn"}).call((function(){n.completeFlip(r)})).start()},n.completeFlip=function(a){this.isAnimating=!1,this.currentTween=null,this.animationCallbacks.forEach((function(a){return a()})),this.animationCallbacks=[],a&&a(),console.log("[CardSkin] Flip animation completed")},n.StopFlip=function(){this.currentTween&&(this.currentTween.stop(),this.currentTween=null),this.isAnimating=!1,this.animationCallbacks=[]},n.IsFlipping=function(){return this.isAnimating},n.SetFlipDirection=function(a){this.flipDirection=a},n.SetFlipDuration=function(a){this.defaultFlipDuration=Math.max(.1,a)},n.SetCardAnimated=function(a,e,i){var r,t,n=this;if(console.log("[CardSkin] "+this.node.name+" setCardAnimated called for:",null==a?void 0:a.display,"UseSprite:",this.useSprite,"CardSprite:",!!this.cardSprite),console.log("[CardSkin] "+this.node.name+" current state - isCardBack: "+this.isCardBack+", cardSprite active: "+(null==(r=this.cardSprite)?void 0:r.node.active)+", cardSprite frame: "+(null==(t=this.cardSprite)||null==(t=t.spriteFrame)?void 0:t.name)),a&&this.useSprite&&this.cardSprite){var o=this.privateGetCardSpriteFrame(a);console.log("[CardSkin] "+this.node.name+" spriteFrame found:",!!o,"for card:",a.display),o?(this.currentCard=a,console.log("[CardSkin] "+this.node.name+" starting animation for: "+a.display),this.privatePerformFlip(o,e||this.defaultFlipDuration,(function(){console.log("[CardSkin] "+n.node.name+" animated set card: "+a.display),i&&i()}),!1)):console.warn("[CardSkin] "+this.node.name+" Sprite frame not found for card: "+a.display)}else console.warn("[CardSkin] "+this.node.name+" Invalid card data or sprite not available - Card:",!!a,"UseSprite:",this.useSprite,"CardSprite:",!!this.cardSprite)},n.SetCardByNameAnimated=function(a,e,i,r){var t=this.privateCreateGameCard(a,e);t?this.SetCardAnimated(t,i,r):l("[CardSkin] Invalid card name: "+a+"_"+e)},n.SetCardByIndexAnimated=function(a,e,i){if(a<0||a>51)l("[CardSkin] Card index out of range (0-51)");else{var r=Math.floor(a/13),t=a%13;this.SetCardByNameAnimated(["hearts","diamonds","clubs","spades"][r],["2","3","4","5","6","7","8","9","T","J","Q","K","A"][t],e,i)}},n.ShowCardBackAnimated=function(a,e,i){if(void 0===a&&(a="normal"),this.useSprite&&this.cardSprite){var r=null;switch(a){case"dragon":r=this.cardDragonBack;break;case"tiger":r=this.cardTigerBack;break;default:r=this.cardBack}r?(this.currentCard=null,this.privatePerformFlip(r,e||this.defaultFlipDuration,(function(){console.log("[CardSkin] Animated showing "+a+" card back"),i&&i()}),!0)):l("[CardSkin] "+a+" card back sprite frame not assigned")}},n.FlipToOpposite=function(a,e){this.isCardBack?this.SetCardByIndexAnimated(0,a,e):this.ShowCardBackAnimated("normal",a,e)},n.ApplyGrayTint=function(){this.useSprite&&this.cardSprite?(this.cardSprite.color=new p(128,128,128,255),console.log("[CardSkin] "+this.node.name+": Gray tint applied")):console.warn("[CardSkin] "+this.node.name+": Cannot apply gray tint - sprite not available")},n.ResetColor=function(){this.useSprite&&this.cardSprite?(this.cardSprite.color=p.WHITE,console.log("[CardSkin] "+this.node.name+": Color reset to white")):console.warn("[CardSkin] "+this.node.name+": Cannot reset color - sprite not available")},e}(h)).prototype,"cardSprite",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=e(B.prototype,"useSprite",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),T=e(B.prototype,"cardBack",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=e(B.prototype,"cardDragonBack",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=e(B.prototype,"cardTigerBack",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=e(B.prototype,"heartCards",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),x=e(B.prototype,"diamondCards",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),G=e(B.prototype,"clubCards",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),M=e(B.prototype,"spadeCards",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),N=e(B.prototype,"enableAnimation",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),_=e(B.prototype,"defaultFlipDuration",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.6}}),J=e(B.prototype,"flipDirection",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return"horizontal"}}),w=B))||w));n._RF.pop()}}}));

System.register("chunks:///_virtual/DynamicPhaseTest.ts",["./rollupPluginModLoBabelHelpers.js","cc","./TowerAnimation.ts","./DynamicRandomPhaseConfig.ts"],(function(e){var o,n,t,i,a,r,s,l,c,u,m,g,A;return{setters:[function(e){o=e.applyDecoratedDescriptor,n=e.inheritsLoose,t=e.initializerDefineProperty,i=e.assertThisInitialized,a=e.asyncToGenerator,r=e.regeneratorRuntime},function(e){s=e.cclegacy,l=e._decorator,c=e.Component},function(e){u=e.TowerAnimation},function(e){m=e.DYNAMIC_RANDOM_CONFIG,g=e.generateDynamicRaceConfig,A=e.logDynamicRaceConfig}],execute:function(){var h,f,p,y,w,T,D,N,d,I,b,R,E,P,S;s._RF.push({},"98043yc75JCh4l+I0geTAkJ","DynamicPhaseTest",void 0);var v=l.ccclass,C=l.property;e("DynamicPhaseTest",(h=v("DynamicPhaseTest"),f=C(u),p=C(u),y=C(u),w=C({tooltip:"Auto-run test on start"}),T=C({tooltip:"Delay between tests (seconds)"}),D=C({tooltip:"Enable debug logging for tests"}),h((I=o((d=function(e){function o(){for(var o,n=arguments.length,a=new Array(n),r=0;r<n;r++)a[r]=arguments[r];return o=e.call.apply(e,[this].concat(a))||this,t(o,"towerAnimationA",I,i(o)),t(o,"towerAnimationP",b,i(o)),t(o,"towerAnimationT",R,i(o)),t(o,"autoRunTest",E,i(o)),t(o,"testDelay",P,i(o)),t(o,"enableDebugLogging",S,i(o)),o}n(o,e);var s=o.prototype;return s.onLoad=function(){var e=this;this.autoRunTest&&this.scheduleOnce((function(){e.runDynamicPhaseTest()}),1)},s.runDynamicPhaseTest=function(){console.log("=== DYNAMIC RANDOM PHASE ANIMATION TEST ===");var e=m.PERFORMANCE.ENABLE_DEBUG_LOGGING;m.PERFORMANCE.ENABLE_DEBUG_LOGGING=this.enableDebugLogging,console.log("\n1. GENERATING DYNAMIC CONFIGURATIONS:");var o=g(1),n=g(2),t=g(3);A(o,"Tower A"),A(n,"Tower P"),A(t,"Tower T"),console.log("\n2. DYNAMIC SYSTEM FEATURES:"),this.showDynamicSystemFeatures(),this.towerAnimationA&&this.towerAnimationP&&this.towerAnimationT?(console.log("\n3. RUNNING DYNAMIC ANIMATIONS:"),this.runAnimationTest()):console.log("\n3. SKIPPING ANIMATION TEST (No tower references)"),m.PERFORMANCE.ENABLE_DEBUG_LOGGING=e},s.showDynamicSystemFeatures=function(){console.log("Dynamic Random Phase System Features:"),console.log("  ✓ Random phases: 3-7 per race"),console.log("  ✓ Small actions: 2-5 per phase"),console.log("  ✓ Delay times: 80-150ms between actions"),console.log("  ✓ Realistic effects: hesitation, bursts, fatigue"),console.log("  ✓ Staggered completion with variation"),console.log("  ✓ Configurable 8-10 second timing range"),console.log("  ✓ Smooth sine-based easing profiles"),console.log("  ✓ Progress-based worker reveals"),console.log("  ✓ Advanced performance optimizations")},s.runAnimationTest=function(){var e=a(r().mark((function e(){var o,n=this;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,o=[this.towerAnimationA.startRace(1),this.towerAnimationP.startRace(2),this.towerAnimationT.startRace(3)],console.log("Starting dynamic phase races..."),e.next=5,Promise.all(o);case 5:console.log("All dynamic phase races completed!"),this.scheduleOnce((function(){n.logSystemInfo()}),1),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0),console.error("Animation test failed:",e.t0);case 12:case"end":return e.stop()}}),e,this,[[0,9]])})));return function(){return e.apply(this,arguments)}}(),s.logSystemInfo=function(){if(console.log("\n4. FINAL SYSTEM INFO:"),this.towerAnimationA){var e=this.towerAnimationA.getAnimationSystemInfo();console.log("Tower A: Dynamic - "+e.phaseCount+" phases, "+e.actionCount+" actions, "+(e.totalDuration/1e3).toFixed(2)+"s")}if(this.towerAnimationP){var o=this.towerAnimationP.getAnimationSystemInfo();console.log("Tower P: Dynamic - "+o.phaseCount+" phases, "+o.actionCount+" actions, "+(o.totalDuration/1e3).toFixed(2)+"s")}if(this.towerAnimationT){var n=this.towerAnimationT.getAnimationSystemInfo();console.log("Tower T: Dynamic - "+n.phaseCount+" phases, "+n.actionCount+" actions, "+(n.totalDuration/1e3).toFixed(2)+"s")}},s.testDynamicGeneration=function(){console.log("=== TESTING DYNAMIC GENERATION ===");for(var e=1;e<=3;e++){var o=g(e);console.log("\nPosition "+e+" Configuration:"),console.log("  Phases: "+o.phases.length),console.log("  Actions: "+o.smallActions.length),console.log("  Duration: "+(o.totalDuration/1e3).toFixed(2)+"s"),console.log("  Multiplier: "+o.staggeredMultiplier.toFixed(3))}},s.showSystemInfo=function(){if(this.towerAnimationA){var e=this.towerAnimationA.getAnimationSystemInfo();console.log("Current Animation System: Dynamic Random Phases"),console.log("System Status: "+(e.isDynamic?"Active":"Inactive")),console.log("Features: Advanced multi-phase racing with realistic effects")}},s.runQuickRace=function(){this.towerAnimationA&&(console.log("Running quick race test..."),this.towerAnimationA.startRace(Math.floor(3*Math.random())+1))},s.runPerformanceTest=function(){console.log("=== PERFORMANCE TEST ===");for(var e=Date.now(),o=0;o<100;o++)g(Math.floor(3*Math.random())+1);var n=Date.now(),t=(n-e)/100;console.log("Generated 100 configurations in "+(n-e)+"ms"),console.log("Average generation time: "+t.toFixed(2)+"ms per configuration")},o}(c)).prototype,"towerAnimationA",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=o(d.prototype,"towerAnimationP",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),R=o(d.prototype,"towerAnimationT",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=o(d.prototype,"autoRunTest",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),P=o(d.prototype,"testDelay",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 2}}),S=o(d.prototype,"enableDebugLogging",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),N=d))||N));s._RF.pop()}}}));

System.register("chunks:///_virtual/DynamicRandomPhaseConfig.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var n,t,a;return{setters:[function(e){n=e.extends,t=e.createForOfIteratorHelperLoose},function(e){a=e.cclegacy}],execute:function(){e({assignWorkerReveals:l,calculateDynamicTargetPositions:function(e){for(var n,a=[],i=o.START_Y,E=t(e);!(n=E()).done;){var A=n.value;i+=o.TOTAL_DISTANCE*A.distancePercent,a.push(i)}a.length>0&&(a[a.length-1]=o.FINISH_Y);return a},calculateStaggeredTiming:c,generateDynamicPhases:R,generateDynamicRaceConfig:function(e,n){var a,i,E=R(),A=_(),r=A.minDuration,s=A.maxDuration,S=1;if(void 0!==(null==n?void 0:n.totalDuration))i=Math.min(Math.max(n.totalDuration,r),s),a=i,console.log("[DynamicRace] Position "+e+" duration override applied: "+(i/1e3).toFixed(2)+"s");else if(a=I(),S=c(e),(i=a*S)<r?(console.log("[DynamicRace] Position "+e+" total duration "+(i/1e3).toFixed(2)+"s clamped to min "+(r/1e3).toFixed(2)+"s"),i=r):i>s&&(console.log("[DynamicRace] Position "+e+" total duration "+(i/1e3).toFixed(2)+"s clamped to max "+(s/1e3).toFixed(2)+"s"),i=s),i===r&&e>1){var u=120*(e-1);i=Math.min(r+u,s)}var N=i/a;console.log("[DynamicRace] TIMING DEBUG - Position "+e+":",{baseDuration:(a/1e3).toFixed(2)+"s",staggeredMultiplier:N.toFixed(3),totalDuration:(i/1e3).toFixed(2)+"s",timingConfig:A}),console.log("[DynamicRace] Position "+e+" race config:",{timingRange:A.minDuration/1e3+"-"+A.maxDuration/1e3+"s",baseDuration:(a/1e3).toFixed(2)+"s",staggeredMultiplier:N.toFixed(3),totalDuration:(i/1e3).toFixed(2)+"s"});var C=T(E,i),M=C.reduce((function(e,n){return e+n.duration}),0),L=C.reduce((function(e,n){return e+Math.max(n.delayBefore,0)}),0),d=C.reduce((function(e,n){var t;return e+(null!=(t=n.microPauseDuration)?t:0)}),0),D=M+L+d;console.log("[DynamicRace] Position "+e+" DURATION VERIFICATION:"),console.log("  Expected total: "+(i/1e3).toFixed(2)+"s"),console.log("  Movement total: "+(M/1e3).toFixed(2)+"s"),console.log("  Delay total: "+(L/1e3).toFixed(2)+"s"),console.log("  Micro-pause total: "+(d/1e3).toFixed(2)+"s"),console.log("  Calculated total: "+(D/1e3).toFixed(2)+"s"),console.log("  Difference: "+((D-i)/1e3).toFixed(2)+"s");var P=C.map((function(e){return e.actionId+":"+e.easing+"("+e.delayBefore+"ms)"})).join(", ");console.log("[DynamicRace] Position "+e+" EASING CHOICES: "+P);var O=E.filter((function(e){return e.id<o.EARLY_PHASE_DRAMA.EARLY_PHASE_COUNT})),m=E.filter((function(e){return e.id>=o.EARLY_PHASE_DRAMA.EARLY_PHASE_COUNT})),g=C.filter((function(e){return e.phaseId<o.EARLY_PHASE_DRAMA.EARLY_PHASE_COUNT})),h=g.filter((function(e){return"hesitation"===e.effectType||"burst"===e.effectType}));console.log("[DynamicRace] Position "+e+" DRAMA ANALYSIS:"),console.log("  Early phases (0-"+(o.EARLY_PHASE_DRAMA.EARLY_PHASE_COUNT-1)+"): "+O.map((function(e){return e.phaseType})).join(", ")),console.log("  Late phases ("+o.EARLY_PHASE_DRAMA.EARLY_PHASE_COUNT+"+): "+m.map((function(e){return e.phaseType})).join(", ")),console.log("  Dramatic actions in early phases: "+h.length+"/"+g.length),console.log("  Early phase chaos level: "+(h.length>.5*g.length?"HIGH":"MEDIUM")+" 🎭"),console.log("[DynamicRace] Position "+e+" TIGHT RACING ANALYSIS:"),console.log("  Final duration: "+(i/1e3).toFixed(2)+"s"),console.log("  Expected gaps: 1st-2nd: ~0.5s, 2nd-3rd: ~0.5s (TIGHT!) 🏁");for(var f,H=l(C),v=function(){var e=f.value,n=C.find((function(n){return n.actionId===e}));n&&(n.hasWorkerReveal=!0)},F=t(H);!(f=F()).done;)v();return{phases:E,smallActions:C,totalDuration:i,workerRevealActions:H,staggeredMultiplier:N}},generateRandomRaceDuration:I,generateSmallActions:T,getCurrentTimingConfig:_,getDelayAdaptiveEasing:s,getTimingMultiplier:S,logDynamicRaceConfig:function(e,n){if(!o.PERFORMANCE.ENABLE_DEBUG_LOGGING)return;console.log("[DynamicRace] "+n+" Configuration:"),console.log("  Total Duration: "+e.totalDuration.toFixed(0)+"ms"),console.log("  Staggered Multiplier: "+e.staggeredMultiplier.toFixed(3)),console.log("  Phases: "+e.phases.length),console.log("  Total Actions: "+e.smallActions.length),console.log("  Worker Reveals: ["+e.workerRevealActions.join(", ")+"]");for(var a,i=t(e.phases);!(a=i()).done;){var E=a.value;console.log("    "+E.description)}},randomChoice:function(e){return e[Math.floor(Math.random()*e.length)]},randomFloat:E,randomInt:i,resetTimingToDefault:function(){r(8,10)},setRaceTimingRange:r}),a._RF.push({},"09feePfEG9EAZTG9bvlKX97","DynamicRandomPhaseConfig",void 0);var o=e("DYNAMIC_RANDOM_CONFIG",{BASE_DURATION:9e3,MIN_RACE_DURATION:8e3,MAX_RACE_DURATION:1e4,RESET_Y:-200,START_Y:-10,FINISH_Y:203,TOTAL_DISTANCE:215,PHASE_GENERATION:{MIN_PHASES:3,MAX_PHASES:7,MIN_DISTANCE_PERCENT:.08,MAX_DISTANCE_PERCENT:.25,MIN_TIME_PERCENT:.1,MAX_TIME_PERCENT:.3,ACCELERATION_CHANCE:.3,STEADY_CHANCE:.4,HESITATION_CHANCE:.2,BURST_CHANCE:.1},SMALL_ACTIONS:{MIN_ACTIONS_PER_PHASE:2,MAX_ACTIONS_PER_PHASE:3,MIN_ACTION_DISTANCE:.3,MAX_ACTION_DISTANCE:.7,MIN_DELAY:20,MAX_DELAY:60,MICRO_PAUSE_CHANCE:.1,MICRO_PAUSE_DURATION:[5,25]},REALISTIC_EFFECTS:{HESITATION_CHANCE:.4,HESITATION_SLOWDOWN:1.5,HESITATION_EXTRA_DELAY:[100,300],BURST_CHANCE:.2,BURST_SPEEDUP:.7,FATIGUE_FACTOR:.85,RECOVERY_FACTOR:1.2,TIMING_VARIATION:.05,POSITION_STAGGER_DELAY:[30,80]},EARLY_PHASE_DRAMA:{EARLY_PHASE_COUNT:3,EARLY_HESITATION_CHANCE:.5,EARLY_BURST_CHANCE:.3,EARLY_TIMING_VARIATION:.15,EARLY_DISTANCE_CHAOS:.4,EARLY_BIG_JUMP_CHANCE:.3,EARLY_BIG_JUMP_MULTIPLIER:1.8,EARLY_SPEED_CHAOS:.25,EARLY_MICRO_PAUSE_CHANCE:.15,LATE_PHASE_STABILITY:.8,LATE_HESITATION_CHANCE:.2,LATE_BURST_CHANCE:.1},TIMING_MULTIPLIERS:{FIRST_PLACE:.92,SECOND_PLACE:.96,THIRD_PLACE:1.04,description:"Ultra-tight multipliers for competitive racing with 1-2s gaps"},WORKER_INTEGRATION:{REVEAL_PROGRESS_POINTS:[.3,.6],REVEAL_VARIATION:.1,USE_DYNAMIC_REVEALS:!0,USE_HEIGHT_BASED_REVEALS:!0,HEIGHT_THRESHOLDS:{WORKER2_HEIGHT:2.5,WORKER3_HEIGHT:60.5,DIRT_HEIGHT:60.5},MOVEMENT_CONFIG:{ENABLE_MOVEMENT:!0,MIN_SPEED:.02,MAX_SPEED:.04,DEFAULT_SPEED:.03,BOUNDARY_PADDING:10,SPEED_VARIATION:!0,DIRECTION_CHANGE_DELAY:100}},EASING_PROFILES:{acceleration:"sineOut",steady:"sineInOut",hesitation:"sineInOut",burst:"sineIn",fatigue:"sineOut",recovery:"sineInOut",shortDelay:"sineOut",mediumDelay:"sineInOut",longDelay:"sineIn",firstAction:"sineOut",middleAction:"sineInOut",lastAction:"sineIn",beforePause:"sineIn",afterPause:"sineOut"},PERFORMANCE:{USE_NATIVE_TWEEN:!0,CACHE_CALCULATIONS:!0,MAX_ACTIONS_PER_RACE:20,ENABLE_DEBUG_LOGGING:!0},CONFIGURABLE_TIMING:{ENABLE_CUSTOM_RANGE:!0,DEFAULT_MIN_DURATION:8e3,DEFAULT_MAX_DURATION:1e4,ALLOW_RUNTIME_CONFIG:!0,PRESERVE_RACE_BALANCE:!0,description:"Runtime configurable timing system for 8-10s races"}});function i(e,n){return Math.floor(Math.random()*(n-e+1))+e}function E(e,n){return Math.random()*(n-e)+e}var A={minDuration:8e3,maxDuration:1e4,baseDuration:9e3};function r(e,n){e<=0||n<=e?console.warn("[DynamicRace] Invalid timing range:",e,n):(A.minDuration=1e3*e,A.maxDuration=1e3*n,A.baseDuration=(e+n)/2*1e3,console.log("[DynamicRace] Timing range set to "+e+"-"+n+" seconds"))}function _(){return n({},A)}function I(){var e=E(A.minDuration,A.maxDuration);return console.log("[DynamicRace] Generated race duration: "+(e/1e3).toFixed(2)+"s from range "+A.minDuration/1e3+"-"+A.maxDuration/1e3+"s"),e}function s(e,n,t,a,i){return a?o.EASING_PROFILES.beforePause:0===n?e>150?o.EASING_PROFILES.longDelay:e>100?o.EASING_PROFILES.mediumDelay:o.EASING_PROFILES.shortDelay:n===t-1?o.EASING_PROFILES.lastAction:o.EASING_PROFILES.middleAction}function R(e){for(var n=o.PHASE_GENERATION,t=i(n.MIN_PHASES,n.MAX_PHASES),a=[],A=1,r=1,_=0;_<t;_++){var I=void 0,s=void 0;if(_===t-1)I=A,s=r;else{var R=Math.min(n.MAX_DISTANCE_PERCENT,A-(t-_-1)*n.MIN_DISTANCE_PERCENT),T=Math.min(n.MAX_TIME_PERCENT,r-(t-_-1)*n.MIN_TIME_PERCENT);A-=I=E(n.MIN_DISTANCE_PERCENT,R),r-=s=E(n.MIN_TIME_PERCENT,T)}var c=_<o.EARLY_PHASE_DRAMA.EARLY_PHASE_COUNT,S=Math.random(),l=void 0,u=void 0;c?S<.15?(l="acceleration",u=o.EASING_PROFILES.acceleration):S<.25?(l="steady",u=o.EASING_PROFILES.steady):S<.7?(l="hesitation",u=o.EASING_PROFILES.hesitation):(l="burst",u=o.EASING_PROFILES.burst):S<.5?(l="acceleration",u=o.EASING_PROFILES.acceleration):S<.8?(l="steady",u=o.EASING_PROFILES.steady):S<.9?(l="hesitation",u=o.EASING_PROFILES.hesitation):(l="burst",u=o.EASING_PROFILES.burst);var N=i(o.SMALL_ACTIONS.MIN_ACTIONS_PER_PHASE,o.SMALL_ACTIONS.MAX_ACTIONS_PER_PHASE);a.push({id:_,distancePercent:I,timePercent:s,actionCount:N,phaseType:l,easing:u,description:"Phase "+(_+1)+": "+l+" ("+(100*I).toFixed(1)+"% distance, "+(100*s).toFixed(1)+"% time)"})}return a}function T(e,n){for(var a,A=0,r=[],_=t(e);!(a=_()).done;){for(var I=a.value,R=n*I.timePercent,T=1,c=[],S=0;S<I.actionCount;S++){var l=void 0;if(S===I.actionCount-1)l=T;else{var u=Math.min(o.SMALL_ACTIONS.MAX_ACTION_DISTANCE,T-(I.actionCount-S-1)*o.SMALL_ACTIONS.MIN_ACTION_DISTANCE);T-=l=E(o.SMALL_ACTIONS.MIN_ACTION_DISTANCE,u)}var N=R*l,C="normal",M=I.id<o.EARLY_PHASE_DRAMA.EARLY_PHASE_COUNT;if(M)"hesitation"===I.phaseType&&Math.random()<o.EARLY_PHASE_DRAMA.EARLY_HESITATION_CHANCE?(N*=1.3*o.REALISTIC_EFFECTS.HESITATION_SLOWDOWN,C="hesitation"):"burst"===I.phaseType&&Math.random()<o.EARLY_PHASE_DRAMA.EARLY_BURST_CHANCE&&(N*=.8*o.REALISTIC_EFFECTS.BURST_SPEEDUP,C="burst"),N*=1+(Math.random()-.5)*o.EARLY_PHASE_DRAMA.EARLY_SPEED_CHAOS,Math.random()<o.EARLY_PHASE_DRAMA.EARLY_BIG_JUMP_CHANCE&&(l*=o.EARLY_PHASE_DRAMA.EARLY_BIG_JUMP_MULTIPLIER,C="burst");else"hesitation"===I.phaseType&&Math.random()<o.EARLY_PHASE_DRAMA.LATE_HESITATION_CHANCE?(N*=o.REALISTIC_EFFECTS.HESITATION_SLOWDOWN,C="hesitation"):"burst"===I.phaseType&&Math.random()<o.EARLY_PHASE_DRAMA.LATE_BURST_CHANCE&&(N*=o.REALISTIC_EFFECTS.BURST_SPEEDUP,C="burst");var L=void 0,d=void 0,D=0;if(M){var P=i(o.SMALL_ACTIONS.MIN_DELAY,o.SMALL_ACTIONS.MAX_DELAY),O=.6*(Math.random()-.5);L=Math.max(5,Math.round(P*(1+O))),d=Math.random()<o.EARLY_PHASE_DRAMA.EARLY_MICRO_PAUSE_CHANCE}else L=i(o.SMALL_ACTIONS.MIN_DELAY,o.SMALL_ACTIONS.MAX_DELAY),d=Math.random()<o.SMALL_ACTIONS.MICRO_PAUSE_CHANCE;if(d){var m=o.SMALL_ACTIONS.MICRO_PAUSE_DURATION;D=i(m[0],m[1])}var g=s(L,S,I.actionCount,d,I.phaseType),h={actionId:A++,phaseId:I.id,distancePercent:l,duration:Math.round(N),delayBefore:L,easing:g,hasMicroPause:d,microPauseDuration:d?D:0,effectType:C};c.push({action:h,originalDistance:Math.max(l,0)})}var f=c.reduce((function(e,n){return e+n.originalDistance}),0);f<=0&&(f=c.length);for(var H=[],v=0,F=c;v<F.length;v++){var p=F[v],G=p.action,U=p.originalDistance,Y=U/f,y=U>0?Y/U:1;G.distancePercent=Y,G.duration=Math.max(20,Math.round(G.duration*y)),H.push(G)}r.push({targetDuration:R,actions:H})}for(var x=[],B=0,b=r;B<b.length;B++){var X=b[B];x.push.apply(x,X.actions)}for(var V=function(){var e=k[W],n=e.actions,t=e.targetDuration;if(!n.length)return 1;var a=t-n.reduce((function(e,n){var t;return e+Math.max(n.delayBefore,0)+(null!=(t=n.microPauseDuration)?t:0)}),0),o=20*n.length;a<o&&(a=o);var i=n.reduce((function(e,n){return e+n.duration}),0),E=i>0?a/i:1;(!Number.isFinite(E)||E<=0)&&(E=1);var A=n.map((function(e){return Math.max(20,Math.round(e.duration*E))})),r=Math.max(o,Math.round(a))-A.reduce((function(e,n){return e+n}),0);if(r>0)A[A.length-1]+=r;else if(r<0)for(var _=A.length-1;_>=0&&r<0;_--){var I=A[_]-20;if(!(I<=0)){var s=Math.min(I,-r);A[_]-=s,r+=s}}for(var R=0;R<n.length;R++)n[R].duration=A[R]},W=0,k=r;W<k.length;W++)V();return x}function c(e){var n=S(e),t=.5*o.REALISTIC_EFFECTS.TIMING_VARIATION,a=0;a=1===e?-Math.random()*t:2===e?(Math.random()-.5)*t:Math.random()*t;var E=o.REALISTIC_EFFECTS.POSITION_STAGGER_DELAY,r=(e-1)*i(E[0],E[1])/A.baseDuration,_=n+a+r,I=o.TIMING_MULTIPLIERS,s=.01;1===e?_=Math.min(_,I.SECOND_PLACE-s):2===e?(_=Math.max(_,I.FIRST_PLACE+s),_=Math.min(_,I.THIRD_PLACE-s)):_=Math.max(_,I.SECOND_PLACE+s);var R=Math.max(.85,Math.min(1.15,_));return console.log("[DynamicRace] Position "+e+" timing: base="+n.toFixed(3)+", variation="+a.toFixed(3)+", stagger="+r.toFixed(3)+", final="+R.toFixed(3)),R}function S(e){switch(e){case 1:return o.TIMING_MULTIPLIERS.FIRST_PLACE;case 2:return o.TIMING_MULTIPLIERS.SECOND_PLACE;case 3:default:return o.TIMING_MULTIPLIERS.THIRD_PLACE}}function l(e){for(var n,a=o.WORKER_INTEGRATION.REVEAL_PROGRESS_POINTS,i=o.WORKER_INTEGRATION.REVEAL_VARIATION,E=[],A=0,r=[],_=t(e);!(n=_()).done;){var I=n.value;A+=I.distancePercent,r.push({actionId:I.actionId,progress:A})}for(var s,R=t(a);!(s=R()).done;){for(var T,c=s.value+(Math.random()-.5)*i,S=r[0],l=Math.abs(S.progress-c),u=t(r);!(T=u()).done;){var N=T.value,C=Math.abs(N.progress-c);C<l&&(l=C,S=N)}E.includes(S.actionId)||E.push(S.actionId)}return E.sort((function(e,n){return e-n}))}a._RF.pop()}}}));

System.register("chunks:///_virtual/ErrorHandler.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(r){var t,e,n,o,i,s,c,u,a,h;return{setters:[function(r){t=r.applyDecoratedDescriptor,e=r.inheritsLoose,n=r.initializerDefineProperty,o=r.assertThisInitialized},function(r){i=r.cclegacy,s=r._decorator,c=r.Node,u=r.Label,a=r.EventTarget,h=r.Component}],execute:function(){var l,f,p,E,d,y,g,w,v,H;i._RF.push({},"b4894oqJPtOx4u8Xf6LBezW","ErrorHandler",void 0);var b=s.ccclass,m=s.property;r("ErrorHandler",(l=b("ErrorHandler"),f=m(c),p=m(u),E=m(c),l(((H=function(r){function t(){for(var t,e=arguments.length,i=new Array(e),s=0;s<e;s++)i[s]=arguments[s];return(t=r.call.apply(r,[this].concat(i))||this).eventTarget=new a,t.errorHistory=[],t.maxErrorHistory=100,n(t,"errorPopup",g,o(t)),n(t,"errorMessageLabel",w,o(t)),n(t,"reconnectButton",v,o(t)),t}e(t,r),t.getInstance=function(){return this._instance};var i=t.prototype;return i.start=function(){t._instance=this,this.privateSetupErrorPopup(),console.log("[ErrorHandler] Initialized")},i.privateSetupErrorPopup=function(){this.errorPopup&&(this.errorPopup.active=!1),this.reconnectButton&&this.reconnectButton.on(c.EventType.TOUCH_END,this.onReconnectClicked,this)},i.HandleError=function(r){switch(console.error("[ErrorHandler] "+r.type.toUpperCase()+" Error:",r.message),this.addToHistory(r),r.type){case"connection":this.handleConnectionError(r);break;case"authentication":this.handleAuthenticationError(r);break;case"betting":this.handleBettingError(r);break;case"game":this.handleGameError(r);break;case"balance":this.handleBalanceError(r);break;default:this.handleGenericError(r)}this.eventTarget.emit("error-occurred",r)},i.handleConnectionError=function(r){var t,e=this;(((t={})[-1]=function(){return e.showError("연결 오류가 발생했습니다. 네트워크를 확인해주세요.",!0)},t[-2]=function(){return e.showError("서버 응답을 처리할 수 없습니다.",!1)},t[-3]=function(){return e.showError("연결이 끊어졌습니다. 재연결을 시도해주세요.",!0)},t[1006]=function(){return e.showError("서버 연결이 비정상적으로 종료되었습니다.",!0)},t[1011]=function(){return e.showError("서버에서 오류가 발생했습니다.",!0)},t)[r.code]||function(){return e.showError("연결 문제가 발생했습니다.",!0)})()},i.handleAuthenticationError=function(r){var t,e=this;(((t={})[401]=function(){e.showError("인증에 실패했습니다. 다시 로그인해주세요.",!1),e.redirectToLogin()},t[403]=function(){return e.showError("접근 권한이 없습니다.",!1)},t[419]=function(){e.showError("세션이 만료되었습니다. 다시 로그인해주세요.",!1),e.redirectToLogin()},t)[r.code]||function(){return e.showError("인증 오류가 발생했습니다.",!1)})()},i.handleBettingError=function(r){var t,e=this;(((t={})[1001]=function(){return e.showError("베팅 시간이 종료되었습니다.",!1)},t[1002]=function(){return e.showError("잔액이 부족합니다.",!1)},t[1003]=function(){return e.showError("최소 베팅 금액을 확인해주세요.",!1)},t[1004]=function(){return e.showError("최대 베팅 금액을 초과했습니다.",!1)},t[1005]=function(){return e.showError("이미 베팅이 완료되었습니다.",!1)},t)[r.code]||function(){return e.showError("베팅 처리 중 오류가 발생했습니다.",!1)})()},i.handleGameError=function(r){var t,e=this;(((t={})[2001]=function(){return e.showError("게임 라운드를 찾을 수 없습니다.",!1)},t[2002]=function(){return e.showError("게임 상태가 올바르지 않습니다.",!0)},t[2003]=function(){return e.showError("게임 데이터 동기화 오류입니다.",!0)},t)[r.code]||function(){return e.showError("게임 오류가 발생했습니다.",!0)})()},i.handleBalanceError=function(r){var t,e=this;(((t={})[3001]=function(){return e.showError("잔액 정보를 가져올 수 없습니다.",!0)},t[3002]=function(){return e.showError("잔액 업데이트에 실패했습니다.",!0)},t)[r.code]||function(){return e.showError("잔액 처리 중 오류가 발생했습니다.",!0)})()},i.handleGenericError=function(r){this.showError("오류가 발생했습니다: "+r.message,!1)},i.showError=function(r,t){var e=this;void 0===t&&(t=!1),this.errorMessageLabel&&(this.errorMessageLabel.string=r),this.reconnectButton&&(this.reconnectButton.active=t),this.errorPopup&&(this.errorPopup.active=!0),t||this.scheduleOnce((function(){e.HideError()}),5)},i.HideError=function(){this.errorPopup&&(this.errorPopup.active=!1)},i.onReconnectClicked=function(){this.HideError(),this.eventTarget.emit("reconnect-requested")},i.redirectToLogin=function(){this.eventTarget.emit("logout-required"),this.scheduleOnce((function(){"undefined"!=typeof window&&window.location&&(window.location.href="/minigame/landing/index.php")}),2)},i.addToHistory=function(r){this.errorHistory.unshift(r),this.errorHistory.length>this.maxErrorHistory&&(this.errorHistory=this.errorHistory.slice(0,this.maxErrorHistory))},i.GetErrorStats=function(){var r={total:this.errorHistory.length,byType:{},recent:this.errorHistory.slice(0,10),lastHour:0},t=new Date(Date.now()-36e5);return this.errorHistory.forEach((function(e){r.byType[e.type]=(r.byType[e.type]||0)+1,new Date(e.timestamp)>t&&r.lastHour++})),r},i.ClearErrorHistory=function(){this.errorHistory=[],console.log("[ErrorHandler] Error history cleared")},i.IsInErrorState=function(){return this.errorHistory.slice(0,5).filter((function(r){return"connection"===r.type||"authentication"===r.type})).length>=3},i.GetRecoverySuggestions=function(){var r=[],t=this.GetErrorStats();return t.byType.connection>3&&r.push("네트워크 연결을 확인해주세요."),t.byType.authentication>1&&r.push("다시 로그인해주세요."),t.byType.betting>2&&r.push("잔액과 베팅 한도를 확인해주세요."),t.lastHour>10&&r.push("잠시 후 다시 시도해주세요."),r},i.On=function(r,t,e){this.eventTarget.on(r,t,e)},i.Off=function(r,t,e){this.eventTarget.off(r,t,e)},t.createError=function(r,t,e,n){return{code:r,message:t,type:e,timestamp:(new Date).toISOString(),details:n}},i.onDestroy=function(){this.eventTarget.removeAll(this),this.reconnectButton&&this.reconnectButton.off(c.EventType.TOUCH_END,this.onReconnectClicked,this)},t}(h))._instance=null,g=t((y=H).prototype,"errorPopup",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=t(y.prototype,"errorMessageLabel",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=t(y.prototype,"reconnectButton",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),d=y))||d));i._RF.pop()}}}));

System.register("chunks:///_virtual/EventManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Singleton.ts"],(function(e){var t,n,E,r,o;return{setters:[function(e){t=e.createClass},function(e){n=e.cclegacy,E=e._decorator,r=e.EventTarget},function(e){o=e.singleton}],execute:function(){var a;e({emitEvent:function(e,t){c.instance.emit(e,t)},getEventManager:function(){return c.instance},offEvent:function(e,t,n){c.instance.off(e,t,n)},onEvent:function(e,t,n){c.instance.on(e,t,n)}}),n._RF.push({},"bee05Dk4PNKjacQIm9d7ILk","EventManager",void 0);var _=E.ccclass,c=e("EventManager",_("EventManager")(a=o(a=function(){function e(){this.eventTarget=new r}var n=e.prototype;return n.emit=function(e,t){this.eventTarget.emit(e,t)},n.on=function(e,t,n){this.eventTarget.on(e,t,n)},n.off=function(e,t,n){this.eventTarget.off(e,t,n)},t(e,null,[{key:"instance",get:function(){return e.instance}}]),e}())||a)||a);e("GAME_EVENTS",{GAME_STARTED:"game-started",GAME_STATUS_CHANGED:"game-status-changed",GAME_RESULT:"game-result",GAME_ENDED:"game-ended",SHOW_RESULT_POPUP:"show-result-popup",TOWER_RESULT:"tower-result",TOWER_RESULT_ERROR:"tower-result-error",TOWER_PHASE_CHANGED:"tower-phase-changed",TOWER_INDIVIDUAL_COMPLETE:"tower-individual-complete",TOWER_ALL_RACES_COMPLETE:"tower-all-races-complete",TOWER_DRAMATIC_EVENT:"tower-dramatic-event",TOWER_FIRST_PLACE_FINISHED:"tower-first-place-finished",TOWER_PHASE_1_COMPLETE:"tower-phase-1-complete",TOWER_PHASE_2_COMPLETE:"tower-phase-2-complete",TOWER_RACE_FINISHED:"tower-race-finished",GAME_RESET_UI:"game-reset-ui",ROUND_COUNTDOWN_STARTED:"round-countdown-started",SHOW_INFO_PANEL:"show-info-panel",SCREEN_SHAKE:"screen-shake",AUTH_SUCCESS:"auth-success",AUTH_FAILED:"auth-failed",TOKEN_REQUIRED:"token-required",BET_PLACED:"bet-placed",BET_CANCELLED:"bet-cancelled",BALANCE_UPDATED:"balance-updated",CONNECTION_OPENED:"connection-opened",CONNECTION_CLOSED:"connection-closed",CONNECTION_ERROR:"connection-error",HISTORY_UPDATED:"history-updated",HISTORY_ENTRY_ADDED:"history-entry-added",HISTORY_CLEARED:"history-cleared",UI_READY:"ui-ready",UI_ERROR:"ui-error",STARTUP_POPUP_SHOWN:"startup-popup-shown",STARTUP_POPUP_HIDDEN:"startup-popup-hidden",RESULT_POPUP_SHOWN:"result-popup-shown",RESULT_POPUP_HIDDEN:"result-popup-hidden",LANGUAGE_CHANGED:"language-changed",ERROR_OCCURRED:"error-occurred",RECONNECT_REQUESTED:"reconnect-requested"});n._RF.pop()}}}));

System.register("chunks:///_virtual/FakeLoadingView.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EventManager.ts"],(function(e){var t,s,a,r,i,n,o,u,c,g,l,d,h;return{setters:[function(e){t=e.applyDecoratedDescriptor,s=e.inheritsLoose,a=e.initializerDefineProperty,r=e.assertThisInitialized,i=e.asyncToGenerator,n=e.regeneratorRuntime},function(e){o=e.cclegacy,u=e._decorator,c=e.ProgressBar,g=e.Label,l=e.Component},function(e){d=e.EventManager,h=e.GAME_EVENTS}],execute:function(){var p,f,S,L,m,y,v;o._RF.push({},"d00cfokO59F4qytaqb1ILU6","FakeLoadingView",void 0);var P=u.ccclass,b=u.property;e("FakeLoadingView",(p=P("FakeLoadingView"),f=b(c),S=b(g),p((y=t((m=function(e){function t(){for(var t,s=arguments.length,i=new Array(s),n=0;n<s;n++)i[n]=arguments[n];return t=e.call.apply(e,[this].concat(i))||this,a(t,"progressBar",y,r(t)),a(t,"statusLabel",v,r(t)),t.isLoadingComplete=!1,t.hasError=!1,t.hasSceneLoadStarted=!1,t.hasReceivedGameStatus=!1,t.isPersistentRoot=!1,t.isWaitingForGameStatus=!1,t.isFinalized=!1,t}s(t,e);var o=t.prototype;return o.start=function(){this.initializeProgressBar(),this.startResourceLoading()},o.handleFirstGameStatusChanged=function(e){var t;console.log("[LoadingScene] GAME_STATUS_CHANGED received:",null!=(t=null==e?void 0:e.status)?t:"unknown"),this.node.destroy()},o.initializeProgressBar=function(){this.progressBar?(this.progressBar.progress=0,console.log("[LoadingScene] Progress bar initialized to 0%")):console.warn("[LoadingScene] Progress bar not assigned in editor")},o.startResourceLoading=function(){var e=i(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.updateProgress(.1,"Initializing..."),e.next=4,this.delay(200);case 4:return this.updateProgress(.4,"Loading assets..."),e.next=7,this.delay(300);case 7:return this.updateProgress(.7,"Loading scenes..."),e.next=10,this.delay(200);case 10:return this.updateProgress(.9,"Preparing game..."),e.next=13,this.delay(200);case 13:this.onResourcesReady(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(0),console.error("[LoadingScene] Loading failed:",e.t0);case 19:case"end":return e.stop()}}),e,this,[[0,16]])})));return function(){return e.apply(this,arguments)}}(),o.onResourcesReady=function(){this.hasError||(this.isLoadingComplete=!0,this.hasReceivedGameStatus||this.updateProgress(1,"Waiting for game data..."),this.waitForGameStatus())},o.waitForGameStatus=function(){this.hasReceivedGameStatus||this.hasError||this.isWaitingForGameStatus||(d.instance.on(h.GAME_STATUS_CHANGED,this.handleFirstGameStatusChanged,this),this.isWaitingForGameStatus=!0)},o.updateProgress=function(e,t){this.progressBar.progress=e,this.updateStatusMessage(t),console.log("[LoadingScene] Progress: "+Math.round(100*e)+"% - "+t)},o.delay=function(e){return new Promise((function(t){return setTimeout(t,e)}))},o.updateStatusMessage=function(e){this.statusLabel&&(this.statusLabel.string=e,console.log("[LoadingScene] Status: "+e))},t}(l)).prototype,"progressBar",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=t(m.prototype,"statusLabel",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=m))||L));o._RF.pop()}}}));

System.register("chunks:///_virtual/GameEvents.ts",["cc"],(function(e){var r;return{setters:[function(e){r=e.cclegacy}],execute:function(){var n;e({getPlayerFromPosition:o,getPositionOfPlayer:function(e,r){var n=e[0],o=e[1],E=e[2];switch(r){case t.A:return n;case t.P:return o;case t.T:return E;default:throw new Error("Unknown player: "+r)}},getWinnerFromPositions:function(e){return o(e,1)}}),r._RF.push({},"9422cZ5jwpDkKEgLsY3edEw","GameEvents",void 0);e("GameEventCode",function(e){return e[e.GAME_INIT=101]="GAME_INIT",e[e.GAME_STATUS_UPDATE=108]="GAME_STATUS_UPDATE",e[e.GAME_RESULT=109]="GAME_RESULT",e[e.BALANCE_UPDATE=204]="BALANCE_UPDATE",e}({})),e("GameStatus",function(e){return e[e.WAITING=1]="WAITING",e[e.BETTING_OPEN=2]="BETTING_OPEN",e[e.BETTING_CLOSED=3]="BETTING_CLOSED",e[e.DEALING=4]="DEALING",e[e.COMPLETED=5]="COMPLETED",e}({}));var t=e("TowerPlayer",function(e){return e.A="A",e.P="P",e.T="T",e}({}));e("SocketState",function(e){return e[e.CONNECTING=0]="CONNECTING",e[e.OPEN=1]="OPEN",e[e.CLOSING=2]="CLOSING",e[e.CLOSED=3]="CLOSED",e}({})),e("TOWER_PLAYERS",((n={})[t.A]={player:t.A,color:"red",displayName:"Player Red"},n[t.P]={player:t.P,color:"green",displayName:"Player Green"},n[t.T]={player:t.T,color:"blue",displayName:"Player Blue"},n)),e("TOWER_BET_CODES",{FIRST_PLACE:1,SEQUENCE:2,GROUP_SELECT:3});function o(e,r){if(!e||!Array.isArray(e)||3!==e.length)return console.error("[GameEvents] Invalid positions array:",e),null;if("number"!=typeof r||r<1||r>3)return console.error("[GameEvents] Invalid target position:",r),null;var n=e[0],o=e[1],E=e[2];return"number"!=typeof n||"number"!=typeof o||"number"!=typeof E?(console.error("[GameEvents] Position values must be numbers:",{aPos:n,pPos:o,tPos:E}),null):n===r?t.A:o===r?t.P:E===r?t.T:(console.error("[GameEvents] No player found at position "+r,{positions:e}),null)}r._RF.pop()}}}));

System.register("chunks:///_virtual/GameManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./SocketManager.ts","./AuthManager.ts","./SoundManager.ts","./LocalizationManager.ts","./EventManager.ts","./GameEvents.ts"],(function(e){var t,a,n,r,o,i,s,c,u,l,g,d,m,h,f,p,v,M,G,T,y;return{setters:[function(e){t=e.applyDecoratedDescriptor,a=e.inheritsLoose,n=e.initializerDefineProperty,r=e.assertThisInitialized,o=e.extends,i=e.asyncToGenerator,s=e.regeneratorRuntime},function(e){c=e.cclegacy,u=e._decorator,l=e.EventTarget,g=e.Component},function(e){d=e.SocketManager},function(e){m=e.AuthManager},function(e){h=e.SoundManager},function(e){f=e.LocalizationManager},function(e){p=e.EventManager,v=e.GAME_EVENTS},function(e){M=e.GameStatus,G=e.TowerPlayer,T=e.getWinnerFromPositions,y=e.getPlayerFromPosition}],execute:function(){var w,k,P,b,S,A;c._RF.push({},"217b0YoQopDDo0f/tJf7zYZ","GameManager",void 0);var R=u.ccclass,I=u.property;e("GameManager",(w=R("GameManager"),k=I(h),w(((A=function(e){function t(){for(var t,a=arguments.length,o=new Array(a),i=0;i<a;i++)o[i]=arguments[i];return t=e.call.apply(e,[this].concat(o))||this,n(t,"soundManager",S,r(t)),t.socketManager=void 0,t.authManager=void 0,t.localizationManager=void 0,t.eventTarget=new l,t.currentRound=0,t.currentRoundId="",t.gameStatus=M.WAITING,t.timeRemains=0,t.balance=0,t.isConnected=!1,t.currentBet={totalAmount:0},t.canBet=!1,t.bettingHistory=[],t}a(t,e);var c=t.prototype;return c.mapSymbolToPlayer=function(e){if(!e)return null;var t=e.trim();if(!t)return null;switch(t.toUpperCase()){case"아":case"A":case"PLAYER_A":return G.A;case"파":case"P":case"PLAYER_P":return G.P;case"트":case"T":case"PLAYER_T":return G.T;default:return null}},t.getInstance=function(){return this._instance},c.start=function(){if(t._instance=this,this.privateInitializeManagers(),!this.privateSetupEventListeners())return console.log("[GameManager] Managers not ready, scheduling retry..."),void this.privateRetryInitialization(1);this.privateStartGame()},c.StartWithToken=function(e,t){if(console.log("[GameManager] Starting game with provided token..."),!e||0===e.trim().length)return console.error("[GameManager] Token parameter is required"),void this.eventTarget.emit("token-required",{message:"Token parameter is required to start the game",suggestion:"Please provide a valid authentication token"});this.privateStartGameWithToken(e.trim(),t)},c.startGame=function(){var e=i(s().mark((function e(){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,console.log("[GameManager] Starting game..."),this.authManager.autoInitialize().success){e.next=8;break}return console.log("[GameManager] No URL token found"),this.eventTarget.emit("token-required",{message:"Please provide authentication token",suggestion:"Use startWithToken(token) method or add token to URL"}),p.instance.emit("token-required",{message:"Please provide authentication token",suggestion:"Use startWithToken(token) method or add token to URL"}),e.abrupt("return");case 8:return console.log("[GameManager] Authentication successful"),e.next=11,this.connectToGame();case 11:e.next=18;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("[GameManager] Failed to start game:",e.t0),this.eventTarget.emit("startup-failed",{error:e.t0.message}),p.instance.emit("startup-failed",{error:e.t0.message});case 18:case"end":return e.stop()}}),e,this,[[0,13]])})));return function(){return e.apply(this,arguments)}}(),c.startGameWithToken=function(){var e=i(s().mark((function e(t,a){var n;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,(n=this.authManager.initializeWithToken(t,a)).success){e.next=7;break}return console.error("[GameManager] Token authentication failed:",n.error),this.eventTarget.emit("authentication-failed",{error:n.error,suggestion:"Please check if the token is valid and not expired"}),p.instance.emit("authentication-failed",{error:n.error,suggestion:"Please check if the token is valid and not expired"}),e.abrupt("return");case 7:return console.log("[GameManager] Token authentication successful"),e.next=10,this.connectToGame();case 10:e.next=17;break;case 12:e.prev=12,e.t0=e.catch(0),console.error("[GameManager] Failed to start game with token:",e.t0),this.eventTarget.emit("startup-failed",{error:e.t0.message}),p.instance.emit("startup-failed",{error:e.t0.message});case 17:case"end":return e.stop()}}),e,this,[[0,12]])})));return function(t,a){return e.apply(this,arguments)}}(),c.connectToGame=function(){var e=i(s().mark((function e(){var t,a;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this.authManager.getToken(),a=this.authManager.getAuthData(),e.next=4,this.socketManager.ConnectWithToken(t,a.playerId);case 4:e.sent?(console.log("[GameManager] Game started successfully"),this.isConnected=!0,this.eventTarget.emit("game-started",{token:t.substring(0,20)+"...",playerId:a.playerId}),p.instance.emit("game-started",{token:t.substring(0,20)+"...",playerId:a.playerId}),this.soundManager&&(console.log("[GameManager] Starting BGM for game session"),this.soundManager.setBGMEnabled(!0))):(console.error("[GameManager] Failed to connect to game server"),this.eventTarget.emit("connection-failed",{error:"Failed to connect to game server"}),p.instance.emit("connection-failed",{error:"Failed to connect to game server"}));case 6:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),c.onGameInit=function(e){var t;console.log("[GameManager] Game initialized:",e.data);var a=e.data,n=a.user,r=a.bet;n&&"number"==typeof n.balance&&(this.balance=n.balance),this.authManager.setAuthenticationStatus(!0,(null==(t=a.session)?void 0:t.toString())||a.sessionId||"",(null==n?void 0:n.balance)||this.balance),this.authManager.updateUserProfile(n,r),this.eventTarget.emit("user-data-loaded",{userProfile:n,bettingLimits:r,gameSession:a.session,gameCode:a.gamecode}),console.log("[GameManager] User profile loaded:",{id:null==n?void 0:n.id,nickname:null==n?void 0:n.nickname,balance:null==n?void 0:n.balance,currency:null==n?void 0:n.currency})},c.onGameStatusUpdate=function(e){var t=e.data;this.currentRound=t.round,this.currentRoundId=t.roundid,this.gameStatus=t.status,this.timeRemains=t.timeremains,this.canBet=t.status===M.BETTING_OPEN&&!t.stop_bet;var a=t.roundid?parseInt(t.roundid):t.round?parseInt(t.round.toString().slice(-4)):0;this.eventTarget.emit("game-status-changed",{status:t.status,timeRemains:t.timeremains,canBet:this.canBet,round:a}),p.instance.emit(v.GAME_STATUS_CHANGED,{status:t.status,timeRemains:t.timeremains,canBet:this.canBet,round:a,server_time:t.server_time}),console.log("[GameManager] Status: "+t.status+", Time: "+t.timeremains+"s, Round: "+t.round)},c.onGameResult=function(e){try{var t=e.data;console.log("[GameManager] Tower game result received, Round:",t.round),console.log("[GameManager] Full event data:",e);var a=this.parseTowerResult(t);if(!a)return console.error("[GameManager] Failed to parse tower result data"),void p.instance.emit(v.TOWER_RESULT_ERROR,{error:"Failed to parse tower result data",round:t.round,rawData:t,timestamp:(new Date).toISOString()});var n={round:t.round,result:a.winner,positions:a.positions,firstPlace:a.firstPlace,secondPlace:a.secondPlace,thirdPlace:a.thirdPlace,timestamp:parseInt(t.timestamp)||Date.now()},r=0;this.currentBet.totalAmount>0&&(r=this.calculateTowerWinnings(a,t.wincode)),this.bettingHistory.unshift(n),this.bettingHistory.length>50&&this.bettingHistory.pop(),this.currentBet={totalAmount:0};var o={code:109,round:t.round,roundid:t.roundid||t.round.toString(),winner:a.winner,positions:a.positions,firstPlace:a.firstPlace,secondPlace:a.secondPlace,thirdPlace:a.thirdPlace,winAmount:r,wincode:t.wincode,timestamp:t.timestamp||(new Date).toISOString(),history:n};this.eventTarget.emit("game-result",o),p.instance.emit(v.TOWER_RESULT,o),p.instance.emit(v.GAME_RESULT,o)}catch(t){var i;console.error("[GameManager] Unexpected error handling tower game result:",t),console.error("[GameManager] Event data:",e),p.instance.emit(v.TOWER_RESULT_ERROR,{error:"Unexpected error processing tower result",details:t.message||t.toString(),round:null==e||null==(i=e.data)?void 0:i.round,rawData:null==e?void 0:e.data,timestamp:(new Date).toISOString()})}},c.onBalanceUpdate=function(e){var t=e.data,a="number"==typeof t.balance?t.balance:0;this.balance=a,this.authManager.updateBalance(a),console.log("[GameManager] Balance updated:",a),this.eventTarget.emit("balance-changed",{balance:a,transaction:t.transaction})},c.onAuthenticated=function(){console.log("[GameManager] Authenticated successfully")},c.onDataSyncRequested=function(e){console.log("[GameManager] Data sync requested:",e),this.eventTarget.emit("data-sync-requested",{timestamp:e.timestamp,reason:e.reason,currentRound:this.currentRound,gameStatus:this.gameStatus,timeRemains:this.timeRemains,balance:this.balance});var t=this.currentRoundId?parseInt(this.currentRoundId):this.currentRound?parseInt(this.currentRound.toString().slice(-4)):0;p.instance.emit("game-status-changed",{status:this.gameStatus,timeRemains:this.timeRemains,canBet:this.canBet,round:t}),console.log("[GameManager] Data sync completed - UI refreshed")},c.onSocketError=function(e){console.error("[GameManager] Socket error:",e),this.isConnected=!1,this.eventTarget.emit("connection-error",e),p.instance.emit("connection-error",e)},c.PlaceBet=function(e,t){if(!this.canBet)return console.warn("[GameManager] Betting not available"),!1;if(t<=0||t>this.balance)return console.warn("[GameManager] Invalid bet amount"),!1;this.currentBet.mainBet={type:e,amount:t,roundId:this.currentRoundId,timestamp:(new Date).toISOString()},this.currentBet.totalAmount=t;var a=this.socketManager.placeBet(e,t,this.currentRoundId);return a&&(console.log("[GameManager] Bet placed: "+e+" - "+t),this.eventTarget.emit("bet-placed",{type:e,amount:t,round:this.currentRound}),this.soundManager&&this.soundManager.playToggleSound(!0)),a},c.CancelBets=function(){if(0===this.currentBet.totalAmount)return console.warn("[GameManager] No bets to cancel"),!1;var e=this.socketManager.cancelBets();return e&&(this.currentBet={totalAmount:0},console.log("[GameManager] Bets cancelled"),this.eventTarget.emit("bets-cancelled")),e},c.parseTowerResult=function(e){try{if(!e.values||!Array.isArray(e.values)||e.values.length<2)return console.error("[GameManager] Invalid tower values format - missing or invalid values array:",e.values),null;var t=Array.isArray(e.values[0])?e.values[0]:[],a=Array.isArray(e.values[1])?e.values[1]:[],n=[0,0,0];if(3===a.length)for(var r=0;r<a.length;r++){var o=this.mapSymbolToPlayer(a[r]);if(o){var i=r+1;o===G.A&&(n[0]=i),o===G.P&&(n[1]=i),o===G.T&&(n[2]=i)}}for(var s=[G.A,G.P,G.T],c=0;c<s.length;c++){var u=s[c],l=u===G.A?0:u===G.P?1:2;if(0===n[l]){var g=t[c];"number"==typeof g&&(n[l]=g)}}if(n.some((function(e){return 0===e})))for(var d=[1,2,3],m=0;m<n.length;m++)0===n[m]&&(n[m]=d[m]);for(var h=0;h<n.length;h++){var f=n[h];if("number"!=typeof f||f<1||f>3||!Number.isInteger(f))return console.error("[GameManager] Invalid derived position value:",{index:h,value:f,symbols:a,rawPositions:t}),null}if(3!==new Set(n).size)return console.error("[GameManager] Duplicate positions detected after mapping:",n),null;var p=T(n);if(!p)return console.error("[GameManager] Failed to determine winner from positions:",n),null;var v=y(n,1),M=y(n,2),w=y(n,3);return v&&M&&w?(console.log("[GameManager] Tower result parsed successfully:",{positions:n,winner:p,firstPlace:v,secondPlace:M,thirdPlace:w}),{winner:p,positions:n,firstPlace:v,secondPlace:M,thirdPlace:w}):(console.error("[GameManager] Failed to determine place finishers:",{firstPlace:v,secondPlace:M,thirdPlace:w,positions:n}),null)}catch(t){return console.error("[GameManager] Unexpected error parsing tower result:",t),console.error("[GameManager] Input data:",e),null}},c.calculateTowerWinnings=function(e,t){if(!this.currentBet.mainBet||null==t||!t.wins)return console.log("[GameManager] No bet placed or invalid wincode, no winnings"),0;var a=this.currentBet.mainBet,n=0;try{1===a.betcode?n=this.calculateFirstPlaceWinnings(a,e.winner,t.wins[1]):2===a.betcode?n=this.calculateSequenceWinnings(a,e,t.wins[2]):3===a.betcode?n=this.calculateGroupWinnings(a,e,t.wins[3]):console.warn("[GameManager] Unknown bet type:",a.type),console.log("[GameManager] Winnings calculated:",{betType:a.type,betAmount:a.amount,winnings:n,wincode:t.wins})}catch(e){console.error("[GameManager] Error calculating winnings:",e),n=0}return n},c.calculateFirstPlaceWinnings=function(e,t,a){if(!a||"number"!=typeof a.value)return console.warn("[GameManager] Invalid wincode data for betcode 1:",a),0;var n={1:"A",2:"P",3:"T"}[a.value];return n?this.getBetPlayerFromType(e.type)===n&&t===n?2.7*e.amount:0:(console.warn("[GameManager] Invalid wincode value for betcode 1:",a.value),0)},c.calculateSequenceWinnings=function(e,t,a){if(!a||"number"!=typeof a.value)return console.warn("[GameManager] Invalid wincode data for betcode 2:",a),0;var n=[null,[G.A,G.P,G.T],[G.A,G.T,G.P],[G.P,G.A,G.T],[G.P,G.T,G.A],[G.T,G.A,G.P],[G.T,G.P,G.A]][a.value];if(!n)return console.warn("[GameManager] Invalid wincode value for betcode 2:",a.value),0;if("sequence"===e.type&&e.betvalue===a.value){var r=[t.firstPlace,t.secondPlace,t.thirdPlace];if(n.every((function(e,t){return e===r[t]})))return 5*e.amount}return 0},c.calculateGroupWinnings=function(e,t,a){if(!a||"number"!=typeof a.value)return console.warn("[GameManager] Invalid wincode data for betcode 3:",a),0;var n=[null,[[G.A,G.P,G.T],[G.A,G.T,G.P],[G.P,G.A,G.T]],[[G.P,G.T,G.A],[G.T,G.A,G.P],[G.T,G.P,G.A]]][a.value];if(!n)return console.warn("[GameManager] Invalid wincode value for betcode 3:",a.value),0;if("group"===e.type&&e.betvalue===a.value){var r=[t.firstPlace,t.secondPlace,t.thirdPlace];if(n.some((function(e){return e.every((function(e,t){return e===r[t]}))})))return 1.95*e.amount}return 0},c.getBetPlayerFromType=function(e){return e.includes("_A")?"A":e.includes("_P")?"P":e.includes("_T")?"T":null},c.GetCurrentRound=function(){return this.currentRound},c.GetGameStatus=function(){return this.gameStatus},c.testTowerPositionParsing=function(){console.log("[GameManager] Running comprehensive tower data processing tests..."),console.log("[GameManager] Test 1: Valid position data");var e=this.parseTowerResult({values:[[3,1,2],["트","아","파"]],wincode:{wins:{1:{value:2},2:{value:3},3:{value:1}}},round:202509100946,roundid:"946"});if(e){var t=G.P,a=G.P,n=G.T,r=G.A;e.winner===t&&e.firstPlace===a&&e.secondPlace===n&&e.thirdPlace===r?console.log("[GameManager] ✅ Test 1 PASSED - Valid data parsed correctly"):(console.error("[GameManager] ❌ Test 1 FAILED - Incorrect parsing"),console.error("Expected:",{expectedWinner:t,expectedFirst:a,expectedSecond:n,expectedThird:r}),console.error("Actual:",e))}else console.error("[GameManager] ❌ Test 1 FAILED - null result for valid data");console.log("[GameManager] Test 2: Invalid data with duplicate positions");null===this.parseTowerResult({values:[[1,1,2],["트","아","파"]]})?console.log("[GameManager] ✅ Test 2 PASSED - Duplicate positions correctly rejected"):console.error("[GameManager] ❌ Test 2 FAILED - Should have rejected duplicate positions"),console.log("[GameManager] Test 3: Invalid data with out-of-range position");null===this.parseTowerResult({values:[[4,1,2],["트","아","파"]]})?console.log("[GameManager] ✅ Test 3 PASSED - Out-of-range position correctly rejected"):console.error("[GameManager] ❌ Test 3 FAILED - Should have rejected out-of-range position"),console.log("[GameManager] Enhanced tower data processing tests completed")},c.testWincodeProcessing=function(){console.log("[GameManager] Running wincode processing tests...");var e={winner:"A",positions:[1,2,3],firstPlace:G.A,secondPlace:G.P,thirdPlace:G.T};console.log("[GameManager] Test: Betcode 1 winning bet"),this.currentBet={mainBet:{type:"first_place_A",amount:1e3,roundId:"test",timestamp:(new Date).toISOString(),betcode:1,betvalue:1},totalAmount:1e3};var t={wins:{1:{value:1},2:{value:1},3:{value:1}}},a=this.calculateTowerWinnings(e,t);2700===a?console.log("[GameManager] ✅ Betcode 1 test PASSED - Correct winnings calculated"):console.error("[GameManager] ❌ Betcode 1 test FAILED - Expected 2700, got:",a),console.log("[GameManager] Test: Betcode 1 losing bet"),this.currentBet.mainBet&&(this.currentBet.mainBet.betvalue=2);var n=this.calculateTowerWinnings(e,t);0===n?console.log("[GameManager] ✅ Betcode 1 losing test PASSED - No winnings for wrong bet"):console.error("[GameManager] ❌ Betcode 1 losing test FAILED - Expected 0, got:",n),console.log("[GameManager] Wincode processing tests completed")},c.GetTimeRemains=function(){return this.timeRemains},c.GetBalance=function(){return this.balance},c.CanPlaceBet=function(){return this.canBet},c.GetCurrentBet=function(){return o({},this.currentBet)},c.GetBettingHistory=function(){return[].concat(this.bettingHistory)},c.IsGameConnected=function(){return this.isConnected},c.GetUserProfile=function(){return this.authManager.getUserProfile()},c.GetBettingLimits=function(){return this.authManager.getBettingLimits()},c.GetUserNickname=function(){return this.authManager.getUserNickname()},c.On=function(e,t,a){this.eventTarget.on(e,t,a)},c.Off=function(e,t,a){this.eventTarget.off(e,t,a)},c.ToggleBGM=function(){return!!this.soundManager&&this.soundManager.toggleBGM()},c.IsBGMEnabled=function(){return!!this.soundManager&&this.soundManager.getBGMEnabled()},c.GetLocalizationManager=function(){return this.localizationManager},c.privateInitializeManagers=function(){this.socketManager=d.GetInstance(),this.authManager=m.getInstance(),this.soundManager=h.instance,this.localizationManager=f.instance,this.socketManager&&this.authManager?console.log("[GameManager] Managers initialized"):console.error("[GameManager] Required managers not found!")},c.privateSetupEventListeners=function(){return this.socketManager&&this.authManager?(this.socketManager.on("game-init",this.onGameInit,this),this.socketManager.on("game-status-update",this.onGameStatusUpdate,this),this.socketManager.on("game-result",this.onGameResult,this),this.socketManager.on("balance-update",this.onBalanceUpdate,this),this.socketManager.on("error",this.onSocketError,this),this.socketManager.on("authenticated",this.onAuthenticated,this),this.socketManager.on("data-sync-requested",this.onDataSyncRequested,this),console.log("[GameManager] Event listeners setup complete"),!0):(console.warn("[GameManager] Cannot setup event listeners - managers not available"),!1)},c.privateStartGame=function(){var e=i(s().mark((function e(){var t,a;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,console.log("[GameManager] Starting game..."),this.authManager.autoInitialize().success){e.next=7;break}return console.log("[GameManager] No URL token found"),this.eventTarget.emit("token-required",{message:"No authentication token found",suggestion:"Please provide a valid authentication token"}),e.abrupt("return");case 7:return t=this.authManager.getToken(),a=this.authManager.getAuthData(),e.next=11,this.socketManager.ConnectWithToken(t,a.playerId);case 11:if(!e.sent){e.next=19;break}console.log("[GameManager] Game started successfully"),this.isConnected=!0,this.eventTarget.emit("game-started",{success:!0,message:"Game started successfully"}),p.instance.emit("game-started",{success:!0,message:"Game started successfully"}),e.next=20;break;case 19:throw new Error("Failed to connect to game server");case 20:e.next=27;break;case 22:e.prev=22,e.t0=e.catch(0),console.error("[GameManager] Failed to start game:",e.t0),this.eventTarget.emit("game-start-failed",{error:e.t0.message}),p.instance.emit("game-start-failed",{error:e.t0.message});case 27:case"end":return e.stop()}}),e,this,[[0,22]])})));return function(){return e.apply(this,arguments)}}(),c.privateStartGameWithToken=function(){var e=i(s().mark((function e(t,a){var n,r;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,(n=this.authManager.initializeWithToken(t,a)).success){e.next=7;break}return console.error("[GameManager] Token authentication failed:",n.error),this.eventTarget.emit("authentication-failed",{error:n.error,suggestion:"Please check your authentication token"}),p.instance.emit("authentication-failed",{error:n.error,suggestion:"Please check your authentication token"}),e.abrupt("return");case 7:return r=this.authManager.getAuthData(),e.next=10,this.socketManager.ConnectWithToken(t,r.playerId);case 10:if(!e.sent){e.next=18;break}console.log("[GameManager] Game started successfully"),this.isConnected=!0,this.eventTarget.emit("game-started",{success:!0,message:"Game started successfully"}),p.instance.emit("game-started",{success:!0,message:"Game started successfully"}),e.next=19;break;case 18:throw new Error("Failed to connect to game server");case 19:e.next=26;break;case 21:e.prev=21,e.t0=e.catch(0),console.error("[GameManager] Failed to start game with token:",e.t0),this.eventTarget.emit("game-start-failed",{error:e.t0.message}),p.instance.emit("game-start-failed",{error:e.t0.message});case 26:case"end":return e.stop()}}),e,this,[[0,21]])})));return function(t,a){return e.apply(this,arguments)}}(),c.privateRetryInitialization=function(e){var t=this;if(e>5)return console.error("[GameManager] Failed to initialize managers after maximum attempts"),void this.eventTarget.emit("initialization-failed",{error:"Required managers could not be initialized",suggestion:"Please ensure SocketManager and AuthManager are properly configured"});var a=Math.min(100*Math.pow(2,e-1),1e3);console.log("[GameManager] Retry attempt "+e+"/5 in "+a+"ms"),setTimeout((function(){t.privateInitializeManagers(),t.privateSetupEventListeners()?(console.log("[GameManager] Managers initialized successfully on retry"),t.privateStartGame()):t.privateRetryInitialization(e+1)}),a)},c.onDestroy=function(){this.eventTarget&&this.eventTarget.removeAll(this),this.socketManager&&"function"==typeof this.socketManager.off&&(this.socketManager.off("game-init",this.onGameInit,this),this.socketManager.off("game-status-update",this.onGameStatusUpdate,this),this.socketManager.off("game-result",this.onGameResult,this),this.socketManager.off("balance-update",this.onBalanceUpdate,this),this.socketManager.off("error",this.onSocketError,this),this.socketManager.off("authenticated",this.onAuthenticated,this),this.socketManager.off("data-sync-requested",this.onDataSyncRequested,this)),console.log("[GameManager] Destroyed and cleaned up")},t}(g))._instance=null,S=t((b=A).prototype,"soundManager",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),P=b))||P));c._RF.pop()}}}));

System.register("chunks:///_virtual/HistoryItemView.ts",["./rollupPluginModLoBabelHelpers.js","cc","./SpiteSkin.ts"],(function(e){var t,i,r,n,o,l,s,u,a;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,r=e.initializerDefineProperty,n=e.assertThisInitialized},function(e){o=e.cclegacy,l=e._decorator,s=e.Label,u=e.Component},function(e){a=e.default}],execute:function(){var c,p,b,y,f,w,S,d,m;o._RF.push({},"b8b44khwl5DQJvI7fTAYD0k","HistoryItemView",void 0);var h=l.ccclass,g=l.property;e("HistoryItemView",(c=h("HistoryItemView"),p=g(s),b=g(s),y=g(a),c((S=t((w=function(e){function t(){for(var t,i=arguments.length,o=new Array(i),l=0;l<i;l++)o[l]=arguments[l];return t=e.call.apply(e,[this].concat(o))||this,r(t,"roundLabel",S,n(t)),r(t,"resultLabel",d,n(t)),r(t,"iconSpriteSkin",m,n(t)),t}return i(t,e),t.prototype.Setup=function(e){console.log("[HistoryItemView] Setup called with data:",e),this.roundLabel?(this.roundLabel.string=e.round.toString(),console.log("[HistoryItemView] Set round:",e.round)):console.warn("[HistoryItemView] roundLabel not found"),this.resultLabel?(this.resultLabel.string=e.result,console.log("[HistoryItemView] Set result:",e.result,"→",e.result)):console.warn("[HistoryItemView] resultLabel not found"),this.iconSpriteSkin?(this.iconSpriteSkin.setSkin("D"===e.result?0:1),console.log("[HistoryItemView] Set icon skin:","D"===e.result?0:1)):console.warn("[HistoryItemView] iconSpriteSkin not found")},t}(u)).prototype,"roundLabel",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),d=t(w.prototype,"resultLabel",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),m=t(w.prototype,"iconSpriteSkin",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),f=w))||f));o._RF.pop()}}}));

System.register("chunks:///_virtual/index.ts",["cc","./SocketManager.ts","./AuthManager.ts","./GameManager.ts","./ErrorHandler.ts","./EventManager.ts","./GameEvents.ts"],(function(e){var t;return{setters:[function(e){t=e.cclegacy},function(t){e("SocketManager",t.SocketManager)},function(t){e("AuthManager",t.AuthManager)},function(t){e("GameManager",t.GameManager)},function(t){e("ErrorHandler",t.ErrorHandler)},function(t){var n={};n.EventManager=t.EventManager,n.GAME_EVENTS=t.GAME_EVENTS,n.emitEvent=t.emitEvent,n.getEventManager=t.getEventManager,n.offEvent=t.offEvent,n.onEvent=t.onEvent,e(n)},function(t){var n={};n.GameEventCode=t.GameEventCode,n.GameStatus=t.GameStatus,n.SocketState=t.SocketState,n.TOWER_BET_CODES=t.TOWER_BET_CODES,n.TOWER_PLAYERS=t.TOWER_PLAYERS,n.TowerPlayer=t.TowerPlayer,n.getPlayerFromPosition=t.getPlayerFromPosition,n.getPositionOfPlayer=t.getPositionOfPlayer,n.getWinnerFromPositions=t.getWinnerFromPositions,e(n)}],execute:function(){t._RF.push({},"ce384Y9AsVFNYpQD1KSoSRG","index",void 0),t._RF.pop()}}}));

System.register("chunks:///_virtual/LoadingScene.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EventManager.ts"],(function(e){var t,s,a,n,i,r,o,c,u,d,h,l,g,S,p;return{setters:[function(e){t=e.applyDecoratedDescriptor,s=e.inheritsLoose,a=e.initializerDefineProperty,n=e.assertThisInitialized,i=e.asyncToGenerator,r=e.regeneratorRuntime},function(e){o=e.cclegacy,c=e._decorator,u=e.ProgressBar,d=e.Label,h=e.director,l=e.Component,g=e.resources},function(e){S=e.EventManager,p=e.GAME_EVENTS}],execute:function(){var f,m,L,G,v,y,w;o._RF.push({},"2ae81A3nDZHuJVFuylRS/c6","LoadingScene",void 0);var R=c.ccclass,E=c.property;e("LoadingScene",(f=R("LoadingScene"),m=E(u),L=E(d),f((y=t((v=function(e){function t(){for(var t,s=arguments.length,i=new Array(s),r=0;r<s;r++)i[r]=arguments[r];return t=e.call.apply(e,[this].concat(i))||this,a(t,"progressBar",y,n(t)),a(t,"statusLabel",w,n(t)),t.isLoadingComplete=!1,t.hasError=!1,t.hasSceneLoadStarted=!1,t.hasReceivedGameStatus=!1,t.isPersistentRoot=!1,t.isWaitingForGameStatus=!1,t.isFinalized=!1,t}s(t,e);var o=t.prototype;return o.onLoad=function(){h.addPersistRootNode(this.node),this.isPersistentRoot=!0},o.start=function(){this.initializeProgressBar(),this.startResourceLoading(),console.log("[LoadingScene] Starting resource loading...")},o.CompleteLoading=function(){this.isLoadingComplete||(this.isLoadingComplete=!0),this.waitForGameStatus(),this.onGameStatusReceived()},o.ResetLoading=function(){this.hasError=!1,this.isLoadingComplete=!1,this.hasSceneLoadStarted=!1,this.hasReceivedGameStatus=!1,this.isWaitingForGameStatus=!1,this.isFinalized=!1,this.detachGameStatusListener(),this.progressBar.progress=0,this.updateStatusMessage("Initializing..."),this.startResourceLoading()},o.initializeProgressBar=function(){this.progressBar?(this.progressBar.progress=0,console.log("[LoadingScene] Progress bar initialized to 0%")):console.warn("[LoadingScene] Progress bar not assigned in editor")},o.startResourceLoading=function(){var e=i(r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,this.updateProgress(.1,"Initializing..."),e.next=4,this.delay(200);case 4:return e.next=6,this.loadGameAssets();case 6:return this.updateProgress(.4,"Loading assets..."),e.next=9,this.delay(300);case 9:return e.next=11,this.preloadMainScene();case 11:return this.updateProgress(.7,"Loading scenes..."),e.next=14,this.delay(200);case 14:return e.next=16,this.initializeGameSystems();case 16:return this.updateProgress(.9,"Preparing game..."),e.next=19,this.delay(200);case 19:this.onResourcesReady(),e.next=26;break;case 22:e.prev=22,e.t0=e.catch(0),console.error("[LoadingScene] Loading failed:",e.t0),this.handleError("Loading failed");case 26:case"end":return e.stop()}}),e,this,[[0,22]])})));return function(){return e.apply(this,arguments)}}(),o.updateProgress=function(e,t){this.progressBar.progress=e,this.updateStatusMessage(t),console.log("[LoadingScene] Progress: "+Math.round(100*e)+"% - "+t)},o.delay=function(e){return new Promise((function(t){return setTimeout(t,e)}))},o.loadGameAssets=function(){var e=i(r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,t){g.loadDir("images",(function(s,a){s?(console.error("[LoadingScene] Failed to load assets:",s),t(s)):(console.log("[LoadingScene] Loaded "+a.length+" assets"),e())}))})));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),o.preloadMainScene=function(){var e=i(r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,t){h.preloadScene("Tower",(function(s){s?(console.error("[LoadingScene] Failed to preload Tower scene:",s),t(s)):(console.log("[LoadingScene] Tower scene preloaded successfully"),e())}))})));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),o.initializeGameSystems=function(){var e=i(r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("[LoadingScene] Game systems initialized"),e.abrupt("return",Promise.resolve());case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),o.updateStatusMessage=function(e){this.statusLabel&&(this.statusLabel.string=e,console.log("[LoadingScene] Status: "+e))},o.startMainSceneLoad=function(){var e=this;this.hasSceneLoadStarted||this.hasError||(this.hasSceneLoadStarted=!0,console.log("[LoadingScene] Loading Tower scene while waiting for game status"),h.loadScene("Tower",(function(t){if(t)return console.error("[LoadingScene] Failed to load Tower scene:",t),e.hasSceneLoadStarted=!1,void e.handleError("Scene transition failed");console.log("[LoadingScene] Tower scene loaded. Awaiting first GAME_STATUS_CHANGED event"),e.hasReceivedGameStatus||e.updateStatusMessage("Waiting for game data...")})))},o.onResourcesReady=function(){this.hasError||(this.isLoadingComplete=!0,this.hasReceivedGameStatus||this.updateProgress(1,"Waiting for game data..."),this.waitForGameStatus())},o.waitForGameStatus=function(){this.hasReceivedGameStatus||this.hasError||(this.isWaitingForGameStatus||(S.instance.on(p.GAME_STATUS_CHANGED,this.handleFirstGameStatusChanged,this),this.isWaitingForGameStatus=!0),this.startMainSceneLoad())},o.handleFirstGameStatusChanged=function(e){var t;console.log("[LoadingScene] GAME_STATUS_CHANGED received:",null!=(t=null==e?void 0:e.status)?t:"unknown"),this.onGameStatusReceived()},o.onGameStatusReceived=function(){var e=this;this.hasReceivedGameStatus||this.hasError||(this.hasReceivedGameStatus=!0,this.detachGameStatusListener(),this.updateProgress(1,"Game ready!"),this.scheduleOnce((function(){e.finalizeLoadingOverlay()}),0))},o.finalizeLoadingOverlay=function(){this.isFinalized||(this.isFinalized=!0,this.isPersistentRoot&&(h.removePersistRootNode(this.node),this.isPersistentRoot=!1),this.node&&this.node.isValid&&this.node.destroy())},o.detachGameStatusListener=function(){this.isWaitingForGameStatus&&(S.instance.off(p.GAME_STATUS_CHANGED,this.handleFirstGameStatusChanged,this),this.isWaitingForGameStatus=!1)},o.handleError=function(e){this.hasError||(this.hasError=!0,console.error("[LoadingScene] Error: "+e),this.updateStatusMessage("Error: "+e),this.progressBar.progress=0,this.updateStatusMessage("Loading failed. Tap to retry..."))},t}(l)).prototype,"progressBar",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=t(v.prototype,"statusLabel",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=v))||G));o._RF.pop()}}}));

System.register("chunks:///_virtual/LocalizationManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Singleton.ts","./EventManager.ts"],(function(a){var e,t,n,r,o,i,s,u;return{setters:[function(a){e=a.inheritsLoose,t=a.createClass},function(a){n=a.cclegacy,r=a._decorator,o=a.Component},function(a){i=a.ccsingleton},function(a){s=a.EventManager,u=a.GAME_EVENTS}],execute:function(){var l;n._RF.push({},"0c04eMOGAtI56Gdrgs7KNPn","LocalizationManager",void 0);var c=r.ccclass;a("LocalizationManager",c("LocalizationManager")(l=i(l=function(a){function n(){for(var e,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return(e=a.call.apply(a,[this].concat(n))||this)._currentLanguage="ko",e._translations={},e}e(n,a);var r=n.prototype;return r.onLoad=function(){this.initializeTranslations(),this.detectLanguageFromURL(),console.log("[LocalizationManager] Initialized with language: "+this._currentLanguage)},r.initializeTranslations=function(){this._translations={ko:{round_info_template:"{time}초 후 {round}회차 시작",round_info_static:"게임 시작",start_label:"시작",seconds_label:"초",date_format:"korean"},en:{round_info_template:"Game #{round} starts in {time}s",round_info_static:"Game starts",start_label:"Start",seconds_label:"s",date_format:"english"},"zh-cn":{round_info_template:"{time}秒后 第{round}期开始",round_info_static:"游戏开始",start_label:"开始",seconds_label:"秒",date_format:"chinese_simplified"},"zh-tw":{round_info_template:"{time}秒後 第{round}期開始",round_info_static:"遊戲開始",start_label:"開始",seconds_label:"秒",date_format:"chinese_traditional"},ja:{round_info_template:"{time}秒後 第{round}回開始",round_info_static:"ゲーム開始",start_label:"開始",seconds_label:"秒",date_format:"japanese"},th:{round_info_template:"อีก {time} วินาที เกมรอบที่ {round} เริ่ม",round_info_static:"เกมเริ่ม",start_label:"เริ่ม",seconds_label:"วินาที",date_format:"thai"},km:{round_info_template:"{time} វិនាទីទៀត ល្បែង {round} ចាប់ផ្តើម",round_info_static:"ល្បែងចាប់ផ្តើម",start_label:"ចាប់ផ្តើម",seconds_label:"វិនាទី",date_format:"khmer"},ms:{round_info_template:"Permainan #{round} bermula dalam {time}s",round_info_static:"Permainan bermula",start_label:"Mula",seconds_label:"s",date_format:"malay"},id:{round_info_template:"Game #{round} mulai dalam {time}d",round_info_static:"Game dimulai",start_label:"Mulai",seconds_label:"d",date_format:"indonesian"},vi:{round_info_template:"Ván #{round} bắt đầu",round_info_static:"Trò chơi bắt đầu",start_label:"Bắt đầu",seconds_label:"giây",date_format:"vietnamese"}}},r.detectLanguageFromURL=function(){var a=this;try{var e=new URLSearchParams(window.location.search).get("lang");if(console.log("[LocalizationManager] URL search params: "+window.location.search),console.log("[LocalizationManager] Lang parameter: "+e),e&&this.isValidLanguage(e)){var t=this._currentLanguage;this._currentLanguage=e,console.log("[LocalizationManager] Language detected from URL: "+this._currentLanguage),t!==this._currentLanguage&&setTimeout((function(){s.instance.emit(u.LANGUAGE_CHANGED,{oldLanguage:t,newLanguage:a._currentLanguage})}),100)}else console.log("[LocalizationManager] No valid language parameter found, using default: "+this._currentLanguage)}catch(a){console.error("[LocalizationManager] Failed to detect language from URL:",a)}},r.refreshLanguageFromURL=function(){this.detectLanguageFromURL()},r.isValidLanguage=function(a){return-1!==["ko","en","zh-cn","zh-tw","ja","th","km","ms","id","vi"].indexOf(a)},r.getText=function(a,e){var t,n=null==(t=this._translations[this._currentLanguage])?void 0:t[a];if(!n){var r;console.warn("[LocalizationManager] Translation not found for key: "+a+" in language: "+this._currentLanguage);var o=null==(r=this._translations.en)?void 0:r[a];return o?this.replaceParams(o,e):a}return this.replaceParams(n,e)},r.replaceParams=function(a,e){if(!e)return a;var t=a;for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];t=t.replace(new RegExp("\\{"+n+"\\}","g"),r.toString())}return t},r.padZero=function(a,e){void 0===e&&(e=2);for(var t=a.toString();t.length<e;)t="0"+t;return t},r.formatDate=function(a){var e=a.getUTCFullYear(),t=a.getUTCMonth()+1,n=a.getUTCDate();switch(this._currentLanguage){case"ko":return e+"년 "+t+"월 "+n+"일";case"en":return["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t-1]+" "+n+", "+e;case"zh-cn":case"zh-tw":case"ja":return e+"年"+this.padZero(t)+"月"+this.padZero(n)+"日";case"th":return n+" "+["ม.ค.","ก.พ.","มี.ค.","เม.ย.","พ.ค.","มิ.ย.","ก.ค.","ส.ค.","ก.ย.","ต.ค.","พ.ย.","ธ.ค."][t-1]+" "+e;case"km":return n+" "+["មករា","កុម្ភៈ","មីនា","មេសា","ឧសភា","មិថុនា","កក្កដា","សីហា","កញ្ញា","តុលា","វិច្ឆិកា","ធ្នូ"][t-1]+" "+e;case"ms":return n+" "+["Jan","Feb","Mac","Apr","Mei","Jun","Jul","Ogos","Sep","Okt","Nov","Dis"][t-1]+" "+e;case"id":return n+" "+["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][t-1]+" "+e;case"vi":return"Ngày "+this.padZero(n)+"/"+this.padZero(t)+"/"+e;default:return e+"-"+this.padZero(t)+"-"+this.padZero(n)}},r.getCurrentLanguage=function(){return this._currentLanguage},r.setLanguage=function(a){if(this.isValidLanguage(a)){var e=this._currentLanguage;this._currentLanguage=a,console.log("[LocalizationManager] Language changed from "+e+" to: "+this._currentLanguage),s.instance.emit(u.LANGUAGE_CHANGED,{oldLanguage:e,newLanguage:this._currentLanguage})}else console.warn("[LocalizationManager] Invalid language: "+a)},r.getRoundInfoText=function(a,e){return this.getText("round_info_template",{time:a.toString(),round:e.toString()})},r.getRoundInfoStatic=function(){return this.getText("round_info_static")},r.getStartLabel=function(){return this.getText("start_label")},r.getSecondsLabel=function(){return this.getText("seconds_label")},t(n,null,[{key:"instance",get:function(){return n.instance}}]),n}(o))||l)||l);n._RF.pop()}}}));

System.register("chunks:///_virtual/main",["./EventManager.ts","./CardSkin.ts","./Singleton.ts","./SpiteSkin.ts","./OptimizedSpineComponent.ts","./LoadingScene.ts","./RoundInfoPanel.ts","./SoundToggleButton.ts","./TowerAnimation.ts","./TowerAnimationController.ts","./WorkersAnimation.ts","./DynamicPhaseTest.ts","./DynamicRandomPhaseConfig.ts","./TimingOptimizationTest.ts","./WorkerAnimationDebugger.ts","./SpineIntegrationExample.ts","./index.ts","./AuthManager.ts","./ErrorHandler.ts","./GameManager.ts","./LocalizationManager.ts","./PopupManager.ts","./SocketManager.ts","./SoundManager.ts","./SpineStartupManager.ts","./WebCommunicationManager.ts","./GameEvents.ts","./WorkersAnimationTest.ts","./SpineInitializer.ts","./SpineMemoryManager.ts","./FakeLoadingView.ts","./HistoryItemView.ts","./RankPopupView.ts","./StartPopupView.ts"],(function(){return{setters:[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){}}}));

System.register("chunks:///_virtual/OptimizedSpineComponent.ts",["./rollupPluginModLoBabelHelpers.js","cc","./SpineMemoryManager.ts","./SpineInitializer.ts"],(function(n){var t,e,i,o,r,a,s,p,l,m,u,h,c,d,f;return{setters:[function(n){t=n.applyDecoratedDescriptor,e=n.inheritsLoose,i=n.initializerDefineProperty,o=n.assertThisInitialized,r=n.asyncToGenerator,a=n.regeneratorRuntime},function(n){s=n.cclegacy,p=n._decorator,l=n.sp,m=n.error,u=n.warn,h=n.Component,c=n.log},function(n){d=n.SpineMemoryManager},function(n){f=n.SpineInitializer}],execute:function(){var y,C,_,g,z,S,M,A,v,b,E,w;s._RF.push({},"8fa983TL8tG/6RMzThUs/gF","OptimizedSpineComponent",void 0);var I=p.ccclass,O=p.property,x=p.requireComponent;n("OptimizedSpineComponent",(y=I("OptimizedSpineComponent"),C=x(l.Skeleton),_=O({displayName:"Auto Register",tooltip:"Automatically register with SpineMemoryManager"}),g=O({displayName:"Preload Animation",tooltip:"Preload animation data to avoid runtime loading"}),z=O({displayName:"Cleanup On Disable",tooltip:"Cleanup resources when component is disabled"}),S=O({displayName:"Max Retry Attempts",tooltip:"Maximum retry attempts for failed operations"}),y(M=C((v=t((A=function(n){function t(){for(var t,e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return t=n.call.apply(n,[this].concat(r))||this,i(t,"autoRegister",v,o(t)),i(t,"preloadAnimation",b,o(t)),i(t,"cleanupOnDisable",E,o(t)),i(t,"maxRetryAttempts",w,o(t)),t._spineComponent=null,t._memoryManager=null,t._isInitialized=!1,t._retryCount=0,t._lastError=null,t}e(t,n);var s=t.prototype;return s.onLoad=function(){this.initializeComponent()},s.onEnable=function(){this._isInitialized&&this.registerWithMemoryManager()},s.onDisable=function(){this.cleanupOnDisable&&this.unregisterFromMemoryManager()},s.onDestroy=function(){this.cleanup()},s.initializeComponent=function(){var n=r(a().mark((function n(){return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,f.waitForSpine();case 3:if(n.sent){n.next=6;break}throw new Error("Spine WASM not loaded");case 6:if(this._spineComponent=this.getComponent(l.Skeleton),this._spineComponent){n.next=9;break}throw new Error("sp.Skeleton component not found");case 9:if(this._memoryManager=d.getInstance(),this.setupErrorHandling(),!this.preloadAnimation){n.next=14;break}return n.next=14,this.preloadAnimationData();case 14:this._isInitialized=!0,this._retryCount=0,this._lastError=null,this.autoRegister&&this.registerWithMemoryManager(),c("[OptimizedSpineComponent] Initialized successfully: "+this.node.name),n.next=24;break;case 21:n.prev=21,n.t0=n.catch(0),this.handleInitializationError(n.t0);case 24:case"end":return n.stop()}}),n,this,[[0,21]])})));return function(){return n.apply(this,arguments)}}(),s.setupErrorHandling=function(){var n=this;if(this._spineComponent){var t=this._spineComponent.setAnimation;this._spineComponent.setAnimation=function(e,i,o){try{return t.call(n._spineComponent,e,i,o)}catch(t){return n.handleSpineError("setAnimation",t),null}};var e=this._spineComponent.addAnimation;this._spineComponent.addAnimation=function(t,i,o,r){try{return e.call(n._spineComponent,t,i,o,r)}catch(t){return n.handleSpineError("addAnimation",t),null}}}},s.preloadAnimationData=function(){var n=r(a().mark((function n(){var t=this;return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",new Promise((function(n){try{if(!t._spineComponent||!t._spineComponent.skeletonData)return void n();var e=t._spineComponent.skeletonData;e&&e._nativeAsset&&c("[OptimizedSpineComponent] Preloaded animation data for: "+t.node.name),n()}catch(t){u("[OptimizedSpineComponent] Failed to preload animation data: "+t.message),n()}})));case 1:case"end":return n.stop()}}),n)})));return function(){return n.apply(this,arguments)}}(),s.registerWithMemoryManager=function(){this._memoryManager&&this._spineComponent&&this._memoryManager.registerSpineComponent(this._spineComponent)},s.unregisterFromMemoryManager=function(){this._memoryManager&&this._spineComponent&&this._memoryManager.unregisterSpineComponent(this._spineComponent)},s.handleInitializationError=function(n){var t=this;this._lastError=n.message||"Unknown initialization error",m("[OptimizedSpineComponent] Initialization failed for "+this.node.name+":",n),this._retryCount<this.maxRetryAttempts?(this._retryCount++,u("[OptimizedSpineComponent] Retrying initialization ("+this._retryCount+"/"+this.maxRetryAttempts+")"),this.scheduleOnce((function(){t.initializeComponent()}),1)):m("[OptimizedSpineComponent] Max retry attempts reached for "+this.node.name)},s.handleSpineError=function(n,t){var e="Spine "+n+" failed: "+(t.message||t);this._lastError=e,u("[OptimizedSpineComponent] "+e+" for "+this.node.name),this.node.emit("spine-error",{operation:n,error:t,component:this})},s.safeSetAnimation=function(n,t,e){if(void 0===e&&(e=!1),!this._isInitialized||!this._spineComponent)return u("[OptimizedSpineComponent] Component not initialized: "+this.node.name),null;try{return this._spineComponent.setAnimation(n,t,e)}catch(n){return this.handleSpineError("safeSetAnimation",n),null}},s.safeAddAnimation=function(n,t,e,i){if(void 0===e&&(e=!1),void 0===i&&(i=0),!this._isInitialized||!this._spineComponent)return u("[OptimizedSpineComponent] Component not initialized: "+this.node.name),null;try{return this._spineComponent.addAnimation(n,t,e,i)}catch(n){return this.handleSpineError("safeAddAnimation",n),null}},s.getSpineComponent=function(){return this._isInitialized?this._spineComponent:null},s.isReady=function(){return this._isInitialized&&this._spineComponent&&this._spineComponent.isValid},s.getLastError=function(){return this._lastError},s.reinitialize=function(){var n=r(a().mark((function n(){return a().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return this.cleanup(),this._isInitialized=!1,this._retryCount=0,this._lastError=null,n.next=6,this.initializeComponent();case 6:case"end":return n.stop()}}),n,this)})));return function(){return n.apply(this,arguments)}}(),s.cleanup=function(){if(this.unregisterFromMemoryManager(),this.unscheduleAllCallbacks(),this._spineComponent)try{this._spineComponent.clearTracks()}catch(n){}this._spineComponent=null,this._memoryManager=null,this._isInitialized=!1},s.getStatus=function(){return{isInitialized:this._isInitialized,isReady:this.isReady(),retryCount:this._retryCount,lastError:this._lastError,hasSpineComponent:!!this._spineComponent,hasMemoryManager:!!this._memoryManager,nodeName:this.node.name}},s.debugStatus=function(){console.log("[OptimizedSpineComponent] === DEBUG STATUS for "+this.node.name+" ==="),console.log("Status:",this.getStatus()),console.log("=== END DEBUG STATUS ===")},t}(h)).prototype,"autoRegister",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),b=t(A.prototype,"preloadAnimation",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),E=t(A.prototype,"cleanupOnDisable",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),w=t(A.prototype,"maxRetryAttempts",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 3}}),M=A))||M)||M));s._RF.pop()}}}));

System.register("chunks:///_virtual/PopupManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EventManager.ts","./GameEvents.ts"],(function(t){var e,n,r,o,a,s,i,u,p,l,c,P,f,h;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.inheritsLoose,r=t.initializerDefineProperty,o=t.assertThisInitialized,a=t.createForOfIteratorHelperLoose},function(t){s=t.cclegacy,i=t._decorator,u=t.Prefab,p=t.Node,l=t.instantiate,c=t.Component},function(t){P=t.EventManager,f=t.GAME_EVENTS},function(t){h=t.TowerPlayer}],execute:function(){var d,R,m,S,v,y,g,b,w,M;s._RF.push({},"adbfeyEakhFJ7jyzhvynibp","PopupManager",void 0);var T=i.ccclass,E=i.property;t("PopupManager",(d=T("PopupManager"),R=E(u),m=E(u),S=E(p),d(((M=function(t){function e(){for(var e,n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return e=t.call.apply(t,[this].concat(a))||this,r(e,"startPopupPrefab",g,o(e)),r(e,"resultPopupPrefab",b,o(e)),r(e,"popupContainer",w,o(e)),e.currentStartPopup=null,e.currentResultPopup=null,e.latestGameResult=null,e}n(e,t),e.getInstance=function(){return this.instance};var s=e.prototype;return s.onLoad=function(){e.instance=this,this.registerListeners()},s.onDestroy=function(){this.detachListeners(),this.disposeNode(this.currentStartPopup),this.disposeNode(this.currentResultPopup),this.currentStartPopup=null,this.currentResultPopup=null,e.instance=null},s.registerListeners=function(){P.instance.on(f.TOWER_RESULT,this.onGameResult,this),P.instance.on(f.SHOW_RESULT_POPUP,this.onShowResultPopup,this),P.instance.on(f.GAME_RESET_UI,this.onGameResetUI,this),P.instance.on(f.STARTUP_POPUP_SHOWN,this.onStartupPopupShown,this)},s.detachListeners=function(){P.instance.off(f.TOWER_RESULT,this.onGameResult,this),P.instance.off(f.SHOW_RESULT_POPUP,this.onShowResultPopup,this),P.instance.off(f.GAME_RESET_UI,this.onGameResetUI,this),P.instance.off(f.STARTUP_POPUP_SHOWN,this.onStartupPopupShown,this)},s.disposeNode=function(t){t&&t.isValid&&t.destroy()},s.onGameResult=function(t){this.latestGameResult=t},s.onGameResetUI=function(){this.currentStartPopup=null,this.currentResultPopup=null,this.latestGameResult=null},s.onStartupPopupShown=function(){var t=this;this.startPopupPrefab?(this.disposeNode(this.currentStartPopup),setTimeout((function(){if(t.startPopupPrefab&&t.isValid){var e=l(t.startPopupPrefab);(t.popupContainer||t.node).addChild(e),t.currentStartPopup=e}}),200)):console.warn("[PopupManager] Start popup prefab not assigned")},s.onShowResultPopup=function(){var t=this.transformServerResultToRaceData(this.latestGameResult);t&&this.presentResultPopup(t)},s.presentResultPopup=function(t){if(this.resultPopupPrefab){var e=this.transformRaceDataForRankPopup(t);if(e){this.disposeNode(this.currentResultPopup);var n=l(this.resultPopupPrefab);(this.popupContainer||this.node).addChild(n),this.currentResultPopup=n;var r=n.getComponent("RankPopupView");r&&"function"==typeof r.SetupPopup?r.SetupPopup(e):console.warn("[PopupManager] RankPopupView component not found or not ready")}}else console.warn("[PopupManager] Result popup prefab not assigned")},s.transformRaceDataForRankPopup=function(t){if(!t||!Array.isArray(t.results)||3!==t.results.length)return console.warn("[PopupManager] Invalid race data for result popup:",t),null;for(var e,n={},r=a(t.results);!(e=r()).done;){var o=e.value;o.player&&"number"==typeof o.position&&(n[o.player]=o.position)}var s=[h.A,h.P,h.T].find((function(t){return void 0===n[t]}));if(s)return console.warn("[PopupManager] Missing position for player:",s,n),null;var i=this.latestGameResult||{};return{positions:n,winner:i.winner,firstPlace:i.firstPlace,secondPlace:i.secondPlace,thirdPlace:i.thirdPlace,raceData:t}},s.transformServerResultToRaceData=function(t){if(!t||!Array.isArray(t.positions)||3!==t.positions.length)return console.warn("[PopupManager] Invalid server result for transformation:",t),null;var e=[8.5,9.5,10.5],n=[h.A,h.P,h.T].map((function(n,r){var o=t.positions[r],a=Math.min(Math.max(o,1),3)-1;return{player:n,position:o,time:e[a]}})),r=Math.max.apply(Math,n.map((function(t){var e;return null!=(e=t.time)?e:0})));return{results:n,totalTime:r}},e}(c)).instance=null,g=e((y=M).prototype,"startPopupPrefab",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=e(y.prototype,"resultPopupPrefab",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=e(y.prototype,"popupContainer",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=y))||v));s._RF.pop()}}}));

System.register("chunks:///_virtual/RankPopupView.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EventManager.ts","./GameEvents.ts","./WebCommunicationManager.ts"],(function(e){var t,i,n,r,a,o,s,l,c,u,p,d,h,f,k,S;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,n=e.initializerDefineProperty,r=e.assertThisInitialized},function(e){a=e.cclegacy,o=e._decorator,s=e.Node,l=e.SpriteFrame,c=e.Sprite,u=e.tween,p=e.Vec3,d=e.Component},function(e){h=e.EventManager,f=e.GAME_EVENTS},function(e){k=e.TowerPlayer},function(e){S=e.WebCommunicationManager}],execute:function(){var g,w,R,T,v,m,P,F,N,b,O,E,I,y,_,A,W,D,V,B,C,L,z,M,U,H,K;a._RF.push({},"ebe84eajHJK9ICTl/+uVu0t","RankPopupView",void 0);var Y=o.ccclass,x=o.property;e("RankPopupView",(g=Y("RankPopupView"),w=x(s),R=x(s),T=x(s),v=x(s),m=x(s),P=x([s]),F=x([s]),N=x([l]),b=x([l]),O=x(c),E=x(c),I=x(c),g((A=t((_=function(e){function t(){for(var t,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return t=e.call.apply(e,[this].concat(a))||this,n(t,"bg1Node",A,r(t)),n(t,"bg2Node",W,r(t)),n(t,"aWorkerNode",D,r(t)),n(t,"pWorkerNode",V,r(t)),n(t,"tWorkerNode",B,r(t)),n(t,"rankNodes",C,r(t)),n(t,"bgNodes",L,r(t)),n(t,"faceSprites",z,r(t)),n(t,"defaultFaceSprites",M,r(t)),n(t,"aFaceSprite",U,r(t)),n(t,"pFaceSprite",H,r(t)),n(t,"tFaceSprite",K,r(t)),t.SLIDE_START_OFFSET_X=550,t.SLIDE_DURATION=1.5,t.RANK_POSITIONS=[-150,0,150],t.SLIDE_END_OFFSET_X=-550,t.BOB_AMPLITUDE=8,t.DESIRED_BOB_CYCLES=10,t.RANK_POSITIONSY=[100,55,0],t.slideInTweens=new Map,t.slideOutTweens=new Map,t.rankRevealTweens=new Map,t.blinkingTween=null,t.slideOutTimer=null,t.currentRankOrder=[],t.completedWorkers=0,t.hasEmittedResultPopupHidden=!1,t.thirdWorkerReachedEnd=!1,t}i(t,e);var a=t.prototype;return a.onLoad=function(){this.validateSetup()},a.onDestroy=function(){this.stopAllAnimations()},a.convertPositionsToRankOrder=function(e){var t=[[k.A,e[k.A]],[k.P,e[k.P]],[k.T,e[k.T]]];return t.sort((function(e,t){return e[1]-t[1]})),t.map((function(e){return e[0]}))},a.show=function(e){var t=this;if(3===e.length){var i=S.instance;i?i.sendCode3ResultScreen():console.warn("[RoundInfoPanel] WebCommunicationManager not available during result hide"),this.stopAllAnimations(),this.showDefaultFaces(),this.resetRankNodes(),this.currentRankOrder=[].concat(e),this.completedWorkers=0,this.hasEmittedResultPopupHidden=!1,this.thirdWorkerReachedEnd=!1,this.node.active=!0,e.forEach((function(e,i){var n=t.getWorkerNode(e);n&&t.animateWorkerToRank(n,i)}))}else console.error("[RankPopupView] Invalid rank order length:",e.length)},a.hide=function(){this.stopAllAnimations(),this.node.active=!1,this.hasEmittedResultPopupHidden=!1,this.thirdWorkerReachedEnd=!1},a.getWorkerNode=function(e){switch(e){case k.A:return this.aWorkerNode;case k.P:return this.pWorkerNode;case k.T:return this.tWorkerNode;default:return console.error("[RankPopupView] Unknown player:",e),null}},a.animateWorkerToRank=function(e,t){if(e){var i=this.RANK_POSITIONS[t],n=e.position.y,r=e.position.z,a=i+this.SLIDE_START_OFFSET_X;e.active=!0,e.setPosition(a,n,r),this.createWalkingAnimation(e,a,i,n,r,t)}},a.createWalkingAnimation=function(e,t,i,n,r,a){for(var o=this,s=2*this.DESIRED_BOB_CYCLES,l=this.SLIDE_DURATION/s,c=!0,d=u(e),h=0;h<s;h++){var f=t+(i-t)*((h+1)/s),k=c?n+this.BOB_AMPLITUDE:n;d=d.to(l,{position:new p(f,k,r)},{easing:"sineInOut"}),c=!c}d=d.to(.5*l,{position:new p(i,n,r)},{easing:"sineOut"}).call((function(){o.animateToRankY(e,i,n,r,a)})).start(),this.slideInTweens.set(e,d)},a.animateToRankY=function(e,t,i,n,r){var a=this,o=this.RANK_POSITIONSY[r],s=u(e).to(1.5,{position:new p(t,o,n)},{easing:"expoOut"}).call((function(){a.slideInTweens.delete(e),a.completedWorkers++,a.showFaceForRank(r),a.completedWorkers>=3&&a.startBackgroundBlinking()})).start();this.slideInTweens.set(e,s)},a.startBackgroundBlinking=function(){var e=this;if(!this.bgNodes||this.bgNodes.length<2)console.warn("[RankPopupView] bgNodes array needs at least 2 nodes for blinking effect");else{var t=!0,i=u({}).repeatForever(u().call((function(){t?(e.bgNodes[0].active=!0,e.bgNodes[1].active=!1):(e.bgNodes[0].active=!1,e.bgNodes[1].active=!0),t=!t})).delay(.2)).start();h.instance.emit(f.GAME_RESET_UI),this.blinkingTween=i,this.slideOutTimer=u({}).delay(1.5).call((function(){e.rankNodes.forEach((function(t){var i=e.rankRevealTweens.get(t);i&&(i.stop(),e.rankRevealTweens.delete(t)),t.setScale(1,1,1),t.active=!1})),e.startSlideOutSequence()})).start()}},a.startSlideOutSequence=function(){var e=this;this.completedWorkers=0,this.currentRankOrder.forEach((function(t,i){var n=e.getWorkerNode(t);n&&e.createSlideOutAnimation(n,i)}))},a.createSlideOutAnimation=function(e,t){var i=this;if(e){for(var n=this.RANK_POSITIONS[t],r=n+this.SLIDE_END_OFFSET_X,a=this.RANK_POSITIONSY[t],o=e.position.z,s=2*this.DESIRED_BOB_CYCLES,l=this.SLIDE_DURATION/s,c=!0,d=u(e),k=0;k<s;k++){var S=n+(r-n)*((k+1)/s),g=c?a+this.BOB_AMPLITUDE:a;d=d.to(l,{position:new p(S,g,o)},{easing:"sineInOut"}),c=!c}d=d.to(.5*l,{position:new p(r,a,o)},{easing:"sineOut"}).call((function(){i.slideOutTweens.delete(e);var n=t===i.currentRankOrder.length-1,a=Math.abs(e.position.x-r)<=.001;n&&a&&(i.thirdWorkerReachedEnd=!0),i.completedWorkers++,i.completedWorkers>=i.currentRankOrder.length&&i.thirdWorkerReachedEnd&&!i.hasEmittedResultPopupHidden&&(i.hasEmittedResultPopupHidden=!0,setTimeout((function(){h.instance.emit(f.RESULT_POPUP_HIDDEN),i.stopBackgroundBlinking(),i.cleanupPopup()}),100))})).start(),this.slideOutTweens.set(e,d)}},a.showFaceForRank=function(e){var t=this.currentRankOrder[e];this.showPlayerFaceAtRank(t,e),this.showRankNodeAtPosition(e)},a.validateSetup=function(){var e=!1;if(this.aFaceSprite||(console.error("[RankPopupView] ❌ aFaceSprite not assigned in editor!"),e=!0),this.pFaceSprite||(console.error("[RankPopupView] ❌ pFaceSprite not assigned in editor!"),e=!0),this.tFaceSprite||(console.error("[RankPopupView] ❌ tFaceSprite not assigned in editor!"),e=!0),this.faceSprites&&0!==this.faceSprites.length)if(this.faceSprites.length<3)console.error("[RankPopupView] ❌ faceSprites array has only "+this.faceSprites.length+" elements, needs 3!"),e=!0;else for(var t=0;t<3;t++)this.faceSprites[t]||(console.error("[RankPopupView] ❌ faceSprites["+t+"] is null! Please assign SpriteFrame for "+(0===t?"1st":1===t?"2nd":"3rd")+" place."),e=!0);else console.error("[RankPopupView] ❌ faceSprites array is empty! Please assign 3 SpriteFrame assets in editor."),e=!0;if(this.defaultFaceSprites&&0!==this.defaultFaceSprites.length)if(this.defaultFaceSprites.length<3)console.error("[RankPopupView] ❌ defaultFaceSprites array has only "+this.defaultFaceSprites.length+" elements, needs 3!"),e=!0;else for(var i=0;i<3;i++)this.defaultFaceSprites[i]||(console.error("[RankPopupView] ❌ defaultFaceSprites["+i+"] is null! Please assign default SpriteFrame for "+(0===i?"A":1===i?"P":"T")+" player."),e=!0);else console.error("[RankPopupView] ❌ defaultFaceSprites array is empty! Please assign 3 default SpriteFrame assets in editor."),e=!0;this.aWorkerNode||(console.error("[RankPopupView] ❌ aWorkerNode not assigned in editor!"),e=!0),this.pWorkerNode||(console.error("[RankPopupView] ❌ pWorkerNode not assigned in editor!"),e=!0),this.tWorkerNode||(console.error("[RankPopupView] ❌ tWorkerNode not assigned in editor!"),e=!0),e&&console.error("[RankPopupView] 🚨 SETUP INCOMPLETE! Please assign missing components in Cocos Creator editor.")},a.showPlayerFaceAtRank=function(e,t){var i=this.getPlayerFaceSprite(e);if(i)if(!this.faceSprites||this.faceSprites.length<=t){var n;console.error("[RankPopupView] ❌ Cannot show face: faceSprites array insufficient (length: "+((null==(n=this.faceSprites)?void 0:n.length)||0)+", need index: "+t+")")}else{var r=this.faceSprites[t];r?(i.spriteFrame=r,i.node.active=!0):console.error("[RankPopupView] ❌ Cannot show face: faceSprites["+t+"] is null")}else console.error("[RankPopupView] ❌ Cannot show face: "+e+" face sprite component not found")},a.showRankNodeAtPosition=function(e){var t=this;if(this.rankNodes&&this.rankNodes.length>e){var i=this.rankNodes[e],n=this.RANK_POSITIONS[e],r=this.RANK_POSITIONSY[e]+80;i.setPosition(n,r,0),i.active=!0;var a=this.rankRevealTweens.get(i);a&&a.stop();i.setScale(.2,.2,1);var o=u(i).delay(.12*e).to(.28,{scale:new p(1.1,1.1,1)},{easing:"backOut"}).to(.12,{scale:new p(1,1,1)},{easing:"sineInOut"}).call((function(){t.rankRevealTweens.delete(i)})).start();this.rankRevealTweens.set(i,o)}},a.resetRankNodes=function(){var e=this;this.rankNodes&&this.rankNodes.forEach((function(t){var i=e.rankRevealTweens.get(t);i&&i.stop(),t.active=!1,t.setScale(1,1,1),e.rankRevealTweens.delete(t)}))},a.getPlayerFaceSprite=function(e){switch(e){case k.A:return this.aFaceSprite;case k.P:return this.pFaceSprite;case k.T:return this.tFaceSprite;default:return null}},a.stopBackgroundBlinking=function(){this.blinkingTween&&(this.blinkingTween.stop(),this.blinkingTween=null),this.resetRankNodes()},a.cleanupPopup=function(){this.node.active=!1,this.currentRankOrder=[],this.completedWorkers=0},a.stopAllAnimations=function(){this.slideInTweens.forEach((function(e){e&&e.stop()})),this.slideInTweens.clear(),this.slideOutTweens.forEach((function(e){e&&e.stop()})),this.slideOutTweens.clear(),this.rankRevealTweens.forEach((function(e,t){e&&e.stop(),t&&t.setScale(1,1,1)})),this.rankRevealTweens.clear(),this.slideOutTimer&&(this.slideOutTimer.stop(),this.slideOutTimer=null),this.blinkingTween&&(this.blinkingTween.stop(),this.blinkingTween=null),this.resetRankNodes(),this.hasEmittedResultPopupHidden=!1,this.thirdWorkerReachedEnd=!1},a.showDefaultFaces=function(){this.aFaceSprite&&this.defaultFaceSprites[0]&&(this.aFaceSprite.spriteFrame=this.defaultFaceSprites[1],this.aFaceSprite.node.active=!0),this.pFaceSprite&&this.defaultFaceSprites[1]&&(this.pFaceSprite.spriteFrame=this.defaultFaceSprites[1],this.pFaceSprite.node.active=!0),this.tFaceSprite&&this.defaultFaceSprites[2]&&(this.tFaceSprite.spriteFrame=this.defaultFaceSprites[1],this.tFaceSprite.node.active=!0)},a.SetupPopup=function(e){var t=this.convertPositionsToRankOrder(e.positions);this.show(t)},t}(d)).prototype,"bg1Node",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),W=t(_.prototype,"bg2Node",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=t(_.prototype,"aWorkerNode",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=t(_.prototype,"pWorkerNode",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=t(_.prototype,"tWorkerNode",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=t(_.prototype,"rankNodes",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),L=t(_.prototype,"bgNodes",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),z=t(_.prototype,"faceSprites",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),M=t(_.prototype,"defaultFaceSprites",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),U=t(_.prototype,"aFaceSprite",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=t(_.prototype,"pFaceSprite",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),K=t(_.prototype,"tFaceSprite",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=_))||y));a._RF.pop()}}}));

System.register("chunks:///_virtual/RoundInfoPanel.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EventManager.ts","./GameEvents.ts","./LocalizationManager.ts","./WebCommunicationManager.ts"],(function(e){var t,i,n,a,s,r,o,l,h,u,c,p,v,d,_,m;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,n=e.initializerDefineProperty,a=e.assertThisInitialized},function(e){s=e.cclegacy,r=e._decorator,o=e.Label,l=e.Node,h=e.Vec3,u=e.tween,c=e.Component},function(e){p=e.EventManager,v=e.GAME_EVENTS},function(e){d=e.GameStatus},function(e){_=e.LocalizationManager},function(e){m=e.WebCommunicationManager}],execute:function(){var T,f,g,R,S,b,D,N,w,U,I,L,E,A,M;s._RF.push({},"50a46L4zitL4r+lA+w+UxH8","RoundInfoPanel",void 0);var P=r.ccclass,y=r.property;e("RoundInfoPanel",(T=P("RoundInfoPanel"),f=y(o),g=y(o),R=y(o),S=y(l),b=y(o),D=y(o),T((U=t((w=function(e){function t(){for(var t,i=arguments.length,s=new Array(i),r=0;r<i;r++)s[r]=arguments[r];return t=e.call.apply(e,[this].concat(s))||this,n(t,"dateLabel",U,a(t)),n(t,"nextRoundLabel",I,a(t)),n(t,"timeToNextRoundLabel",L,a(t)),n(t,"statePanelInfo",E,a(t)),n(t,"seconsLabel",A,a(t)),n(t,"round_info_label",M,a(t)),t._currentRound=0,t._currentStatus=void 0,t._timeRemains=0,t._eventManager=void 0,t._localTimeRemains=0,t._lastServerSync=0,t._serverTime=null,t._serverTimeOffset=0,t._lastServerTimeUpdate=0,t._blinkTween=null,t._lastBlinkTime=-1,t._positionTween=null,t._isAnimating=!1,t.HIDDEN_Y=-500,t.SHOWN_Y=-125,t.ANIMATION_DURATION=.4,t._localizationManager=null,t.firstShow=!0,t.hidePanelEvent=!0,t.hasTriggeredCountdownRaise=!1,t.COUNTDOWN_RAISE_THRESHOLD=3,t}i(t,e);var s=t.prototype;return s.start=function(){this._eventManager=p.instance,this._localizationManager=_.instance,this.privateSetupEventListeners(),this.privateInitializeLocalization(),this.privateInitializeDisplay(),this.privateStartUpdateTimer(),this.seconsLabel.string=this._localizationManager.getSecondsLabel(),this.round_info_label.string=this._localizationManager.getRoundInfoStatic()},s.TestLanguageChange=function(e){this._localizationManager&&this._localizationManager.setLanguage(e)},s.TestRefreshLanguage=function(){this._localizationManager&&this._localizationManager.refreshLanguageFromURL()},s.TestServerTime=function(){this.privateCalculateServerTime();this.privateUpdateDateDisplay()},s.DebugBuildIssue=function(){},s.privateSetupEventListeners=function(){this._eventManager.on(v.GAME_STATUS_CHANGED,this.privateOnGameStatusChanged,this),this._eventManager.on(v.LANGUAGE_CHANGED,this.privateOnLanguageChanged,this),this._eventManager.on(v.RESULT_POPUP_HIDDEN,this.resultHiddenAlready,this),this._eventManager.on(v.GAME_RESET_UI,this.privateOnGameResetUI,this)},s.resultHiddenAlready=function(){this.hidePanelEvent=!0,this.privateAnimateToPosition(0,!0)},s.privateInitializeLocalization=function(){this._localizationManager},s.privateInitializeDisplay=function(){this.statePanelInfo&&(this.statePanelInfo.active=!0);var e=this.node.position.clone();this.node.setPosition(-500,e.y,e.z)},s.privateStartUpdateTimer=function(){this.schedule(this.privateUpdateDateDisplayIfServerTime,1),this.schedule(this.privateUpdateLocalCountdown,1)},s.privateUpdateDateDisplayIfServerTime=function(){this._serverTime&&this._lastServerTimeUpdate>0&&this.privateUpdateDateDisplay()},s.privateUpdateLocalCountdown=function(){(this._currentStatus===d.BETTING_OPEN||this._currentStatus===d.BETTING_CLOSED)&&this._localTimeRemains>0&&(this._localTimeRemains--,this._localTimeRemains<=0&&this.hidePanel(),this.privateUpdateTimeToNextRound(this._localTimeRemains))},s.hidePanel=function(){var e=this;this.hidePanelEvent&&(this.hidePanelEvent=!1,this.hasTriggeredCountdownRaise=!1,setTimeout((function(){e.privateAnimateToPosition(-500,!1),p.instance.emit(v.STARTUP_POPUP_SHOWN)}),500))},s.privateOnLanguageChanged=function(e){this.privateInitializeLocalization(),this._serverTime&&this._lastServerTimeUpdate>0&&this.privateUpdateDateDisplay()},s.privateOnGameStatusChanged=function(e){if(e&&"object"==typeof e){if(void 0!==e.status&&(this._currentStatus=e.status),void 0!==e.round&&(this._currentRound=e.round),void 0!==e.timeRemains){this._timeRemains=e.timeRemains;var t=e.timeRemains;if(this._currentStatus===d.BETTING_OPEN||this._currentStatus===d.BETTING_CLOSED){if(this._currentStatus===d.BETTING_OPEN){var i=Math.abs(this._localTimeRemains-t);(this._localTimeRemains<0||i>2)&&(this._localTimeRemains=t,this._lastServerSync=Date.now())}}else{var n=Math.abs(this._localTimeRemains-t);(0===this._localTimeRemains||n>2)&&(this._localTimeRemains=t,this._lastServerSync=Date.now())}t<=0&&(this._localTimeRemains=0)}void 0!==e.server_time&&(this.privateProcessServerTime(e.server_time),this.privateUpdateDateDisplay()),this.checkFirstShow(),this.privateUpdateNextRoundInfo(),this._localTimeRemains>=0&&this.privateUpdateTimeToNextRound(this._localTimeRemains),void 0!==(null==e?void 0:e.timeRemains)&&e.timeRemains<=0&&this.hidePanel()}},s.checkFirstShow=function(){this.statePanelInfo&&this.firstShow&&(this.firstShow=!1,this.privateAnimateToPosition(0,!0))},s.privateAnimateToPosition=function(e,t){var i=this;this._positionTween&&(this._positionTween.stop(),this._positionTween=null),this._isAnimating=!0;var n=this.node.position.clone(),a=new h(e,n.y,n.z);t||(this.hasTriggeredCountdownRaise=!1);var s=m.instance;s&&0==e?s.sendCode4PanelState():console.warn("[RoundInfoPanel] WebCommunicationManager not available when showing panel"),this._positionTween=u(this.node).to(this.ANIMATION_DURATION,{position:a},{easing:"quadInOut"}).call((function(){i._isAnimating=!1,i._positionTween=null,t&&i.privateEmitCountdownRaise()})).start()},s.privateEmitCountdownRaise=function(){if(!this.hasTriggeredCountdownRaise){var e=Math.max(0,this._localTimeRemains),t=e>this.COUNTDOWN_RAISE_THRESHOLD?3:1;this.hasTriggeredCountdownRaise=!0,p.instance.emit(v.ROUND_COUNTDOWN_STARTED,{countdownSeconds:e,tweenDuration:t})}},s.privateOnGameResetUI=function(){this.hasTriggeredCountdownRaise=!1},s.privateProcessServerTime=function(e){try{var t=null;if(t=new Date(e),isNaN(t.getTime())){var i=parseInt(e);isNaN(i)||(t=new Date(i))}if(isNaN(t.getTime())){var n=parseInt(e);!isNaN(n)&&n>1e12&&(t=new Date(n))}if(t&&!isNaN(t.getTime())){var a=Date.now();this._serverTimeOffset=t.getTime()-a,this._serverTime=e,this._lastServerTimeUpdate=a}}catch(e){}},s.privateCalculateServerTime=function(){if(!this._serverTime||0===this._lastServerTimeUpdate)return null;var e=Date.now();if(e-this._lastServerTimeUpdate>1e4)return null;try{return new Date(e+this._serverTimeOffset)}catch(e){return null}},s.privateUpdateDateDisplay=function(){var e=this.privateCalculateServerTime();if(e&&this.dateLabel)try{var t=e.getFullYear(),i=(e.getMonth()+1).toString(),n=e.getDate().toString(),a=t+"-"+("0"+i).slice(-2)+"-"+("0"+n).slice(-2);this.dateLabel.string=a}catch(t){var s=e.toISOString().split("T")[0];this.dateLabel.string=s}else this.dateLabel&&(this.dateLabel.string="")},s.privateUpdateNextRoundInfo=function(){var e=this._currentRound.toString();this.nextRoundLabel&&(this.nextRoundLabel.string=""+e)},s.privateUpdateTimeToNextRound=function(e){var t=Math.max(0,e);this.timeToNextRoundLabel.string=""+t},s.privateStopBlinkAnimation=function(){this._blinkTween&&(this._blinkTween.stop(),this._blinkTween=null)},s.ForceUpdate=function(){this.privateInitializeLocalization(),this._serverTime&&this._lastServerTimeUpdate>0&&this.privateUpdateDateDisplay(),this.privateUpdateNextRoundInfo(),this.privateUpdateTimeToNextRound(this._localTimeRemains)},s.onDestroy=function(){this.unscheduleAllCallbacks(),this.privateStopBlinkAnimation(),this._positionTween&&(this._positionTween.stop(),this._positionTween=null),this._eventManager&&(this._eventManager.off(v.GAME_STATUS_CHANGED,this.privateOnGameStatusChanged,this),this._eventManager.off(v.LANGUAGE_CHANGED,this.privateOnLanguageChanged,this),this._eventManager.off(v.RESULT_POPUP_HIDDEN,this.resultHiddenAlready,this),this._eventManager.off(v.GAME_RESET_UI,this.privateOnGameResetUI,this))},t}(c)).prototype,"dateLabel",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=t(w.prototype,"nextRoundLabel",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=t(w.prototype,"timeToNextRoundLabel",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=t(w.prototype,"statePanelInfo",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=t(w.prototype,"seconsLabel",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=t(w.prototype,"round_info_label",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=w))||N));s._RF.pop()}}}));

System.register("chunks:///_virtual/Singleton.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var t,n,o,i,r;return{setters:[function(e){t=e.inheritsLoose,n=e.assertThisInitialized,o=e.createClass},function(e){i=e.cclegacy,r=e.director}],execute:function(){e({ccsingleton:function(e){var n=null,i=function(i){function s(){return i.apply(this,arguments)||this}t(s,i);var c=s.prototype;return c.onLoad=function(){if(n&&n!==this)return console.warn("[ccsingleton] Multiple instances of "+e.name+" detected. Destroying new instance."),void(this.node&&this.node.isValid&&this.node.destroy());i.prototype.onLoad&&i.prototype.onLoad.call(this),n=this,r.addPersistRootNode(this.node)},c.onDestroy=function(){i.prototype.onDestroy&&i.prototype.onDestroy.call(this),n===this&&(n=null),r.removePersistRootNode(this.node)},o(s,null,[{key:"instance",get:function(){return n}}]),s}(e);return Object.setPrototypeOf(i,e),Object.defineProperty(i,"name",{value:e.name}),i},singleton:function(e){var i=null,r=function(r){function s(){var t;if(i)return console.warn("[singleton] Multiple instances of "+e.name+" detected. Returning existing instance."),i||n(t);for(var o=arguments.length,s=new Array(o),c=0;c<o;c++)s[c]=arguments[c];return t=r.call.apply(r,[this].concat(s))||this,(i=n(t))||n(t)}return t(s,r),o(s,null,[{key:"instance",get:function(){return i||(i=new s),i}}]),s}(e);return Object.setPrototypeOf(r,e),Object.defineProperty(r,"name",{value:e.name}),r}}),i._RF.push({},"938e5CqNSRKgrjEvTm8O/eP","Singleton",void 0),i._RF.pop()}}}));

System.register("chunks:///_virtual/SocketManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GameEvents.ts","./EventManager.ts"],(function(e){var t,n,a,s,o,i,r,c,u;return{setters:[function(e){t=e.inheritsLoose,n=e.extends},function(e){a=e.cclegacy,s=e._decorator,o=e.EventTarget,i=e.Component},function(e){r=e.SocketState,c=e.GameEventCode},function(e){u=e.EventManager}],execute:function(){var h,l;a._RF.push({},"534fbmJaVNBN6bGp9X0YpzD","SocketManager",void 0);var d=s.ccclass;e("SocketManager",d("SocketManager")(((l=function(e){function a(){for(var t,n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return(t=e.call.apply(e,[this].concat(a))||this)._socket=null,t._socketState=r.CLOSED,t._isAuthenticated=!1,t._reconnectAttempts=0,t._maxReconnectAttempts=5,t._reconnectDelay=3e3,t._heartbeatInterval=1e4,t._heartbeatTimer=null,t._lastMessageTime=0,t._connectionHealthTimer=null,t._backgroundHeartbeatInterval=15e3,t._lastHeartbeatSent=0,t._isPageVisible=!0,t._isIframeFocused=!0,t._lastFocusTime=Date.now(),t._syncOnFocusTimer=null,t._eventTarget=new o,t._messageQueue=[],t._authData={token:"",playerId:"",sessionId:"",balance:0,isAuthenticated:!1},t._config={socketUrl:"wss://gfty.spon-mini.com/fty_apartment_01",reconnectAttempts:5,reconnectDelay:3e3,heartbeatInterval:1e4,requestTimeout:1e4},t}t(a,e);var s=a.prototype;return s.start=function(){a._instance=this,this.privateSetupFocusManagement(),console.log("[SocketManager] Initialized")},a.GetInstance=function(){return this._instance},s.ConnectWithToken=function(e,t){var n=this;return void 0===t&&(t=""),new Promise((function(a,s){if(n._socketState===r.OPEN)return console.log("[SocketManager] Already connected"),void a(!0);n._authData.token=e,n._authData.playerId=t;try{console.log("[SocketManager] Connecting to:",n._config.socketUrl),n._socket=new WebSocket(n._config.socketUrl),n._socketState=r.CONNECTING,n._socket.onopen=function(){console.log("[SocketManager] WebSocket connected"),n._socketState=r.OPEN,n._reconnectAttempts=0,n._sendLoginRequest(),n._startHeartbeat(),n._processMessageQueue(),a(!0)},n._socket.onmessage=function(e){n._handleMessage(e)},n._socket.onclose=function(e){console.log("[SocketManager] WebSocket closed:",e.code,e.reason),n._socketState=r.CLOSED,n._isAuthenticated=!1,n._stopHeartbeat(),1e3!==e.code&&1001!==e.code&&n._handleReconnection()},n._socket.onerror=function(e){console.error("[SocketManager] WebSocket error:",e),n._emitError({code:-1,message:"WebSocket connection error",type:"connection",timestamp:(new Date).toISOString()}),s(new Error("WebSocket connection failed"))}}catch(e){console.error("[SocketManager] Connection failed:",e),s(e)}}))},s.disconnect=function(){this._socket&&this._socketState===r.OPEN&&(console.log("[SocketManager] Disconnecting..."),this._socketState=r.CLOSING,this._socket.close(1e3,"Client disconnect")),this._stopHeartbeat(),this._isAuthenticated=!1},s.sendMessage=function(e){if(this._socketState!==r.OPEN)return console.warn("[SocketManager] Socket not connected, queuing message"),this._messageQueue.push(JSON.stringify(e)),!1;try{var t=JSON.stringify(e);return this._socket.send(t),console.log("[SocketManager] Message sent:",e),!0}catch(e){return console.error("[SocketManager] Failed to send message:",e),!1}},s.on=function(e,t,n){this._eventTarget&&"function"==typeof this._eventTarget.on&&this._eventTarget.on(e,t,n)},s.off=function(e,t,n){this._eventTarget&&"function"==typeof this._eventTarget.off&&this._eventTarget.off(e,t,n)},s.getSocketState=function(){return this._socketState},s.isAuthenticated=function(){return this._isAuthenticated},s.getAuthData=function(){return n({},this._authData)},s._handleMessage=function(e){try{if(!e.data||"string"!=typeof e.data)return void console.warn("[SocketManager] Received invalid message data");this._parseMultipleJsonMessages(e.data)}catch(n){console.warn("[SocketManager] Failed to parse message - skipping:",n.message);var t=e.data.length>100?e.data.substring(0,100)+"...":e.data;console.warn("[SocketManager] Problematic data:",t)}},s._parseMultipleJsonMessages=function(e){for(var t=e.trim();t.length>0;)try{var n=JSON.parse(t);console.log("[SocketManager] Received message:",n),n.code===c.GAME_INIT&&this._handleAuthentication(n),this._emitGameEvent(n);break}catch(e){for(var a=0,s=-1,o=0;o<t.length;o++)if("{"===t[o])a++;else if("}"===t[o]&&0===--a){s=o+1;break}if(!(s>0)){console.warn("[SocketManager] Cannot parse message structure");break}var i=t.substring(0,s);try{var r=JSON.parse(i);console.log("[SocketManager] Received message:",r),r.code===c.GAME_INIT&&this._handleAuthentication(r),this._emitGameEvent(r),t=t.substring(s).trim()}catch(e){console.warn("[SocketManager] Failed to parse extracted JSON:",e.message);break}}},s._handleAuthentication=function(e){e.code===c.GAME_INIT&&e.data?(this._isAuthenticated=!0,this._authData.isAuthenticated=!0,this._authData.playerId=e.data.playerId||this._authData.playerId,this._authData.sessionId=e.data.sessionId||e.data.session||"",console.log("[SocketManager] Authentication successful"),this._eventTarget.emit("authenticated",this._authData),this.sendMessage({code:204})):(console.error("[SocketManager] Authentication failed - invalid response structure"),this._emitError({code:401,message:"Authentication failed - invalid response",type:"authentication",timestamp:(new Date).toISOString()}))},s._emitGameEvent=function(e){var t,n=((t={})[c.GAME_INIT]="game-init",t[c.GAME_STATUS_UPDATE]="game-status-update",t[c.GAME_RESULT]="game-result",t[c.BALANCE_UPDATE]="balance-update",t)[e.code];n?(this._eventTarget.emit(n,e),this._eventTarget.emit("game-event",e),e.code===c.BALANCE_UPDATE&&e.data&&e.data.history&&(console.log("[SocketManager] Processing history data from balance update"),u.instance.emit("server-history-received",e))):console.warn("[SocketManager] Unknown event code:",e.code)},s._emitError=function(e){this._eventTarget.emit("error",e)},s._handleReconnection=function(){var e=this;this._reconnectAttempts<this._maxReconnectAttempts?(this._reconnectAttempts++,console.log("[SocketManager] Reconnecting... Attempt "+this._reconnectAttempts+"/"+this._maxReconnectAttempts),setTimeout((function(){e._authData.token&&e.ConnectWithToken(e._authData.token,e._authData.playerId)}),this._reconnectDelay)):(console.error("[SocketManager] Max reconnection attempts reached"),this._emitError({code:-3,message:"Connection lost - max reconnection attempts reached",type:"connection",timestamp:(new Date).toISOString()}))},s._detectPlatform=function(){var e=navigator.userAgent;return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(e)?"mobile":"desktop"},s._sendLoginRequest=function(){var e={clientinfo:"*******",clientip:"",code:101,platform:this._detectPlatform(),referrer:window.location.hostname,token:this._authData.token};console.log("[SocketManager] Sending login request:",e),this.sendMessage(e)},s._startHeartbeat=function(){var e=this;this._stopHeartbeat(),this._lastHeartbeatSent=Date.now(),this._heartbeatTimer=setInterval((function(){e._socketState===r.OPEN&&e._isAuthenticated&&e._sendHeartbeatMessage()}),this._heartbeatInterval),console.log("[SocketManager] Heartbeat started with adaptive focus management")},s._stopHeartbeat=function(){this._heartbeatTimer&&(clearInterval(this._heartbeatTimer),this._heartbeatTimer=null)},s._sendHeartbeatMessage=function(){var e=Date.now(),t=this._isPageVisible&&this._isIframeFocused,n=t?this._heartbeatInterval:this._backgroundHeartbeatInterval;if(e-this._lastHeartbeatSent>=n)if(this.sendMessage({code:204})){this._lastHeartbeatSent=e;var a=t?"focused":"background";console.log("[SocketManager] Heartbeat sent ("+a+" mode, interval: "+n+"ms)")}else console.warn("[SocketManager] Failed to send heartbeat - connection may be lost")},s._forceHeartbeat=function(){this._socketState===r.OPEN&&this._isAuthenticated&&(console.log("[SocketManager] Forcing immediate heartbeat"),this._lastHeartbeatSent=0,this._sendHeartbeatMessage())},s._processMessageQueue=function(){for(;this._messageQueue.length>0;){var e=this._messageQueue.shift();e&&this._socketState===r.OPEN&&this._socket.send(e)}},s.placeBet=function(e,t,n){if(!this._isAuthenticated)return console.error("[SocketManager] Not authenticated - cannot place bet"),!1;var a={type:"place_bet",data:{bet_type:e,amount:t,round_id:n||"",timestamp:(new Date).toISOString(),token:this._authData.token}};return this.sendMessage(a)},s.cancelBets=function(){if(!this._isAuthenticated)return console.error("[SocketManager] Not authenticated - cannot cancel bets"),!1;var e={type:"cancel_bet",data:{timestamp:(new Date).toISOString(),token:this._authData.token}};return this.sendMessage(e)},s.requestGameState=function(){return console.log("[SocketManager] requestGameState() called but disabled - server sends updates automatically"),!0},s.requestGameHistory=function(e){void 0===e&&(e=20);var t={type:"get_history",data:{limit:e,timestamp:(new Date).toISOString()}};return this.sendMessage(t)},s.requestBalance=function(){return this.sendMessage({code:204})},s.updateConfig=function(e){this._config=n({},this._config,e),console.log("[SocketManager] Configuration updated:",this._config)},s.getConfig=function(){return n({},this._config)},s.forceReconnect=function(){var e=this;console.log("[SocketManager] Forcing reconnection..."),this.disconnect(),setTimeout((function(){e._authData.token&&e.ConnectWithToken(e._authData.token,e._authData.playerId)}),1e3)},s.getHeartbeatStatus=function(){var e=this._isPageVisible&&this._isIframeFocused,t=Date.now();return{isActive:null!==this._heartbeatTimer,isFocused:e,currentInterval:e?this._heartbeatInterval:this._backgroundHeartbeatInterval,lastHeartbeatSent:this._lastHeartbeatSent,timeSinceLastHeartbeat:t-this._lastHeartbeatSent,isPageVisible:this._isPageVisible,isIframeFocused:this._isIframeFocused}},s.onDestroy=function(){this.privateCleanupFocusManagement(),this.disconnect(),this._eventTarget&&"function"==typeof this._eventTarget.removeAll&&this._eventTarget.removeAll(this),console.log("[SocketManager] Destroyed and cleaned up")},s.privateSetupFocusManagement=function(){document.addEventListener("visibilitychange",this.privateOnVisibilityChange.bind(this)),window.addEventListener("focus",this.privateOnWindowFocus.bind(this)),window.addEventListener("blur",this.privateOnWindowBlur.bind(this)),window.addEventListener("message",this.privateOnParentMessage.bind(this)),console.log("[SocketManager] Focus management setup complete")},s.privateOnVisibilityChange=function(){this._isPageVisible=!document.hidden,this._isPageVisible?(console.log("[SocketManager] Page became visible - syncing data and adjusting heartbeat"),this.privateHandleFocusReturn(),this._adjustHeartbeatForFocus()):(console.log("[SocketManager] Page became hidden - switching to background heartbeat mode"),this._adjustHeartbeatForFocus())},s.privateOnWindowFocus=function(){this._isIframeFocused=!0,console.log("[SocketManager] Window focused - syncing data and adjusting heartbeat"),this.privateHandleFocusReturn(),this._adjustHeartbeatForFocus()},s.privateOnWindowBlur=function(){this._isIframeFocused=!1,this._lastFocusTime=Date.now(),console.log("[SocketManager] Window blurred - switching to background heartbeat mode"),this._adjustHeartbeatForFocus()},s._adjustHeartbeatForFocus=function(){var e=this._isPageVisible&&this._isIframeFocused,t=e?this._heartbeatInterval:this._backgroundHeartbeatInterval;if(this._socketState===r.OPEN&&this._isAuthenticated){var n=Date.now()-this._lastHeartbeatSent;!e&&n>=this._heartbeatInterval&&(console.log("[SocketManager] Focus lost - sending immediate heartbeat to maintain connection"),this._sendHeartbeatMessage()),console.log("[SocketManager] Heartbeat adjusted for "+(e?"focused":"background")+" mode ("+t+"ms interval)")}},s.privateOnParentMessage=function(e){e.data&&"iframe-focus"===e.data.type&&(console.log("[SocketManager] Received iframe focus message from parent"),this.privateHandleFocusReturn())},s.privateHandleFocusReturn=function(){var e=this,t=Date.now()-this._lastFocusTime;this._syncOnFocusTimer&&(clearTimeout(this._syncOnFocusTimer),this._syncOnFocusTimer=null),t>5e3?(console.log("[SocketManager] Been away for "+t+"ms - forcing data sync"),this._syncOnFocusTimer=setTimeout((function(){e.privateForceSyncData()}),500)):console.log("[SocketManager] Quick focus return - no sync needed"),this._lastFocusTime=Date.now()},s.privateForceSyncData=function(){if(!this._isAuthenticated||this._socketState!==r.OPEN)return console.log("[SocketManager] Not authenticated or connected - attempting reconnection"),void this.forceReconnect();console.log("[SocketManager] Forcing data sync..."),this.sendMessage({code:108}),this.sendMessage({code:204}),this._eventTarget.emit("data-sync-requested",{timestamp:Date.now(),reason:"focus-return"}),console.log("[SocketManager] Data sync requests sent")},s.privateCleanupFocusManagement=function(){document.removeEventListener("visibilitychange",this.privateOnVisibilityChange.bind(this)),window.removeEventListener("focus",this.privateOnWindowFocus.bind(this)),window.removeEventListener("blur",this.privateOnWindowBlur.bind(this)),window.removeEventListener("message",this.privateOnParentMessage.bind(this)),this._syncOnFocusTimer&&(clearTimeout(this._syncOnFocusTimer),this._syncOnFocusTimer=null),console.log("[SocketManager] Focus management cleanup complete")},a}(i))._instance=null,h=l))||h);a._RF.pop()}}}));

System.register("chunks:///_virtual/SoundManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Singleton.ts"],(function(e){var o,n,i,t,s,l,r,a,g,c,u;return{setters:[function(e){o=e.applyDecoratedDescriptor,n=e.inheritsLoose,i=e.initializerDefineProperty,t=e.assertThisInitialized,s=e.createClass},function(e){l=e.cclegacy,r=e._decorator,a=e.AudioClip,g=e.AudioSource,c=e.Component},function(e){u=e.ccsingleton}],execute:function(){var f,d,h,p,S,b,m,O,M,T;l._RF.push({},"97eb3MQ+y9Bg7b9YAqkpqCw","SoundManager",void 0);var x=r.ccclass,y=r.property;e("SoundManager",(f=x("SoundManager"),d=y(a),h=y(a),p=y(a),f(S=u(((T=function(e){function o(){for(var o,n=arguments.length,s=new Array(n),l=0;l<n;l++)s[l]=arguments[l];return o=e.call.apply(e,[this].concat(s))||this,i(o,"bgmClip",m,t(o)),i(o,"sfxToggleOnClip",O,t(o)),i(o,"sfxToggleOffClip",M,t(o)),o.bgmSource=null,o.sfxToggleOnSource=null,o.sfxToggleOffSource=null,o.isBGMEnabled=!0,o}n(o,e);var l=o.prototype;return l.onLoad=function(){this.createAudioSources(),this.loadSoundPreference()},l.start=function(){console.log("[SoundManager] start() - bgmSource:",!!this.bgmSource,"bgmClip:",!!this.bgmClip),this.isBGMEnabled&&this.bgmSource&&this.bgmClip&&(this.bgmSource.clip=this.bgmClip,this.bgmSource.play(),console.log("[SoundManager] BGM started based on preference")),this.notifyParentWindow(this.isBGMEnabled)},l.createAudioSources=function(){this.bgmSource=this.node.addComponent(g),this.bgmSource&&(this.bgmSource.loop=!0,this.bgmSource.playOnAwake=!1,this.bgmSource.clip=this.bgmClip),this.sfxToggleOnSource=this.node.addComponent(g),this.sfxToggleOnSource&&(this.sfxToggleOnSource.loop=!1,this.sfxToggleOnSource.playOnAwake=!1,this.sfxToggleOnSource.clip=this.sfxToggleOnClip),this.sfxToggleOffSource=this.node.addComponent(g),this.sfxToggleOffSource&&(this.sfxToggleOffSource.loop=!1,this.sfxToggleOffSource.playOnAwake=!1,this.sfxToggleOffSource.clip=this.sfxToggleOffClip),console.log("[SoundManager] Audio sources created and clips assigned")},l.toggleBGM=function(){return this.isBGMEnabled=!this.isBGMEnabled,this.saveSoundPreference(),this.notifyParentWindow(this.isBGMEnabled),this.isBGMEnabled?(this.bgmSource&&this.bgmClip&&(this.bgmSource.clip=this.bgmClip,this.bgmSource.play()),this.sfxToggleOnSource&&this.sfxToggleOnClip&&(this.sfxToggleOnSource.clip=this.sfxToggleOnClip,this.sfxToggleOnSource.play())):(this.bgmSource&&this.bgmSource.stop(),this.sfxToggleOffSource&&this.sfxToggleOffClip&&(this.sfxToggleOffSource.clip=this.sfxToggleOffClip,this.sfxToggleOffSource.play())),console.log("[SoundManager] BGM "+(this.isBGMEnabled?"enabled":"disabled")),this.isBGMEnabled},l.getBGMEnabled=function(){return this.isBGMEnabled},l.isBGMPlaying=function(){var e;return(null==(e=this.bgmSource)?void 0:e.playing)||!1},l.setBGMEnabled=function(e){this.isBGMEnabled!==e&&(this.isBGMEnabled=e,this.saveSoundPreference(),this.notifyParentWindow(e),e&&this.bgmSource&&this.bgmClip?(this.bgmSource.clip=this.bgmClip,this.bgmSource.play()):!e&&this.bgmSource&&this.bgmSource.stop(),console.log("[SoundManager] BGM "+(e?"enabled":"disabled")))},l.playToggleSound=function(e){e&&this.sfxToggleOnSource&&this.sfxToggleOnClip?(this.sfxToggleOnSource.clip=this.sfxToggleOnClip,this.sfxToggleOnSource.play()):!e&&this.sfxToggleOffSource&&this.sfxToggleOffClip&&(this.sfxToggleOffSource.clip=this.sfxToggleOffClip,this.sfxToggleOffSource.play())},l.notifyParentWindow=function(e){try{if("undefined"!=typeof window&&window.parent&&window.parent!==window){var o=e?1:2;window.parent.postMessage({code:o},"*"),console.log("[SoundManager] Notified parent window: sound "+(e?"ON":"OFF")+" (code: "+o+")")}}catch(e){console.warn("[SoundManager] Failed to notify parent window:",e.message)}},l.loadSoundPreference=function(){try{if("undefined"!=typeof localStorage){var e=localStorage.getItem(o.SOUND_PREFERENCE_KEY);null!==e?(this.isBGMEnabled="true"===e,console.log("[SoundManager] Loaded sound preference: "+(this.isBGMEnabled?"enabled":"disabled"))):console.log("[SoundManager] No saved sound preference found, using default: enabled")}}catch(e){console.warn("[SoundManager] Failed to load sound preference from localStorage:",e.message),this.isBGMEnabled=!0}},l.saveSoundPreference=function(){try{"undefined"!=typeof localStorage&&(localStorage.setItem(o.SOUND_PREFERENCE_KEY,this.isBGMEnabled.toString()),console.log("[SoundManager] Saved sound preference: "+(this.isBGMEnabled?"enabled":"disabled")))}catch(e){console.warn("[SoundManager] Failed to save sound preference to localStorage:",e.message)}},s(o,null,[{key:"instance",get:function(){return o.instance}}]),o}(c)).SOUND_PREFERENCE_KEY="tower_game_sound_enabled",m=o((b=T).prototype,"bgmClip",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=o(b.prototype,"sfxToggleOnClip",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=o(b.prototype,"sfxToggleOffClip",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=b))||S)||S));l._RF.pop()}}}));

System.register("chunks:///_virtual/SoundToggleButton.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GameManager.ts","./EventManager.ts"],(function(e){var n,t,o,i,a,r,s,u,g,c,l,h,p;return{setters:[function(e){n=e.applyDecoratedDescriptor,t=e.inheritsLoose,o=e.initializerDefineProperty,i=e.assertThisInitialized},function(e){a=e.cclegacy,r=e._decorator,s=e.Button,u=e.Sprite,g=e.SpriteFrame,c=e.Node,l=e.Component},function(e){h=e.GameManager},function(e){p=e.EventManager}],execute:function(){var d,f,m,M,B,I,b,v,y,S,E,O,G,T,F,z,W;a._RF.push({},"73d8dtBfUlLn6SQO/djhFKI","SoundToggleButton",void 0);var k=r.ccclass,w=r.property;e("SoundToggleButton",(d=k("SoundToggleButton"),f=w(s),m=w(u),M=w(g),B=w(g),I=w(g),b=w(g),v=w(h),d((E=n((S=function(e){function n(){for(var n,t=arguments.length,a=new Array(t),r=0;r<t;r++)a[r]=arguments[r];return n=e.call.apply(e,[this].concat(a))||this,o(n,"toggleButton",E,i(n)),o(n,"iconSprite",O,i(n)),o(n,"soundOnIcon",G,i(n)),o(n,"soundOffIcon",T,i(n)),o(n,"soundHoverWhenOffIcon",F,i(n)),o(n,"soundHoverWhenOnIcon",z,i(n)),o(n,"gameManager",W,i(n)),n.spriteFrameBackup=null,n}t(n,e);var a=n.prototype;return a.start=function(){if(this.gameManager=h.getInstance(),!this.gameManager)return p.instance.on("game-started",this.onGameManagerReady,this),void console.log("[SoundToggleButton] Waiting for GameManager...");this.setupButton(),this.updateIconWhenGameStarted()},a.setupButton=function(){this.toggleButton&&(this.toggleButton.node.on(s.EventType.CLICK,this.onToggleClick,this),this.toggleButton.node.on(c.EventType.MOUSE_ENTER,this.onMouseEnter,this),this.toggleButton.node.on(c.EventType.MOUSE_LEAVE,this.onMouseLeave,this))},a.onGameManagerReady=function(){this.gameManager=h.getInstance(),this.gameManager&&(this.setupButton(),this.updateIcon(),p.instance.off("game-started",this.onGameManagerReady,this))},a.onMouseLeave=function(){this.iconSprite.spriteFrame=this.spriteFrameBackup},a.onMouseEnter=function(){this.iconSprite.spriteFrame=this.spriteFrameBackup,this.iconSprite.spriteFrame=this.gameManager.IsBGMEnabled()?this.soundHoverWhenOnIcon:this.soundHoverWhenOffIcon},a.onToggleClick=function(){var e=this.gameManager.ToggleBGM();console.log("[SoundToggleButton] BGM toggled: "+(e?"ON":"OFF")),this.updateIcon()},a.updateIcon=function(){if(this.iconSprite&&this.gameManager){var e=this.gameManager.IsBGMEnabled();e&&this.soundOnIcon?this.iconSprite.spriteFrame=this.soundOnIcon:!e&&this.soundOffIcon&&(this.iconSprite.spriteFrame=this.soundOffIcon),this.spriteFrameBackup=this.iconSprite.spriteFrame}},a.updateIconWhenGameStarted=function(){this.node.active=!1,this.updateIcon(),this.node.active=!0},a.setBGMEnabled=function(e){this.gameManager&&(this.gameManager.IsBGMEnabled()!==e&&(this.gameManager.ToggleBGM(),this.updateIcon()))},a.getBGMEnabled=function(){return!!this.gameManager&&this.gameManager.IsBGMEnabled()},a.onDestroy=function(){this.toggleButton&&this.toggleButton.node.off(s.EventType.CLICK,this.onToggleClick,this)},n}(l)).prototype,"toggleButton",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=n(S.prototype,"iconSprite",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=n(S.prototype,"soundOnIcon",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=n(S.prototype,"soundOffIcon",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=n(S.prototype,"soundHoverWhenOffIcon",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=n(S.prototype,"soundHoverWhenOnIcon",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),W=n(S.prototype,"gameManager",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=S))||y));a._RF.pop()}}}));

System.register("chunks:///_virtual/SpineInitializer.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(i){var n,e,t,r,a,o,s,u,c,l;return{setters:[function(i){n=i.inheritsLoose,e=i.asyncToGenerator,t=i.regeneratorRuntime},function(i){r=i.cclegacy,a=i._decorator,o=i.log,s=i.warn,u=i.Component,c=i.error,l=i.sp}],execute:function(){var p,f;r._RF.push({},"0bb8ayKf1VFlJ+6/NAu1fg7","SpineInitializer",void 0);var d=a.ccclass;i("SpineInitializer",d("SpineInitializer")(((f=function(i){function r(){return i.apply(this,arguments)||this}n(r,i),r.getInstance=function(){return this._instance};var a=r.prototype;return a.onLoad=function(){null===r._instance?(r._instance=this,this.initializeSpine()):this.destroy()},a.onDestroy=function(){r._instance===this&&(r._instance=null)},a.initializeSpine=function(){var i=e(t().mark((function i(){return t().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(!r._isSpineLoaded){i.next=3;break}return o("[SpineInitializer] Spine already loaded"),i.abrupt("return");case 3:if(!r._isLoading){i.next=8;break}return o("[SpineInitializer] Spine loading in progress, waiting..."),i.next=7,r._loadPromise;case 7:return i.abrupt("return");case 8:return r._isLoading=!0,r._loadPromise=this.loadSpineWasm(),i.prev=10,i.next=13,r._loadPromise;case 13:i.sent?(r._isSpineLoaded=!0,o("[SpineInitializer] Spine WASM loaded successfully")):c("[SpineInitializer] Failed to load Spine WASM"),i.next=20;break;case 17:i.prev=17,i.t0=i.catch(10),c("[SpineInitializer] Error loading Spine WASM:",i.t0);case 20:return i.prev=20,r._isLoading=!1,i.finish(20);case 23:case"end":return i.stop()}}),i,this,[[10,17,20,23]])})));return function(){return i.apply(this,arguments)}}(),a.loadSpineWasm=function(){var i=e(t().mark((function i(){var n=this;return t().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.abrupt("return",new Promise((function(i){try{if(void 0===l)return s("[SpineInitializer] Spine module not available"),void i(!1);n.configureWasmMemory(),l.spine&&"function"==typeof l.spine.wasmInit?l.spine.wasmInit().then((function(){o("[SpineInitializer] Spine WASM initialized successfully"),i(!0)})).catch((function(e){c("[SpineInitializer] Spine WASM initialization failed:",e),n.tryFallbackInitialization().then(i)})):n.tryAlternativeInitialization().then(i)}catch(n){c("[SpineInitializer] Exception during Spine WASM loading:",n),i(!1)}})));case 1:case"end":return i.stop()}}),i)})));return function(){return i.apply(this,arguments)}}(),a.configureWasmMemory=function(){try{if("undefined"!=typeof WebAssembly&&WebAssembly.Memory){var i={initial:r.WASM_CONFIG.INITIAL_MEMORY/65536,maximum:r.WASM_CONFIG.MAXIMUM_MEMORY/65536,shared:!1};o("[SpineInitializer] Configured WASM memory:",i)}if("undefined"!=typeof window&&window.spine){var n=window.spine;n.wasmMemoryConfig&&Object.assign(n.wasmMemoryConfig,r.WASM_CONFIG)}}catch(i){s("[SpineInitializer] Could not configure WASM memory:",i)}},a.tryFallbackInitialization=function(){var i=e(t().mark((function i(){return t().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.abrupt("return",new Promise((function(i){try{o("[SpineInitializer] Trying fallback initialization..."),setTimeout((function(){l&&l.spine?(o("[SpineInitializer] Fallback initialization successful"),i(!0)):(s("[SpineInitializer] Fallback initialization failed"),i(!1))}),1e3)}catch(n){c("[SpineInitializer] Fallback initialization error:",n),i(!1)}})));case 1:case"end":return i.stop()}}),i)})));return function(){return i.apply(this,arguments)}}(),a.tryAlternativeInitialization=function(){var i=e(t().mark((function i(){return t().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.abrupt("return",new Promise((function(i){try{o("[SpineInitializer] Trying alternative initialization...");for(var n=0,e=[function(){return l&&l.spine},function(){return"undefined"!=typeof window&&window.spine},function(){return"undefined"!=typeof globalThis&&globalThis.spine}];n<e.length;n++){var t=e[n];try{if(t())return o("[SpineInitializer] Alternative initialization successful"),void i(!0)}catch(i){}}s("[SpineInitializer] All alternative initialization methods failed"),i(!1)}catch(n){c("[SpineInitializer] Alternative initialization error:",n),i(!1)}})));case 1:case"end":return i.stop()}}),i)})));return function(){return i.apply(this,arguments)}}(),r.isSpineReady=function(){return r._isSpineLoaded},r.waitForSpine=function(){var i=e(t().mark((function i(){return t().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(!r._isSpineLoaded){i.next=2;break}return i.abrupt("return",!0);case 2:if(!r._isLoading||!r._loadPromise){i.next=6;break}return i.next=5,r._loadPromise;case 5:return i.abrupt("return",i.sent);case 6:if(r._instance){i.next=9;break}return s("[SpineInitializer] No instance found, Spine may not be properly initialized"),i.abrupt("return",!1);case 9:return i.abrupt("return",r._isSpineLoaded);case 10:case"end":return i.stop()}}),i)})));return function(){return i.apply(this,arguments)}}(),r.getMemoryInfo=function(){var i={isSpineLoaded:r._isSpineLoaded,isLoading:r._isLoading,wasmConfig:r.WASM_CONFIG};if("undefined"!=typeof performance&&performance.memory){var n=performance.memory;i.browserMemory={used:(n.usedJSHeapSize/1024/1024).toFixed(2)+"MB",total:(n.totalJSHeapSize/1024/1024).toFixed(2)+"MB",limit:(n.jsHeapSizeLimit/1024/1024).toFixed(2)+"MB"}}return i},r.forceReload=function(){var i=e(t().mark((function i(){return t().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(s("[SpineInitializer] Force reloading Spine WASM..."),r._isSpineLoaded=!1,r._isLoading=!1,r._loadPromise=null,!r._instance){i.next=8;break}return i.next=7,r._instance.initializeSpine().then((function(){return r._isSpineLoaded}));case 7:return i.abrupt("return",i.sent);case 8:return i.abrupt("return",!1);case 9:case"end":return i.stop()}}),i)})));return function(){return i.apply(this,arguments)}}(),a.debugStatus=function(){console.log("[SpineInitializer] === DEBUG STATUS ==="),console.log("Memory Info:",r.getMemoryInfo()),console.log("Spine Ready:",r.isSpineReady()),console.log("=== END DEBUG STATUS ===")},r}(u))._instance=null,f._isSpineLoaded=!1,f._isLoading=!1,f._loadPromise=null,f.WASM_CONFIG={INITIAL_MEMORY:16777216,MAXIMUM_MEMORY:134217728,ALLOW_MEMORY_GROWTH:!0,OPTIMIZE_FOR_SIZE:!0},p=f))||p);r._RF.pop()}}}));

System.register("chunks:///_virtual/SpineIntegrationExample.ts",["./rollupPluginModLoBabelHelpers.js","cc","./OptimizedSpineComponent.ts","./SpineMemoryManager.ts","./SpineStartupManager.ts"],(function(n){var e,i,t,o,a,s,p,r,m,u,l,c,h,y;return{setters:[function(n){e=n.applyDecoratedDescriptor,i=n.inheritsLoose,t=n.initializerDefineProperty,o=n.assertThisInitialized,a=n.asyncToGenerator,s=n.regeneratorRuntime},function(n){p=n.cclegacy,r=n._decorator,m=n.log,u=n.warn,l=n.Component},function(n){c=n.OptimizedSpineComponent},function(n){h=n.SpineMemoryManager},function(n){y=n.SpineStartupManager}],execute:function(){var S,d,g,f,C,x,A,I,E;p._RF.push({},"4eeb1Ei40dFBIkH+OuPFXbI","SpineIntegrationExample",void 0);var v=r.ccclass,R=r.property;n("SpineIntegrationExample",(S=v("SpineIntegrationExample"),d=R(c),g=R({displayName:"Animation Names",tooltip:"List of animation names to cycle through"}),f=R({displayName:"Auto Play",tooltip:"Automatically start playing animations"}),S((A=e((x=function(n){function e(){for(var e,i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];return e=n.call.apply(n,[this].concat(a))||this,t(e,"spineComponent",A,o(e)),t(e,"animationNames",I,o(e)),t(e,"autoPlay",E,o(e)),e._currentAnimationIndex=0,e._isSystemReady=!1,e}i(e,n);var p=e.prototype;return p.onLoad=function(){this.node.on("spine-system-ready",this.onSpineSystemReady,this),this.node.on("spine-error",this.onSpineError,this)},p.start=function(){var n=this.getComponent(y);n&&n.isInitialized()?this.onSpineSystemReady():m("[SpineIntegrationExample] Waiting for Spine system to initialize...")},p.onDestroy=function(){this.node.off("spine-system-ready",this.onSpineSystemReady,this),this.node.off("spine-error",this.onSpineError,this)},p.onSpineSystemReady=function(){m("[SpineIntegrationExample] Spine system is ready!"),this._isSystemReady=!0,this.spineComponent&&this.initializeSpineComponent(),this.autoPlay&&this.startAutoPlay()},p.initializeSpineComponent=function(){var n=a(s().mark((function n(){var e,i;return s().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(this.spineComponent){n.next=3;break}return u("[SpineIntegrationExample] No spine component assigned"),n.abrupt("return");case 3:e=0,i=10;case 5:if(this.spineComponent.isReady()||!(e<i)){n.next=11;break}return n.next=8,this.waitForSeconds(.5);case 8:e++,n.next=5;break;case 11:this.spineComponent.isReady()?(m("[SpineIntegrationExample] Spine component is ready"),this.animationNames.length>0&&this.playAnimation(this.animationNames[0])):u("[SpineIntegrationExample] Spine component failed to initialize");case 12:case"end":return n.stop()}}),n,this)})));return function(){return n.apply(this,arguments)}}(),p.playAnimation=function(n,e){return void 0===e&&(e=!0),this._isSystemReady&&this.spineComponent&&this.spineComponent.isReady()?this.spineComponent.safeSetAnimation(0,n,e)?(m("[SpineIntegrationExample] Playing animation: "+n),!0):(u("[SpineIntegrationExample] Failed to play animation: "+n),!1):(u("[SpineIntegrationExample] Cannot play animation '"+n+"' - system not ready"),!1)},p.queueAnimation=function(n,e,i){return void 0===e&&(e=!1),void 0===i&&(i=0),this._isSystemReady&&this.spineComponent&&this.spineComponent.isReady()?this.spineComponent.safeAddAnimation(0,n,e,i)?(m("[SpineIntegrationExample] Queued animation: "+n),!0):(u("[SpineIntegrationExample] Failed to queue animation: "+n),!1):(u("[SpineIntegrationExample] Cannot queue animation '"+n+"' - system not ready"),!1)},p.startAutoPlay=function(){0!==this.animationNames.length&&(this.playAnimation(this.animationNames[0]),this.schedule(this.cycleAnimation,5))},p.cycleAnimation=function(){if(!(this.animationNames.length<=1)){this._currentAnimationIndex=(this._currentAnimationIndex+1)%this.animationNames.length;var n=this.animationNames[this._currentAnimationIndex];this.playAnimation(n)}},p.onSpineError=function(n){u("[SpineIntegrationExample] Spine error occurred:",n.detail),this.spineComponent&&(m("[SpineIntegrationExample] Attempting to recover from spine error..."),this.spineComponent.reinitialize().then((function(){m("[SpineIntegrationExample] Spine component reinitialized")})).catch((function(n){u("[SpineIntegrationExample] Failed to reinitialize spine component:",n)})))},p.waitForSeconds=function(n){var e=this;return new Promise((function(i){e.scheduleOnce(i,n)}))},p.getSystemStatus=function(){var n={isSystemReady:this._isSystemReady,hasSpineComponent:!!this.spineComponent,spineComponentReady:!!this.spineComponent&&this.spineComponent.isReady(),currentAnimation:this._currentAnimationIndex,animationNames:this.animationNames},e=h.getInstance();return e&&(n.memoryStatus=e.getMemoryStatus()),n},p.debugStatus=function(){console.log("[SpineIntegrationExample] === DEBUG STATUS ==="),console.log("System Status:",this.getSystemStatus()),this.spineComponent&&this.spineComponent.debugStatus();var n=h.getInstance();n&&n.debugStatus(),console.log("=== END DEBUG STATUS ===")},p.stopAnimation=function(){if(this.spineComponent&&this.spineComponent.isReady()){var n=this.spineComponent.getSpineComponent();n&&(n.clearTracks(),m("[SpineIntegrationExample] Stopped all animations"))}},p.pauseAnimation=function(n){if(this.spineComponent&&this.spineComponent.isReady()){var e=this.spineComponent.getSpineComponent();e&&(e.paused=n,m("[SpineIntegrationExample] Animation "+(n?"paused":"resumed")))}},p.setAnimationSpeed=function(n){if(this.spineComponent&&this.spineComponent.isReady()){var e=this.spineComponent.getSpineComponent();e&&(e.timeScale=n,m("[SpineIntegrationExample] Animation speed set to: "+n))}},p.isAnimationPlaying=function(){if(this.spineComponent&&this.spineComponent.isReady()){var n=this.spineComponent.getSpineComponent();if(n&&n.findAnimation)return!n.paused}return!1},p.getAvailableAnimations=function(){if(this.spineComponent&&this.spineComponent.isReady()){var n=this.spineComponent.getSpineComponent();if(n&&n.skeletonData)return this.animationNames.length>0?this.animationNames:[]}return[]},e}(l)).prototype,"spineComponent",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=e(x.prototype,"animationNames",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return["idle","walk","run"]}}),E=e(x.prototype,"autoPlay",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),C=x))||C));p._RF.pop()}}}));

System.register("chunks:///_virtual/SpineMemoryManager.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(e){var n,i,t,o,r,a,s,m,p;return{setters:[function(e){n=e.applyDecoratedDescriptor,i=e.inheritsLoose,t=e.initializerDefineProperty,o=e.assertThisInitialized},function(e){r=e.cclegacy,a=e._decorator,s=e.log,m=e.warn,p=e.Component}],execute:function(){var c,l,u,h,d,y,M,f,g,S,v,C;r._RF.push({},"0059eKxGm9PRabx/z2fw7Rt","SpineMemoryManager",void 0);var _=a.ccclass,z=a.property;e("SpineMemoryManager",(c=_("SpineMemoryManager"),l=z({displayName:"Max Concurrent Animations",tooltip:"Maximum number of Spine animations that can run simultaneously"}),u=z({displayName:"Memory Threshold (MB)",tooltip:"Memory threshold in MB before optimization kicks in"}),h=z({displayName:"Enable Auto Cleanup",tooltip:"Automatically cleanup unused Spine resources"}),d=z({displayName:"Cleanup Interval (seconds)",tooltip:"Interval for automatic cleanup in seconds"}),c(((C=function(e){function n(){for(var n,i=arguments.length,r=new Array(i),a=0;a<i;a++)r[a]=arguments[a];return(n=e.call.apply(e,[this].concat(r))||this)._activeSpineComponents=new Set,n._memoryThreshold=52428800,n._maxConcurrentAnimations=5,n._isMemoryOptimized=!1,t(n,"maxConcurrentAnimations",f,o(n)),t(n,"memoryThresholdMB",g,o(n)),t(n,"enableAutoCleanup",S,o(n)),t(n,"cleanupInterval",v,o(n)),n}i(n,e),n.getInstance=function(){return this._instance};var r=n.prototype;return r.onLoad=function(){null===n._instance?(n._instance=this,this.initializeMemoryManager()):this.destroy()},r.onDestroy=function(){n._instance===this&&(this.cleanup(),n._instance=null)},r.initializeMemoryManager=function(){this._memoryThreshold=1024*this.memoryThresholdMB*1024,this._maxConcurrentAnimations=this.maxConcurrentAnimations,this.enableAutoCleanup&&this.schedule(this.performCleanup,this.cleanupInterval),this.schedule(this.monitorMemoryUsage,5),s("[SpineMemoryManager] Initialized with settings:",{maxConcurrentAnimations:this._maxConcurrentAnimations,memoryThreshold:this.memoryThresholdMB+"MB",autoCleanup:this.enableAutoCleanup})},r.registerSpineComponent=function(e){e?(this._activeSpineComponents.add(e),this._activeSpineComponents.size>this._maxConcurrentAnimations&&this.optimizeMemoryUsage(),s("[SpineMemoryManager] Registered spine component. Active count: "+this._activeSpineComponents.size)):m("[SpineMemoryManager] Cannot register null spine component")},r.unregisterSpineComponent=function(e){this._activeSpineComponents.has(e)&&(this._activeSpineComponents.delete(e),s("[SpineMemoryManager] Unregistered spine component. Active count: "+this._activeSpineComponents.size))},r.optimizeMemoryUsage=function(){var e=this;if(!this._isMemoryOptimized){this._isMemoryOptimized=!0,s("[SpineMemoryManager] Starting memory optimization...");var n=0,i=Array.from(this._activeSpineComponents);i.sort((function(e,n){var i=e.node.active&&e.enabled,t=n.node.active&&n.enabled;return i&&!t?-1:!i&&t?1:0}));for(var t=this._maxConcurrentAnimations;t<i.length;t++){var o=i[t];o&&o.isValid&&(o.paused=!0,n++)}s("[SpineMemoryManager] Paused "+n+" animations to optimize memory"),this.scheduleOnce((function(){e._isMemoryOptimized=!1}),2)}},r.monitorMemoryUsage=function(){if("undefined"!=typeof performance&&performance.memory){var e=performance.memory.usedJSHeapSize;e>this._memoryThreshold&&(m("[SpineMemoryManager] Memory usage high: "+(e/1024/1024).toFixed(2)+"MB"),this.optimizeMemoryUsage())}},r.performCleanup=function(){var e=this,n=0;this._activeSpineComponents.forEach((function(i){i&&i.isValid&&i.node.isValid||(e._activeSpineComponents.delete(i),n++)})),n>0&&s("[SpineMemoryManager] Cleaned up "+n+" invalid spine components"),"undefined"!=typeof window&&window.gc&&(window.gc(),s("[SpineMemoryManager] Forced garbage collection"))},r.resumeAllAnimations=function(){this._activeSpineComponents.forEach((function(e){e&&e.isValid&&(e.paused=!1)})),s("[SpineMemoryManager] Resumed all animations")},r.getMemoryStatus=function(){var e={activeComponents:this._activeSpineComponents.size,maxConcurrentAnimations:this._maxConcurrentAnimations,isOptimized:this._isMemoryOptimized,memoryThreshold:this.memoryThresholdMB+"MB"};if("undefined"!=typeof performance&&performance.memory){var n=performance.memory;e.currentMemoryUsage=(n.usedJSHeapSize/1024/1024).toFixed(2)+"MB",e.memoryLimit=(n.jsHeapSizeLimit/1024/1024).toFixed(2)+"MB"}return e},r.cleanup=function(){this.unscheduleAllCallbacks(),this._activeSpineComponents.clear(),s("[SpineMemoryManager] Cleaned up all resources")},r.debugStatus=function(){console.log("[SpineMemoryManager] === DEBUG STATUS ==="),console.log("Memory Status:",this.getMemoryStatus()),console.log("Active Components:",Array.from(this._activeSpineComponents).map((function(e){return{name:e.node.name,active:e.node.active,enabled:e.enabled,paused:e.paused}}))),console.log("=== END DEBUG STATUS ===")},n}(p))._instance=null,f=n((M=C).prototype,"maxConcurrentAnimations",[l],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 5}}),g=n(M.prototype,"memoryThresholdMB",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 50}}),S=n(M.prototype,"enableAutoCleanup",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),v=n(M.prototype,"cleanupInterval",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 30}}),y=M))||y));r._RF.pop()}}}));

System.register("chunks:///_virtual/SpineStartupManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./SpineInitializer.ts","./SpineMemoryManager.ts"],(function(e){var t,i,n,r,a,o,s,u,p,l,c,m,f,y,d,S;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,n=e.initializerDefineProperty,r=e.assertThisInitialized,a=e.asyncToGenerator,o=e.regeneratorRuntime},function(e){s=e.cclegacy,u=e._decorator,p=e.error,l=e.warn,c=e.Node,m=e.director,f=e.log,y=e.Component},function(e){d=e.SpineInitializer},function(e){S=e.SpineMemoryManager}],execute:function(){var g,h,b,w,z,M,I,v,C,x,_;s._RF.push({},"ec5e5CrFoNB8pKhC/+tzZI9","SpineStartupManager",void 0);var A=u.ccclass,E=u.property;e("SpineStartupManager",(g=A("SpineStartupManager"),h=E({displayName:"Initialize On Start",tooltip:"Initialize Spine automatically when the scene starts"}),b=E({displayName:"Show Debug Info",tooltip:"Show debug information during initialization"}),w=E({displayName:"Retry On Failure",tooltip:"Retry initialization if it fails"}),z=E({displayName:"Max Retry Attempts",tooltip:"Maximum number of retry attempts"}),g((v=t((I=function(e){function t(){for(var t,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return t=e.call.apply(e,[this].concat(a))||this,n(t,"initializeOnStart",v,r(t)),n(t,"showDebugInfo",C,r(t)),n(t,"retryOnFailure",x,r(t)),n(t,"maxRetryAttempts",_,r(t)),t._isInitialized=!1,t._retryCount=0,t._initializationPromise=null,t}i(t,e);var s=t.prototype;return s.onLoad=function(){},s.start=function(){this.initializeOnStart&&this.initializeSpineSystem()},s.initializeSpineSystem=function(){var e=a(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._isInitialized){e.next=3;break}return f("[SpineStartupManager] Spine system already initialized"),e.abrupt("return",!0);case 3:if(!this._initializationPromise){e.next=8;break}return f("[SpineStartupManager] Initialization in progress, waiting..."),e.next=7,this._initializationPromise;case 7:return e.abrupt("return",e.sent);case 8:return this._initializationPromise=this.performInitialization(),e.next=11,this._initializationPromise;case 11:return e.abrupt("return",e.sent);case 12:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),s.performInitialization=function(){var e=a(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,f("[SpineStartupManager] Starting Spine system initialization..."),this.checkBrowserCompatibility()){e.next=4;break}throw new Error("Browser not compatible with WebAssembly");case 4:return this.getOrCreateSpineInitializer(),e.next=7,d.waitForSpine();case 7:if(e.sent){e.next=10;break}throw new Error("Failed to initialize Spine WASM");case 10:if(this.getOrCreateMemoryManager()){e.next=13;break}throw new Error("Failed to initialize Spine Memory Manager");case 13:return this.setupGlobalErrorHandling(),this.setupMemoryMonitoring(),this._isInitialized=!0,this._retryCount=0,f("[SpineStartupManager] Spine system initialized successfully"),this.showDebugInfo&&this.logInitializationInfo(),this.node.emit("spine-system-ready"),m.emit("spine-system-ready"),e.abrupt("return",!0);case 24:return e.prev=24,e.t0=e.catch(0),e.abrupt("return",this.handleInitializationError(e.t0));case 27:return e.prev=27,this._initializationPromise=null,e.finish(27);case 30:case"end":return e.stop()}}),e,this,[[0,24,27,30]])})));return function(){return e.apply(this,arguments)}}(),s.checkBrowserCompatibility=function(){try{if("undefined"==typeof WebAssembly)return p("[SpineStartupManager] WebAssembly not supported"),!1;try{new WebAssembly.Memory({initial:1})}catch(e){return p("[SpineStartupManager] WebAssembly Memory not supported:",e),!1}if("undefined"!=typeof performance&&performance.memory){var e=performance.memory,t=e.jsHeapSizeLimit-e.usedJSHeapSize;t<52428800&&l("[SpineStartupManager] Low memory available: "+(t/1024/1024).toFixed(2)+"MB")}return!0}catch(e){return p("[SpineStartupManager] Browser compatibility check failed:",e),!1}},s.getOrCreateSpineInitializer=function(){var e=d.getInstance();if(!e){var t=new c("SpineInitializer");e=t.addComponent(d),m.getScene().addChild(t),f("[SpineStartupManager] Created new SpineInitializer")}return e},s.getOrCreateMemoryManager=function(){var e=S.getInstance();if(!e){var t=new c("SpineMemoryManager");e=t.addComponent(S),m.getScene().addChild(t),f("[SpineStartupManager] Created new SpineMemoryManager")}return e},s.setupGlobalErrorHandling=function(){var e=this;if("undefined"!=typeof window){var t=window.onerror;window.onerror=function(i,n,r,a,o){return"string"==typeof i&&i.includes("WebAssembly")&&e.handleWebAssemblyError(i,o),!!t&&t(i,n,r,a,o)};var i=window.onunhandledrejection;window.onunhandledrejection=function(t){if(t.reason&&t.reason.message&&t.reason.message.includes("WebAssembly")&&(e.handleWebAssemblyError(t.reason.message,t.reason),t.preventDefault()),i)return i(t)}}},s.setupMemoryMonitoring=function(){this.schedule((function(){if("undefined"!=typeof performance&&performance.memory){var e=performance.memory,t=e.usedJSHeapSize/e.jsHeapSizeLimit*100;if(t>80){l("[SpineStartupManager] High memory usage: "+t.toFixed(1)+"%");var i=S.getInstance();i&&(null==i.performCleanup||i.performCleanup())}}}),10)},s.handleWebAssemblyError=function(e,t){if(t("[SpineStartupManager] WebAssembly Error:",e),e.includes("Out of memory")){l("[SpineStartupManager] WebAssembly out of memory - triggering cleanup");var i=S.getInstance();i&&(null==i.performCleanup||i.performCleanup()),"undefined"!=typeof window&&window.gc&&window.gc()}this.node.emit("spine-wasm-error",{message:e,error:t}),m.emit("spine-wasm-error",{message:e,error:t})},s.handleInitializationError=function(e){var t=this,i=e.message||"Unknown initialization error";return p("[SpineStartupManager] Initialization failed:",i),this.retryOnFailure&&this._retryCount<this.maxRetryAttempts?(this._retryCount++,l("[SpineStartupManager] Retrying initialization ("+this._retryCount+"/"+this.maxRetryAttempts+")"),new Promise((function(e){t.scheduleOnce(a(o().mark((function i(){var n;return o().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,t.performInitialization();case 2:n=i.sent,e(n);case 4:case"end":return i.stop()}}),i)}))),2)}))):(p("[SpineStartupManager] Max retry attempts reached"),this.node.emit("spine-system-failed",{error:e}),m.emit("spine-system-failed",{error:e}),Promise.resolve(!1))},s.logInitializationInfo=function(){console.log("[SpineStartupManager] === SPINE SYSTEM INFO ==="),console.log("Spine Ready:",d.isSpineReady()),console.log("Memory Info:",d.getMemoryInfo());var e=S.getInstance();e&&console.log("Memory Manager Status:",e.getMemoryStatus()),console.log("=== END SPINE SYSTEM INFO ===")},s.isInitialized=function(){return this._isInitialized},s.reinitialize=function(){var e=a(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._isInitialized=!1,this._retryCount=0,this._initializationPromise=null,e.next=5,this.initializeSpineSystem();case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),s.getSystemStatus=function(){return{isInitialized:this._isInitialized,retryCount:this._retryCount,spineReady:d.isSpineReady(),memoryManagerExists:!!S.getInstance(),browserCompatible:this.checkBrowserCompatibility()}},t}(y)).prototype,"initializeOnStart",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),C=t(I.prototype,"showDebugInfo",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),x=t(I.prototype,"retryOnFailure",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),_=t(I.prototype,"maxRetryAttempts",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 3}}),M=I))||M));s._RF.pop()}}}));

System.register("chunks:///_virtual/SpiteSkin.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(i){var t,e,n,r,o,s,a,l,u,p,c;return{setters:[function(i){t=i.applyDecoratedDescriptor,e=i.inheritsLoose,n=i.initializerDefineProperty,r=i.assertThisInitialized},function(i){o=i.cclegacy,s=i._decorator,a=i.Sprite,l=i.SpriteFrame,u=i.Color,p=i.warn,c=i.Component}],execute:function(){var h,f,S,m,k,b,d,g,y,v,D,w,z;o._RF.push({},"149f6QPszFCQrLGehkDgbXp","SpiteSkin",void 0);var C=s.ccclass,F=s.property;s.menu,s.executeInEditMode,i("default",(h=C("SpriteSkin"),f=F(a),S=F(l),m=F(l),k=F(u),h((g=t((d=function(i){function t(){for(var t,e=arguments.length,o=new Array(e),s=0;s<e;s++)o[s]=arguments[s];return t=i.call.apply(i,[this].concat(o))||this,n(t,"item",g,r(t)),n(t,"isSprite",y,r(t)),n(t,"spriteSkin",v,r(t)),n(t,"spriteDefaultSkin",D,r(t)),n(t,"isColor",w,r(t)),n(t,"colorSkin",z,r(t)),t.idSkin=0,t}e(t,i);var o=t.prototype;return o.onLoad=function(){!this.item&&this.getComponent(a)&&(this.item=this.getComponent(a))},o.setSkin=function(i){if(i<0||i>this.countSkin())return p("wrong id skin "),void(this.spriteDefaultSkin&&(this.item.spriteFrame=this.spriteDefaultSkin));this.idSkin=i,this.isSprite&&this.item&&(this.spriteSkin[i]?this.item.spriteFrame=this.spriteSkin[i]:this.spriteDefaultSkin&&(this.item.spriteFrame=this.spriteDefaultSkin)),this.isColor&&this.item&&(this.item.color=this.colorSkin[i])},o.countSkin=function(){return this.isSprite&&this.item?this.spriteSkin.length:this.isColor&&this.item?this.colorSkin.length:void 0},o.randomSkin=function(){var i=this.getRandomInt(0,this.countSkin()-1);this.setSkin(i)},o.getRandomInt=function(i,t){return i=Math.ceil(i),t=Math.floor(t),Math.floor(Math.random()*(t-i+1))+i},t}(c)).prototype,"item",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=t(d.prototype,"isSprite",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),v=t(d.prototype,"spriteSkin",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),D=t(d.prototype,"spriteDefaultSkin",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=t(d.prototype,"isColor",[F],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),z=t(d.prototype,"colorSkin",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),b=d))||b));o._RF.pop()}}}));

System.register("chunks:///_virtual/StartPopupView.ts",["./rollupPluginModLoBabelHelpers.js","cc","./EventManager.ts"],(function(t){var e,o,i,a,n,r,s,c,l,u,p,h;return{setters:[function(t){e=t.applyDecoratedDescriptor,o=t.inheritsLoose,i=t.initializerDefineProperty,a=t.assertThisInitialized},function(t){n=t.cclegacy,r=t._decorator,s=t.Node,c=t.tween,l=t.Vec3,u=t.Component},function(t){p=t.EventManager,h=t.GAME_EVENTS}],execute:function(){var w,d,f,N,v,g,y,b,P;n._RF.push({},"fa4aeAoOhpJW6VyIl39hsAp","StartPopupView",void 0);var m=r.ccclass,A=r.property;t("StartPopupView",(w=m("StartPopupView"),d=A(s),f=A(s),N=A(s),w((y=e((g=function(t){function e(){for(var e,o=arguments.length,n=new Array(o),r=0;r<o;r++)n[r]=arguments[r];return e=t.call.apply(t,[this].concat(n))||this,i(e,"characterNode",y,a(e)),i(e,"flowNode",b,a(e)),i(e,"startNode",P,a(e)),e}o(e,t);var n=e.prototype;return n.init=function(){this.flowNode.active=!1,this.startNode.active=!1,this.characterNode.active=!1,this.characterNode.setPosition(400,-69,0)},n.start=function(){this.init(),this.showCharacter()},n.onDestroy=function(){c(this.characterNode).stop(),c(this.flowNode).stop(),c(this.startNode).stop(),this.characterNode.active=!1,this.flowNode.active=!1,this.startNode.active=!1,p.instance.emit(h.STARTUP_POPUP_HIDDEN),this.node.destroy()},n.showCharacter=function(){var t=this;this.characterNode.active=!0,c(this.characterNode).to(1,{position:new l(165,-69,0)},{easing:"backOut"}).call((function(){t.showFlow()})).start()},n.showFlow=function(){var t=this;this.flowNode.active=!0,c(this.flowNode).to(.4,{position:new l(50,-19,0),scale:new l(1.1,1.1,1)},{easing:"backOut"}).to(.4,{position:new l(40,-19,0),scale:new l(1,1,1)},{easing:"backOut"}).to(.4,{position:new l(50,-19,0),scale:new l(1.1,1.1,1)},{easing:"backOut"}).to(.4,{position:new l(40,-19,0),scale:new l(1,1,1)},{easing:"backOut"}).call((function(){t.startAnimation()})).start()},n.startAnimation=function(){var t=this;this.startNode.active=!0,c(this.startNode).to(.3,{scale:new l(1.1,1.1,1.1)}).to(.3,{scale:new l(1,1,1)}).to(.3,{scale:new l(1.1,1.1,1.1)}).to(.3,{scale:new l(1,1,1)}).call((function(){console.log("[StartPopupView] Animation complete, notifying PopupManager to hide startup popup"),t.node.destroy()})).start()},e}(u)).prototype,"characterNode",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=e(g.prototype,"flowNode",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=e(g.prototype,"startNode",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=g))||v));n._RF.pop()}}}));

System.register("chunks:///_virtual/TimingOptimizationTest.ts",["./rollupPluginModLoBabelHelpers.js","cc","./TowerAnimation.ts","./DynamicRandomPhaseConfig.ts"],(function(e){var o,n,t,i,s,a,r,l,c,u,g,m,p,f,h,T;return{setters:[function(e){o=e.applyDecoratedDescriptor,n=e.inheritsLoose,t=e.initializerDefineProperty,i=e.assertThisInitialized,s=e.asyncToGenerator,a=e.regeneratorRuntime},function(e){r=e.cclegacy,l=e._decorator,c=e.Label,u=e.Node,g=e.Component},function(e){m=e.TowerAnimation},function(e){p=e.setRaceTimingRange,f=e.generateRandomRaceDuration,h=e.getCurrentTimingConfig,T=e.resetTimingToDefault}],execute:function(){var d,S,y,w,b,v,R,A,P,E,C;r._RF.push({},"b13b5soicdOZZZA4QUG4Dhl","TimingOptimizationTest",void 0);var D=l.ccclass,z=l.property;e("TimingOptimizationTest",(d=D("TimingOptimizationTest"),S=z(c),y=z(u),w=z(u),b=z(u),d((A=o((R=function(e){function o(){for(var o,n=arguments.length,s=new Array(n),a=0;a<n;a++)s[a]=arguments[a];return o=e.call.apply(e,[this].concat(s))||this,t(o,"statusLabel",A,i(o)),t(o,"towerA",P,i(o)),t(o,"towerP",E,i(o)),t(o,"towerT",C,i(o)),o.towerAnimations=[],o}n(o,e);var r=o.prototype;return r.onLoad=function(){console.log("[TimingOptimizationTest] Initializing timing optimization test system"),this.initializeTowerAnimations(),this.updateStatusDisplay()},r.initializeTowerAnimations=function(){for(var e=[this.towerA,this.towerP,this.towerT],o=["A","P","T"],n=0;n<e.length;n++)if(e[n]){var t=e[n].getComponent(m);t&&(t.towerPlayer=o[n],this.towerAnimations.push(t))}console.log("[TimingOptimizationTest] Initialized "+this.towerAnimations.length+" tower animations")},r.testCustomTimingRange=function(){console.log("\n=== TEST 1: Custom Timing Range (8-10s) ==="),p(8,10);for(var e=0;e<5;e++){var o=f();console.log("Sample "+(e+1)+":"),console.log("  Dynamic: "+(o/1e3).toFixed(2)+"s")}this.updateStatusDisplay()},r.testEasingSmoothness=function(){console.log("\n=== TEST 2: Easing Smoothness Comparison ==="),console.log("OLD SYSTEM (Jerky):"),console.log("  Phase 1: quadOut (abrupt speed changes)"),console.log("  Phase 2: quadInOut (harsh transitions)"),console.log("  Phase 3: quadIn (sudden acceleration)"),console.log("\nNEW SYSTEM (Smooth):"),console.log("  Phase 1: sineOut (smooth acceleration)"),console.log("  Phase 2: sineInOut (gentle transitions)"),console.log("  Phase 3: sineIn (natural acceleration)"),console.log("\nSmooth easing profiles provide:"),console.log("  ✓ Natural movement curves"),console.log("  ✓ Reduced visual jarring"),console.log("  ✓ Better user experience"),console.log("  ✓ More realistic race dynamics")},r.testOptimizedRace=function(){var e=s(a().mark((function e(){var o;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("\n=== TEST 3: Optimized Race Test ==="),!(this.towerAnimations.length<3)){e.next=4;break}return console.warn("Not enough tower animations for race test"),e.abrupt("return");case 4:return p(8,10),this.towerAnimations.forEach((function(e){return e.resetPosition()})),console.log("Starting optimized race with:"),console.log("  ✓ 8-10 second timing range"),console.log("  ✓ Smooth sine-based easing"),console.log("  ✓ Staggered completion timing"),o=this.towerAnimations.map((function(e,o){var n=o+1;return console.log("  "+e.towerPlayer+": Position "+n),e.startRace(n)})),e.prev=11,e.next=14,Promise.all(o);case 14:console.log("✅ Optimized race completed successfully!"),this.logRaceResults(),e.next=21;break;case 18:e.prev=18,e.t0=e.catch(11),console.error("❌ Race failed:",e.t0);case 21:case"end":return e.stop()}}),e,this,[[11,18]])})));return function(){return e.apply(this,arguments)}}(),r.testPerformanceComparison=function(){console.log("\n=== TEST 4: Performance Comparison ===");for(var e=performance.now(),o=0;o<100;o++)f();var n=performance.now()-e;console.log("Generated 200 race durations in "+n.toFixed(2)+"ms"),console.log("Average: "+(n/200).toFixed(4)+"ms per generation"),console.log("✅ Performance is excellent for real-time use")},r.testConfigurationFlexibility=function(){console.log("\n=== TEST 5: Configuration Flexibility ===");[[6,8],[8,10],[10,12],[5,15]].forEach((function(e){var o=e[0],n=e[1];p(o,n);h();var t=f();console.log("Range "+o+"-"+n+"s: Generated "+(t/1e3).toFixed(2)+"s")})),T(),console.log("✅ Reset to default 8-10s range")},r.runAllTests=function(){console.log("\n🚀 STARTING TIMING OPTIMIZATION TESTS 🚀\n"),this.testCustomTimingRange(),this.testEasingSmoothness(),this.testPerformanceComparison(),this.testConfigurationFlexibility(),console.log("\n✅ ALL TESTS COMPLETED SUCCESSFULLY! ✅"),console.log("\nThe new timing system provides:"),console.log("  ✓ Configurable 8-10 second race duration"),console.log("  ✓ Smooth sine-based easing (no more jerky movement)"),console.log("  ✓ Runtime configuration flexibility"),console.log("  ✓ Excellent performance"),console.log("  ✓ Realistic race dynamics"),this.updateStatusDisplay()},r.updateStatusDisplay=function(){if(this.statusLabel){var e=h(),o="DYNAMIC TIMING SYSTEM ACTIVE\nRange: "+e.minDuration/1e3+"-"+e.maxDuration/1e3+"s\nEasing: Smooth Sine-based\nSystem: Dynamic Random Phases\nStatus: Ready for 8-10s races";this.statusLabel.string=o}},r.logRaceResults=function(){console.log("\n📊 RACE RESULTS:"),this.towerAnimations.forEach((function(e){var o=e.getAnimationSystemInfo();console.log(o.towerPlayer+": "+(o.isDynamic?"Dynamic":"Simple")+" system"),console.log("  Duration: "+(o.totalDuration/1e3).toFixed(2)+"s"),console.log("  Phases: "+o.phaseCount+", Actions: "+o.actionCount)}))},r.quickTest=function(){console.log("🔥 QUICK TIMING TEST:"),p(8,10);for(var e=[],o=0;o<3;o++)e.push(f()/1e3);console.log("Sample durations: "+e.map((function(e){return e.toFixed(2)+"s"})).join(", ")),console.log("✅ Timing system working correctly!"),this.updateStatusDisplay()},o}(g)).prototype,"statusLabel",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=o(R.prototype,"towerA",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=o(R.prototype,"towerP",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=o(R.prototype,"towerT",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=R))||v));r._RF.pop()}}}));

System.register("chunks:///_virtual/TowerAnimation.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GameEvents.ts","./EventManager.ts","./WorkersAnimation.ts","./DynamicRandomPhaseConfig.ts"],(function(e){var t,i,o,n,r,a,s,l,h,b,u,c,w,d,p,m,f,g,T,A,y,P;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.inheritsLoose,o=e.initializerDefineProperty,n=e.assertThisInitialized,r=e.extends,a=e.asyncToGenerator,s=e.regeneratorRuntime},function(e){l=e.cclegacy,h=e._decorator,b=e.Node,u=e.tween,c=e.Vec3,w=e.Component},function(e){d=e.TowerPlayer},function(e){p=e.EventManager,m=e.GAME_EVENTS},function(e){f=e.WorkersAnimation},function(e){g=e.DYNAMIC_RANDOM_CONFIG,T=e.setRaceTimingRange,A=e.getCurrentTimingConfig,y=e.generateDynamicRaceConfig,P=e.logDynamicRaceConfig}],execute:function(){var S,v,R,D,C,N,x,M,I,Y,L,k,O,E,F,W,_,z,U,B,H;l._RF.push({},"1659aH/L1NO/LEOHX4u5qIU","TowerAnimation",void 0);var G=h.ccclass,j=h.property;e("TowerAnimation",(S=G("TowerAnimation"),v=j(b),R=j(f),D=j({tooltip:"Tower player identifier (A, P, T)"}),C=j({tooltip:"Horizontal jiggle amplitude (legacy apartment wobble)",range:[0,10,.1],slide:!0}),N=j({tooltip:"Seconds between jiggle flips (legacy apartment wobble)",range:[.01,.5,.01],slide:!0}),x=j({tooltip:"Optional smoothing window; set to 0 for instant jiggle",range:[0,.5,.01],slide:!0}),M=j({tooltip:"Strength of the speed-follow behaviour (0 = disabled)",range:[0,3,.1],slide:!0}),I=j({tooltip:"Reference vertical speed that keeps the base jiggle timing (units/sec)",range:[5,80,1],slide:!0}),Y=j({tooltip:"Smoothing used when sampling tower speed for wobble follow",range:[0,1,.05],slide:!0}),S((O=t((k=function(e){function t(){for(var t,i=arguments.length,r=new Array(i),a=0;a<i;a++)r[a]=arguments[a];return t=e.call.apply(e,[this].concat(r))||this,o(t,"towerNode",O,n(t)),o(t,"workersAnimation",E,n(t)),o(t,"towerPlayer",F,n(t)),o(t,"wobbleAmplitude",W,n(t)),o(t,"wobbleInterval",_,n(t)),o(t,"wobbleSmoothing",z,n(t)),o(t,"wobbleSpeedFollowStrength",U,n(t)),o(t,"wobbleSpeedReference",B,n(t)),o(t,"wobbleSpeedSmoothing",H,n(t)),t.isRacing=!1,t.activeTween=null,t.finalPosition=1,t.raceStartTime=0,t.currentPhase="phase1",t.currentY=g.RESET_Y,t.wobbleActive=!1,t.wobbleBaseX=0,t.wobbleOffset=0,t.wobbleTargetOffset=0,t.wobbleDirection=1,t.wobbleTimer=0,t.wobbleCurrentAmplitude=0,t.wobbleCurrentInterval=.02,t.wobbleSmoothedSpeed=0,t.wobbleSampledY=0,t.dynamicRaceConfig=null,t.currentActionIndex=0,t.raceDurationMultiplier=1,t.plannedDurationMs=null,t.countdownTween=null,t}i(t,e);var l=t.prototype;return l.debugLog=function(){var e;g.PERFORMANCE.ENABLE_DEBUG_LOGGING&&(e=console).log.apply(e,arguments)},l.onLoad=function(){this.debugLog("[TowerAnimation] "+this.towerPlayer+" onLoad() called - FORCING timing configuration"),T(8,10),this.towerNode&&this.resetPosition()},l.resetPosition=function(){this.towerNode&&(this.cancelCountdownTween(),this.currentY=g.RESET_Y,this.towerNode.setPosition(this.towerNode.position.x,this.currentY,this.towerNode.position.z),this.currentPhase="phase1",this.isRacing=!1,this.wobbleBaseX=this.towerNode.position.x,this.wobbleOffset=0,this.wobbleTargetOffset=0,this.wobbleTimer=0,this.wobbleDirection=-1,this.wobbleCurrentAmplitude=this.wobbleAmplitude,this.wobbleCurrentInterval=Math.max(.005,this.wobbleInterval),this.wobbleSmoothedSpeed=0,this.wobbleSampledY=this.currentY,this.applyTowerWobble(!0),this.debugLog("[TowerAnimation] "+this.towerPlayer+" reset to Y="+this.currentY))},l.cancelCountdownTween=function(){this.countdownTween&&(this.countdownTween.stop(),this.countdownTween=null)},l.animateToStartPosition=function(e,t){var i=this;if(this.towerNode&&!this.isRacing){var o=this.resolveCountdownTweenDuration(e,t);if(0!==o){var n=g.START_Y,r=this.towerNode.getPosition();Math.abs(r.y-n)<=.01?this.currentY=n:(this.cancelCountdownTween(),this.countdownTween=u(this.towerNode).to(o,{position:new c(r.x,n,r.z)},{easing:"sineOut",onUpdate:function(){i.currentY=i.towerNode.position.y,i.wobbleSampledY=i.currentY}}).call((function(){i.currentY=n,i.wobbleSampledY=i.currentY,i.countdownTween=null})).start())}else this.snapToStartPosition()}},l.snapToStartPosition=function(){this.towerNode&&(this.cancelCountdownTween(),this.currentY=g.START_Y,this.towerNode.setPosition(this.towerNode.position.x,this.currentY,this.towerNode.position.z),this.wobbleSampledY=this.currentY,this.applyTowerWobble(!0))},l.resolveCountdownTweenDuration=function(e,t){var i=Math.max(0,e);return"number"==typeof t&&t>=0?t:i>3?3:1},l.setTimingRange=function(e,t){T(e,t),this.debugLog("[TowerAnimation] "+this.towerPlayer+" timing range set to "+e+"-"+t+"s")},l.getAnimationSystemInfo=function(){var e={towerPlayer:this.towerPlayer,isRacing:this.isRacing,currentPhase:this.currentPhase,finalPosition:this.finalPosition,currentY:this.currentY,timingConfig:A(),isDynamic:!0};if(this.dynamicRaceConfig)return r({},e,{phaseCount:this.dynamicRaceConfig.phases.length,actionCount:this.dynamicRaceConfig.smallActions.length,totalDuration:this.dynamicRaceConfig.totalDuration});var t=A();return r({},e,{phaseCount:0,actionCount:0,totalDuration:t.baseDuration})},l.startRace=function(){var e=a(s().mark((function e(t,i,o){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===i&&(i=1),!this.isRacing){e.next=4;break}return console.warn("[TowerAnimation] "+this.towerPlayer+" already racing"),e.abrupt("return");case 4:return this.stopRace(),this.resetPosition(),this.snapToStartPosition(),this.finalPosition=t,this.raceDurationMultiplier=i,this.plannedDurationMs=o||null,this.isRacing=!0,this.raceStartTime=Date.now(),this.currentPhase="phase1",console.log("[TowerAnimation] "+this.towerPlayer+" RACE START:"),console.log("  Target Duration: "+(void 0!==o?(o/1e3).toFixed(3)+"s":"undefined")),console.log("  Stored Planned Duration: "+(null!==this.plannedDurationMs?(this.plannedDurationMs/1e3).toFixed(3)+"s":"NULL")),console.log("  Position: "+t),null!==this.plannedDurationMs?(console.log("  STATUS: Will use planned duration for timing consistency"),this.debugLog("[TowerAnimation] "+this.towerPlayer+" PLANNED DURATION SET: "+(this.plannedDurationMs/1e3).toFixed(3)+"s for position "+t)):(console.log("  STATUS: Will use measured time (NO PLANNED DURATION)"),this.debugLog("[TowerAnimation] "+this.towerPlayer+" NO PLANNED DURATION - will use measured time for position "+t)),this.debugLog("[TowerAnimation] "+this.towerPlayer+" starting race to position "+t+" with duration multiplier "+i+"x"),this.startTowerWobble(),this.workersAnimation&&(this.workersAnimation.startRacingMode(),this.workersAnimation.updateWorkersByHeight(this.currentY),this.workersAnimation.setAnimationSpeed(1)),this.debugLog("[TowerAnimation] "+this.towerPlayer+" starting dynamic race to position "+t),e.next=24,this.executeDynamicPhaseRace(o);case 24:case"end":return e.stop()}}),e,this)})));return function(t,i,o){return e.apply(this,arguments)}}(),l.stopRace=function(){this.activeTween&&(this.activeTween.stop(),this.activeTween=null),this.cancelCountdownTween(),this.isRacing=!1,this.currentPhase="complete",this.stopTowerWobble(),this.debugLog("[TowerAnimation] "+this.towerPlayer+" race stopped")},l.executeDynamicPhaseRace=function(){var e=a(s().mark((function e(t){var i=this;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,o){try{i.debugLog("[TowerAnimation] "+i.towerPlayer+" executeDynamicPhaseRace() called for position "+i.finalPosition);var n=t?{totalDuration:t}:void 0;i.dynamicRaceConfig=y(i.finalPosition,n);var r=i.dynamicRaceConfig.totalDuration;n||1===i.raceDurationMultiplier?i.debugLog("[TowerAnimation] "+i.towerPlayer+" race duration set to "+(i.dynamicRaceConfig.totalDuration/1e3).toFixed(2)+"s"):(i.dynamicRaceConfig.totalDuration*=i.raceDurationMultiplier,i.debugLog("[TowerAnimation] "+i.towerPlayer+" applied duration multiplier "+i.raceDurationMultiplier+"x: "+(r/1e3).toFixed(2)+"s → "+(i.dynamicRaceConfig.totalDuration/1e3).toFixed(2)+"s")),i.dynamicRaceConfig.totalDuration<8e3&&console.error("[TowerAnimation] "+i.towerPlayer+" ERROR: Duration too short! "+(i.dynamicRaceConfig.totalDuration/1e3).toFixed(2)+"s - something is overriding the timing!"),i.currentActionIndex=0,P(i.dynamicRaceConfig,i.towerPlayer),i.activeTween=i.buildDynamicTweenChain(e),i.activeTween.start()}catch(e){console.error("[TowerAnimation] "+i.towerPlayer+" dynamic race execution failed:",e),i.stopRace(),o(e)}})));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),l.buildDynamicTweenChain=function(e){for(var t=this,i=u(this.towerNode),o=g.START_Y,n=this.dynamicRaceConfig.smallActions,r=g.FINISH_Y,a=this.towerNode?this.towerNode.getPosition():new c,s=function(){var e=n[l];e.delayBefore>0&&(i=i.delay(e.delayBefore/1e3));var s=g.TOTAL_DISTANCE*t.dynamicRaceConfig.phases[e.phaseId].distancePercent*e.distancePercent,h=Math.max(0,r-o),b=Math.min(Math.max(s,0),h),u=o+b;l===n.length-1&&(u=r),u=Math.min(u,r);var w=e.duration/1e3;t.debugLog("[TowerAnimation] "+t.towerPlayer+" Action "+e.actionId+": "+w.toFixed(3)+"s ("+e.duration+"ms)");var d=u;if(i=i.to(w,{position:new c(a.x,d,a.z)},{easing:e.easing,onUpdate:function(){t.applyTowerWobble()}}).call((function(){t.onSmallActionComplete(e,u)})),o=u,e.hasMicroPause){var p,m=null!=(p=e.microPauseDuration)?p:0;m>0&&(i=i.delay(m/1e3))}},l=0;l<n.length;l++)s();return i=i.call((function(){t.onRaceComplete(),e()}))},l.onSmallActionComplete=function(e,t){if(this.currentY=t,this.currentActionIndex++,this.workersAnimation&&this.workersAnimation.updateWorkersByHeight(t),e.hasWorkerReveal&&this.debugLog("[TowerAnimation] "+this.towerPlayer+" Worker reveal at action "+e.actionId+", Y="+t.toFixed(1)),this.workersAnimation){var i=1;switch(e.effectType){case"hesitation":i=.6;break;case"burst":i=1.4;break;case"fatigue":i=.8;break;default:i=1}this.workersAnimation.setAnimationSpeed(i)}this.debugLog("[TowerAnimation] "+this.towerPlayer+" Action "+e.actionId+" complete at Y="+t.toFixed(1)+", effect="+e.effectType)},l.onPhase1Complete=function(e){this.currentPhase="phase2",this.currentY=e,this.workersAnimation&&this.workersAnimation.updateWorkersByHeight(e),this.workersAnimation&&this.workersAnimation.setAnimationSpeed(.6),this.debugLog("[TowerAnimation] "+this.towerPlayer+" Phase 1 complete at Y="+e.toFixed(1))},l.onPhase2Complete=function(e){this.currentPhase="phase3",this.currentY=e,this.workersAnimation&&this.workersAnimation.updateWorkersByHeight(e),this.workersAnimation&&this.workersAnimation.setAnimationSpeed(1.4),this.debugLog("[TowerAnimation] "+this.towerPlayer+" Phase 2 complete at Y="+e.toFixed(1))},l.onRaceComplete=function(){var e;this.currentPhase="complete",this.currentY=g.FINISH_Y,this.isRacing=!1,this.activeTween=null,this.stopTowerWobble(),this.workersAnimation&&this.workersAnimation.updateWorkersByHeight(g.FINISH_Y),this.workersAnimation&&this.workersAnimation.setAnimationSpeed(1);var t=(Date.now()-this.raceStartTime)/1e3;console.log("[TowerAnimation] "+this.towerPlayer+" RACE COMPLETE ANALYSIS:"),console.log("  Position: "+this.finalPosition),console.log("  Planned Duration: "+(null!==this.plannedDurationMs?(this.plannedDurationMs/1e3).toFixed(3)+"s":"NULL")),console.log("  Measured Time: "+t.toFixed(3)+"s"),null!==this.plannedDurationMs?(e=this.plannedDurationMs/1e3,console.log("  USING PLANNED TIME: "+e.toFixed(3)+"s"),this.debugLog("[TowerAnimation] "+this.towerPlayer+" Race complete using planned time "+e.toFixed(2)+"s (measured: "+t.toFixed(2)+"s) at position "+this.finalPosition)):(e=t,console.log("  USING MEASURED TIME: "+e.toFixed(3)+"s (NO PLANNED DURATION)"),this.debugLog("[TowerAnimation] "+this.towerPlayer+" Race complete using measured time "+e.toFixed(2)+"s at position "+this.finalPosition)),p.instance.emit(m.TOWER_INDIVIDUAL_COMPLETE,{player:this.towerPlayer,finalPosition:this.finalPosition,raceTime:e})},l.startTowerWobble=function(){if(this.towerNode){var e=this.towerNode.getPosition();this.wobbleBaseX=e.x,this.wobbleOffset=0,this.wobbleDirection=1,this.wobbleTimer=0,this.wobbleCurrentAmplitude=this.wobbleAmplitude,this.wobbleCurrentInterval=Math.max(.005,this.wobbleInterval),this.wobbleTargetOffset=this.wobbleCurrentAmplitude,this.wobbleActive=this.wobbleAmplitude>0&&this.wobbleInterval>0,this.wobbleSmoothedSpeed=0,this.wobbleSampledY=e.y,this.wobbleActive&&this.wobbleSmoothing<=1e-4?(this.wobbleOffset=this.wobbleCurrentAmplitude,this.applyTowerWobble(!0)):this.applyTowerWobble(!0)}},l.stopTowerWobble=function(){this.towerNode&&(this.wobbleActive=!1,this.wobbleOffset=0,this.wobbleTargetOffset=0,this.wobbleCurrentAmplitude=this.wobbleAmplitude,this.wobbleCurrentInterval=Math.max(.005,this.wobbleInterval),this.wobbleSmoothedSpeed=0,this.applyTowerWobble(!0))},l.applyTowerWobble=function(e){if(void 0===e&&(e=!1),this.towerNode){var t=this.towerNode.getPosition(),i=this.wobbleBaseX+(this.wobbleActive?this.wobbleOffset:0);(e||Math.abs(t.x-i)>.001)&&this.towerNode.setPosition(i,t.y,t.z)}},l.update=function(e){if(this.wobbleActive&&this.towerNode){this.updateWobbleSpeedFollow(e),this.wobbleTimer+=e;var t=this.wobbleCurrentInterval;if(this.wobbleSmoothing<=1e-4){var i=!1;if(t>0)for(;this.wobbleTimer>=t;)this.wobbleTimer-=t,this.wobbleDirection*=-1,i=!0;return this.wobbleOffset=this.wobbleCurrentAmplitude*this.wobbleDirection,void this.applyTowerWobble(i)}var o=!1;if(t>0)for(;this.wobbleTimer>=t;)this.wobbleTimer-=t,this.wobbleDirection*=-1,this.wobbleTargetOffset=this.wobbleCurrentAmplitude*this.wobbleDirection,o=!0;var n=Math.max(1e-4,this.wobbleSmoothing),r=Math.min(1,e/n);this.wobbleOffset=this.wobbleOffset+(this.wobbleTargetOffset-this.wobbleOffset)*r,this.applyTowerWobble(o)}},l.updateWobbleSpeedFollow=function(e){if(this.towerNode&&!(e<=0)){var t=this.towerNode.getPosition().y,i=t-this.wobbleSampledY;this.wobbleSampledY=t;var o=Math.abs(i)/e;if(Number.isFinite(o)){var n=Math.min(Math.max(this.wobbleSpeedSmoothing,0),1);if(this.wobbleSmoothedSpeed=this.wobbleSmoothedSpeed+(o-this.wobbleSmoothedSpeed)*n,this.wobbleSpeedFollowStrength<=0||this.wobbleSpeedReference<=0)return this.wobbleCurrentAmplitude=this.wobbleAmplitude,void(this.wobbleCurrentInterval=Math.max(.005,this.wobbleInterval));var r=Math.min(this.wobbleSmoothedSpeed/this.wobbleSpeedReference,5),a=1/(1+r*this.wobbleSpeedFollowStrength);this.wobbleCurrentInterval=Math.max(.005,this.wobbleInterval*a);var s=1+r*(.4*this.wobbleSpeedFollowStrength);this.wobbleCurrentAmplitude=Math.min(this.wobbleAmplitude*s,3*this.wobbleAmplitude),this.wobbleSmoothing>0&&(this.wobbleTargetOffset=this.wobbleCurrentAmplitude*this.wobbleDirection)}}},l.getAnimationState=function(){return{isRacing:this.isRacing,currentPhase:this.currentPhase,currentY:this.currentY,finalPosition:this.finalPosition}},l.setTowerPlayer=function(e){this.towerPlayer=e},l.onDestroy=function(){this.stopRace()},t}(w)).prototype,"towerNode",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=t(k.prototype,"workersAnimation",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=t(k.prototype,"towerPlayer",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return d.A}}),W=t(k.prototype,"wobbleAmplitude",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1}}),_=t(k.prototype,"wobbleInterval",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.02}}),z=t(k.prototype,"wobbleSmoothing",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),U=t(k.prototype,"wobbleSpeedFollowStrength",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1.2}}),B=t(k.prototype,"wobbleSpeedReference",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 24}}),H=t(k.prototype,"wobbleSpeedSmoothing",[Y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.25}}),L=k))||L));l._RF.pop()}}}));

System.register("chunks:///_virtual/TowerAnimationController.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GameEvents.ts","./EventManager.ts","./TowerAnimation.ts","./DynamicRandomPhaseConfig.ts","./PopupManager.ts"],(function(t){var o,i,n,e,r,a,s,l,u,c,d,m,h,p,g,w,f,A,T,P,b,R;return{setters:[function(t){o=t.applyDecoratedDescriptor,i=t.inheritsLoose,n=t.initializerDefineProperty,e=t.assertThisInitialized,r=t.createForOfIteratorHelperLoose,a=t.asyncToGenerator,s=t.regeneratorRuntime},function(t){l=t.cclegacy,u=t._decorator,c=t.UIOpacity,d=t.Node,m=t.tween,h=t.Vec3,p=t.Component},function(t){g=t.TowerPlayer},function(t){w=t.EventManager,f=t.GAME_EVENTS},function(t){A=t.TowerAnimation},function(t){T=t.DYNAMIC_RANDOM_CONFIG,P=t.setRaceTimingRange,b=t.getCurrentTimingConfig},function(t){R=t.PopupManager}],execute:function(){var y,C,D,L,v,E,N,M,S,I,x,O,_,U,G,F,z,Y,B,H,k,V,W,j,J;l._RF.push({},"ef38dYr9fJGX4ILluY1eKgY","TowerAnimationController",void 0);var q=u.ccclass,K=u.property;t("TowerAnimationController",(y=q("TowerAnimationController"),C=K({tooltip:"Enable TowerAnimation system for 3-phase racing"}),D=K(A),L=K(A),v=K(A),E=K(c),N=K(d),M=K(d),S=K({tooltip:"Cloud starting Y position (moves down during race for parallax effect)"}),I=K({tooltip:"Cloud ending Y position"}),x=K({tooltip:"Enable cloud background animation during racing"}),O=K({tooltip:"Global race duration multiplier (for testing)"}),y((G=o((U=function(t){function o(){for(var o,i=arguments.length,r=new Array(i),a=0;a<i;a++)r[a]=arguments[a];return o=t.call.apply(t,[this].concat(r))||this,n(o,"enableTowerAnimations",G,e(o)),n(o,"towerAnimationA",F,e(o)),n(o,"towerAnimationP",z,e(o)),n(o,"towerAnimationT",Y,e(o)),n(o,"finalLineUIOpacity",B,e(o)),n(o,"cloudNode",H,e(o)),n(o,"confettiNode",k,e(o)),n(o,"cloudStartY",V,e(o)),n(o,"cloudEndY",W,e(o)),n(o,"enableCloudAnimation",j,e(o)),n(o,"raceDurationMultiplier",J,e(o)),o.firstPlayerOfRound=!1,o.isRacing=!1,o.raceStartTime=0,o.completedTowers=new Set,o.raceResults=[],o.countdownRaiseActive=!1,o.cloudTween=null,o}i(o,t);var l=o.prototype;return l.debugLog=function(){var t;T.PERFORMANCE.ENABLE_DEBUG_LOGGING&&(t=console).log.apply(t,arguments)},l.onLoad=function(){this.debugLog("[TowerAnimationController] onLoad() called - initializing controller"),this.setupTowerPlayers(),this.setupEventListeners(),this.setupTimingConfiguration(),this.debugLog("[TowerAnimationController] onLoad() completed")},l.setupTowerPlayers=function(){this.towerAnimationA&&this.towerAnimationA.setTowerPlayer(g.A),this.towerAnimationP&&this.towerAnimationP.setTowerPlayer(g.P),this.towerAnimationT&&this.towerAnimationT.setTowerPlayer(g.T),this.debugLog("[TowerAnimationController] Tower players configured")},l.setupEventListeners=function(){w.instance.on(f.TOWER_INDIVIDUAL_COMPLETE,this.handleTowerComplete,this),w.instance.on(f.STARTUP_POPUP_HIDDEN,this.onStartupPopupHidden,this),w.instance.on(f.GAME_RESET_UI,this.onGameResetUI,this),w.instance.on(f.ROUND_COUNTDOWN_STARTED,this.onRoundCountdownStarted,this),this.debugLog("[TowerAnimationController] Event listeners setup - listening for STARTUP_POPUP_HIDDEN, TOWER_INDIVIDUAL_COMPLETE, GAME_RESET_UI, and ROUND_COUNTDOWN_STARTED")},l.setupTimingConfiguration=function(){P(8,10);var t=b();this.debugLog("[TowerAnimationController] Timing configuration set to 8-10 second race duration"),this.debugLog("[TowerAnimationController] Current timing config:",{minDuration:t.minDuration/1e3+"s",maxDuration:t.maxDuration/1e3+"s",baseDuration:t.baseDuration/1e3+"s"}),this.debugLog("[TowerAnimationController] Race duration multiplier: "+this.raceDurationMultiplier+"x")},l.finalLineAnimationRound=function(){this.firstPlayerOfRound||(this.firstPlayerOfRound=!0,m(this.finalLineUIOpacity).to(.2,{opacity:255}).to(.2,{opacity:0}).union().start())},l.planRaceDurations=function(t){var o=this,i=new Map;if(!t||0===t.length)return i;var n=b(),e=[].concat(t).sort((function(t,o){return t.finalPosition-o.finalPosition})),a=420,s=500,l=Math.min(260,Math.max(0,n.maxDuration-n.minDuration-920)),u=n.minDuration+this.randomBetween(0,l),c=u+this.randomBetween(a,720);c>n.maxDuration-s&&(c=n.maxDuration-s);var d=(c=Math.max(c,u+a))+this.randomBetween(s,820);d=Math.min(d,n.maxDuration),d=Math.max(d,c+s);var m=Math.min(60,.5*l);u=this.applyJitter(u,n.minDuration,c-140,m),c=this.applyJitter(c,u+a,d-140,90),d=this.applyJitter(d,c+s,n.maxDuration,110),c=Math.max(c,u+a),d=Math.max(d,c+s),(d=Math.min(d,n.maxDuration))<c+s&&(d=Math.min(n.maxDuration,c+s));var h=new Map([[1,Math.round(u)],[2,Math.round(c)],[3,Math.round(d)]]);this.debugLog("[TowerAnimationController] Duration plan (ms):",{first:h.get(1),second:h.get(2),third:h.get(3)}),console.log("[TowerAnimationController] DETAILED DURATION ASSIGNMENT:");for(var p,g=r(e);!(p=g()).done;){var w=p.value,f=h.get(w.finalPosition);void 0!==f?(i.set(w.player,f),console.log("[TowerAnimationController] "+w.player+" (Position "+w.finalPosition+") → "+(f/1e3).toFixed(3)+"s"),this.debugLog("[TowerAnimationController] "+w.player+" (Position "+w.finalPosition+") → "+(f/1e3).toFixed(3)+"s")):(console.log("[TowerAnimationController] ERROR: No duration found for "+w.player+" at position "+w.finalPosition),this.debugLog("[TowerAnimationController] ERROR: No duration found for "+w.player+" at position "+w.finalPosition))}var A=Array.from(i.entries()).sort((function(t,o){return t[1]-o[1]}));return this.debugLog("[TowerAnimationController] DURATION ORDER VERIFICATION:"),A.forEach((function(t,i){var n,r=t[0],a=t[1],s=i+1,l=null==(n=e.find((function(t){return t.player===r})))?void 0:n.finalPosition,u=l===s;o.debugLog("[TowerAnimationController] "+(i+1)+". "+r+" ("+(a/1e3).toFixed(3)+"s) → Position "+l+" "+(u?"✅":"❌"))})),i},l.validateDurationPlan=function(t,o){if(t&&0!==t.size&&o&&0!==o.length){for(var i,n=o.map((function(o){var i=t.get(o.player);return{player:o.player,finalPosition:o.finalPosition,plannedDurationMs:i,plannedDurationSec:"number"==typeof i?(i/1e3).toFixed(2):"n/a"}})).sort((function(t,o){return t.finalPosition-o.finalPosition})),e=!0,a=!1,s=null,l=null,u=r(n);!(i=u()).done;){var c=i.value;"number"==typeof c.plannedDurationMs&&(s=null===s?c.plannedDurationMs:Math.min(s,c.plannedDurationMs),l=null===l?c.plannedDurationMs:Math.max(l,c.plannedDurationMs))}for(var d=1;d<n.length;d++){var m=n[d-1],h=n[d];"number"==typeof m.plannedDurationMs&&"number"==typeof h.plannedDurationMs&&(h.plannedDurationMs<m.plannedDurationMs&&(e=!1),h.plannedDurationMs-m.plannedDurationMs>40&&(a=!0))}if(T.PERFORMANCE.ENABLE_DEBUG_LOGGING){var p=null!==l&&null!==s?((l-s)/1e3).toFixed(2):"n/a";this.debugLog("[TowerAnimationController] Duration plan breakdown:",n),this.debugLog("[TowerAnimationController] Duration plan analysis summary:",{orderValid:e,hasSeparation:a,spreadSeconds:p})}e||console.warn("[TowerAnimationController] Duration plan order mismatch detected - verify race result sequencing before run",n)}else this.debugLog("[TowerAnimationController] Duration plan empty - relying on dynamic race timings only")},l.randomBetween=function(t,o){return Number.isFinite(t)&&Number.isFinite(o)?o<=t?t:t+Math.random()*(o-t):t},l.clamp=function(t,o,i){return Math.min(Math.max(t,o),i)},l.applyJitter=function(t,o,i,n){if(!Number.isFinite(t))return t;if(i<=o)return o;var e=t+(Math.random()-.5)*n*2;return this.clamp(e,o,i)},l.onGameResetUI=function(){this.debugLog("[TowerAnimationController] GAME_RESET_UI received - resetting all towers for next round"),this.confettiNode.position=new h(0,580,0),this.stopRacing(),this.resetAllTowers(),this.raceResults=[],this.isRacing=!1,this.firstPlayerOfRound=!1,this.countdownRaiseActive=!1,this.debugLog("[TowerAnimationController] Game reset complete - towers ready for next round")},l.onRoundCountdownStarted=function(t){if(!this.isRacing){var o=this.normalizeCountdownPayload(t);if(o){var i=o.countdownSeconds,n=o.tweenDuration;!Number.isFinite(n)||n<=0?this.prepareTowersForRace():(this.countdownRaiseActive=!0,this.towerAnimationA&&this.towerAnimationA.animateToStartPosition(i,n),this.towerAnimationP&&this.towerAnimationP.animateToStartPosition(i,n),this.towerAnimationT&&this.towerAnimationT.animateToStartPosition(i,n))}else this.prepareTowersForRace()}},l.normalizeCountdownPayload=function(t){if("number"==typeof t){var o=Math.max(0,t);return{countdownSeconds:o,tweenDuration:o>3?3:1}}if(t&&"object"==typeof t){var i=Math.max(0,"number"==typeof t.countdownSeconds?t.countdownSeconds:0);return{countdownSeconds:i,tweenDuration:"number"==typeof t.tweenDuration?Math.max(0,t.tweenDuration):i>3?3:1}}return null},l.startConfetti=function(){m(this.confettiNode).to(1.5,{position:new h(0,-580,0)},{easing:"linear"}).start()},l.startRacing=function(){var t=a(s().mark((function t(o){var i,n,e,a,l,u,c,d;return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.enableTowerAnimations){t.next=3;break}return console.warn("[TowerAnimationController] Tower animations disabled"),t.abrupt("return");case 3:if(!this.isRacing){t.next=6;break}return console.warn("[TowerAnimationController] Race already in progress"),t.abrupt("return");case 6:for(this.isRacing=!0,this.raceStartTime=Date.now(),this.completedTowers.clear(),this.raceResults=[],this.debugLog("[TowerAnimationController] Starting dynamic race for all towers"),this.prepareTowersForRace(),i=this.planRaceDurations(o),this.validateDurationPlan(i,o),this.enableCloudAnimation&&this.cloudNode&&(n=this.getThirdPlaceDurationSeconds(i,o),this.startCloudAnimation(n)),e=[],a=r(o);!(l=a()).done;)u=l.value,(c=this.getTowerAnimation(u.player))&&(d=i.get(u.player),console.log("[TowerAnimationController] STARTING RACE FOR "+u.player+":"),console.log("  Position: "+u.finalPosition),console.log("  Target Duration: "+("number"==typeof d?(d/1e3).toFixed(3)+"s":"undefined")),console.log("  Duration Multiplier: "+this.raceDurationMultiplier+"x"),"number"==typeof d?this.debugLog("[TowerAnimationController] Starting race for "+u.player+" (position "+u.finalPosition+") targeting "+(d/1e3).toFixed(2)+"s"):this.debugLog("[TowerAnimationController] Starting race for "+u.player+" at position "+u.finalPosition+" with default timing plan"),e.push(c.startRace(u.finalPosition,this.raceDurationMultiplier,d)));return t.prev=17,t.next=20,Promise.all(e);case 20:this.debugLog("[TowerAnimationController] All tower animations completed via Promise.all"),t.next=27;break;case 23:t.prev=23,t.t0=t.catch(17),console.error("[TowerAnimationController] Race execution failed:",t.t0),this.stopRacing();case 27:case"end":return t.stop()}}),t,this,[[17,23]])})));return function(o){return t.apply(this,arguments)}}(),l.stopRacing=function(){this.isRacing&&(this.isRacing=!1,this.completedTowers.clear(),this.countdownRaiseActive=!1,this.towerAnimationA&&this.towerAnimationA.stopRace(),this.towerAnimationP&&this.towerAnimationP.stopRace(),this.towerAnimationT&&this.towerAnimationT.stopRace(),this.stopCloudAnimation(),this.debugLog("[TowerAnimationController] All racing stopped"))},l.resetAllTowers=function(){this.towerAnimationA&&this.towerAnimationA.resetPosition(),this.towerAnimationP&&this.towerAnimationP.resetPosition(),this.towerAnimationT&&this.towerAnimationT.resetPosition(),this.cloudNode&&this.cloudNode.setPosition(this.cloudNode.position.x,this.cloudStartY,this.cloudNode.position.z),this.countdownRaiseActive=!1,this.debugLog("[TowerAnimationController] All towers reset")},l.prepareTowersForRace=function(){this.towerAnimationA&&this.towerAnimationA.snapToStartPosition(),this.towerAnimationP&&this.towerAnimationP.snapToStartPosition(),this.towerAnimationT&&this.towerAnimationT.snapToStartPosition(),this.cloudNode&&this.cloudNode.setPosition(this.cloudNode.position.x,this.cloudStartY,this.cloudNode.position.z),this.countdownRaiseActive=!1},l.getTowerAnimation=function(t){switch(t){case g.A:return this.towerAnimationA;case g.P:return this.towerAnimationP;case g.T:return this.towerAnimationT;default:return null}},l.startCloudAnimation=function(t){var o=this;if(this.cloudNode){this.stopCloudAnimation();var i="number"==typeof t&&t>0?t:this.calculateMaxRaceDuration();(!Number.isFinite(i)||i<=0)&&(i=this.calculateMaxRaceDuration()),this.debugLog("[TowerAnimationController] Starting cloud tween animation for "+i.toFixed(2)+"s"),this.cloudTween=m(this.cloudNode).to(i,{position:new h(this.cloudNode.position.x,this.cloudEndY,this.cloudNode.position.z)},{easing:"linear"}).call((function(){o.debugLog("[TowerAnimationController] Cloud animation completed"),o.cloudTween=null})).start()}},l.getThirdPlaceDurationSeconds=function(t,o){if(!t||0===t.size)return null;var i=o.find((function(t){return 3===t.finalPosition}));if(i){var n=t.get(i.player);if("number"==typeof n&&n>0)return n/1e3}var e=0;return t.forEach((function(t){"number"==typeof t&&t>e&&(e=t)})),e>0?e/1e3:null},l.calculateMaxRaceDuration=function(){return b().maxDuration*T.TIMING_MULTIPLIERS.THIRD_PLACE/1e3},l.stopCloudAnimation=function(){this.cloudTween&&(this.cloudTween.stop(),this.cloudTween=null,this.debugLog("[TowerAnimationController] Cloud animation stopped and tween set to null"))},l.handleTowerComplete=function(t){if(t){this.finalLineAnimationRound();var o=t.player,i=t.finalPosition,n=t.raceTime;if(o&&void 0!==i){var e="number"!=typeof n||isNaN(n)?0:n;this.completedTowers.add(o),this.raceResults.push({player:o,position:i,time:e}),e!==n&&console.warn("[TowerAnimationController] Invalid raceTime ("+n+") replaced with 0 for tower "+o),this.debugLog("[TowerAnimationController] Tower "+o+" completed race in position "+i+" ("+e.toFixed(2)+"s)"),this.completedTowers.size>=3&&(this.onAllRacesComplete(),this.startConfetti())}else console.error("[TowerAnimationController] Missing tower completion data",t)}else console.error("[TowerAnimationController] Received invalid TOWER_INDIVIDUAL_COMPLETE payload")},l.onStartupPopupHidden=function(){if(this.enableTowerAnimations){var t=R.getInstance();if(t){var o=t.latestGameResult;if(o&&o.positions){var i=o.positions,n=o.round,e=o.winner,r=o.firstPlace,a=o.secondPlace,s=o.thirdPlace;this.debugLog("[TowerAnimationController] Starting race after popup hidden for round "+n+":",{winner:e,positions:i,firstPlace:r,secondPlace:a,thirdPlace:s,positionsType:Array.isArray(i)?"array":"object"});try{var l;if(Array.isArray(i)&&3===i.length){var u=i[0],c=i[1],d=i[2];l=[{player:g.A,finalPosition:u},{player:g.P,finalPosition:c},{player:g.T,finalPosition:d}],this.debugLog("[TowerAnimationController] Parsed array positions:",{A:u,P:c,T:d})}else if("object"==typeof i&&void 0!==i[g.A])l=[{player:g.A,finalPosition:i[g.A]},{player:g.P,finalPosition:i[g.P]},{player:g.T,finalPosition:i[g.T]}],this.debugLog("[TowerAnimationController] Parsed object positions:",i);else{if(!(r&&a&&s))return void console.error("[TowerAnimationController] Unable to parse positions data:",{positions:i,firstPlace:r,secondPlace:a,thirdPlace:s});l=[{player:r,finalPosition:1},{player:a,finalPosition:2},{player:s,finalPosition:3}],this.debugLog("[TowerAnimationController] Using place fields:",{firstPlace:r,secondPlace:a,thirdPlace:s})}if(!l.every((function(t){return t.finalPosition>=1&&t.finalPosition<=3})))return void console.error("[TowerAnimationController] Invalid race positions:",l);this.debugLog("[TowerAnimationController] Starting dynamic race after popup hidden:",l),this.startRacing(l)}catch(t){console.error("[TowerAnimationController] Failed to process startup popup hidden event:",t),console.error("[TowerAnimationController] Event data:",o)}}else console.warn("[TowerAnimationController] No valid game result data available from PopupManager")}else console.error("[TowerAnimationController] PopupManager instance not found")}else this.debugLog("[TowerAnimationController] Tower animations disabled - ignoring STARTUP_POPUP_HIDDEN")},l.onAllRacesComplete=function(){this.isRacing=!1;var t=(Date.now()-this.raceStartTime)/1e3;w.instance.emit(f.TOWER_ALL_RACES_COMPLETE,{results:this.raceResults,totalTime:t}),this.stopCloudAnimation(),this.raceResults.sort((function(t,o){return t.position-o.position}))},l.getRaceState=function(){return{isRacing:this.isRacing,completedTowers:Array.from(this.completedTowers),results:[].concat(this.raceResults)}},l.getAllTowerStates=function(){var t={};return this.towerAnimationA&&(t[g.A]=this.towerAnimationA.getAnimationState()),this.towerAnimationP&&(t[g.P]=this.towerAnimationP.getAnimationState()),this.towerAnimationT&&(t[g.T]=this.towerAnimationT.getAnimationState()),t},l.testRacing=function(){this.debugLog("[TowerAnimationController] Manual test racing triggered");var t=[{player:g.A,finalPosition:1},{player:g.P,finalPosition:2},{player:g.T,finalPosition:3}];this.startRacing(t)},l.debugStatus=function(){this.debugLog("[TowerAnimationController] === DEBUG STATUS ==="),this.debugLog("enableTowerAnimations:",this.enableTowerAnimations),this.debugLog("isRacing:",this.isRacing),this.debugLog("Tower components assigned:"),this.debugLog("  - towerAnimationA:",!!this.towerAnimationA),this.debugLog("  - towerAnimationP:",!!this.towerAnimationP),this.debugLog("  - towerAnimationT:",!!this.towerAnimationT),this.towerAnimationA&&(this.debugLog("  - A towerNode:",!!this.towerAnimationA.towerNode),this.debugLog("  - A isRacing:",this.towerAnimationA.isRacing)),this.towerAnimationP&&(this.debugLog("  - P towerNode:",!!this.towerAnimationP.towerNode),this.debugLog("  - P isRacing:",this.towerAnimationP.isRacing)),this.towerAnimationT&&(this.debugLog("  - T towerNode:",!!this.towerAnimationT.towerNode),this.debugLog("  - T isRacing:",this.towerAnimationT.isRacing)),this.debugLog("completedTowers:",Array.from(this.completedTowers)),this.debugLog("raceResults:",this.raceResults),this.debugLog("[TowerAnimationController] === END DEBUG ===")},l.testGameResult=function(){this.debugLog("[TowerAnimationController] Simulating startup popup hidden flow");var t={code:109,round:"TEST_ROUND",roundid:"TEST",winner:g.P,positions:[3,1,2],firstPlace:g.P,secondPlace:g.T,thirdPlace:g.A,timestamp:(new Date).toISOString()},o=R.getInstance();o?(o.latestGameResult=t,this.debugLog("[TowerAnimationController] Mock game result stored in PopupManager"),this.onStartupPopupHidden()):console.warn("[TowerAnimationController] PopupManager not available for testing")},l.testCloudAnimation=function(){this.debugLog("[TowerAnimationController] Testing cloud animation"),this.cloudNode?(this.cloudNode.setPosition(this.cloudNode.position.x,this.cloudStartY,this.cloudNode.position.z),this.startCloudAnimation(),this.debugLog("[TowerAnimationController] Cloud animation started from Y="+this.cloudStartY+" to Y="+this.cloudEndY)):console.warn("[TowerAnimationController] No cloud node assigned")},l.onDestroy=function(){this.stopRacing(),this.stopCloudAnimation(),w.instance.off(f.TOWER_INDIVIDUAL_COMPLETE,this.handleTowerComplete,this),w.instance.off(f.STARTUP_POPUP_HIDDEN,this.onStartupPopupHidden,this),w.instance.off(f.GAME_RESET_UI,this.onGameResetUI,this),w.instance.off(f.ROUND_COUNTDOWN_STARTED,this.onRoundCountdownStarted,this),this.debugLog("[TowerAnimationController] Event listeners cleaned up")},o}(p)).prototype,"enableTowerAnimations",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),F=o(U.prototype,"towerAnimationA",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=o(U.prototype,"towerAnimationP",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Y=o(U.prototype,"towerAnimationT",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=o(U.prototype,"finalLineUIOpacity",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=o(U.prototype,"cloudNode",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),k=o(U.prototype,"confettiNode",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),V=o(U.prototype,"cloudStartY",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),W=o(U.prototype,"cloudEndY",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 300}}),j=o(U.prototype,"enableCloudAnimation",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),J=o(U.prototype,"raceDurationMultiplier",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1}}),_=U))||_));l._RF.pop()}}}));

System.register("chunks:///_virtual/WebCommunicationManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./Singleton.ts"],(function(n){var e,o,t,a,i,r;return{setters:[function(n){e=n.inheritsLoose,o=n.createClass},function(n){t=n.cclegacy,a=n._decorator,i=n.Component},function(n){r=n.ccsingleton}],execute:function(){var s;t._RF.push({},"47f37Uzm/JE4Z2v4kJYwzDY","WebCommunicationManager",void 0);var c=a.ccclass;n("WebCommunicationManager",c("WebCommunicationManager")(s=r(s=function(n){function t(){return n.apply(this,arguments)||this}e(t,n);var a=t.prototype;return a.start=function(){console.log("[WebCommunicationManager] Initialized for iframe communication"),console.log("[WebCommunicationManager] Environment info:",this.getEnvironmentInfo())},a.sendCode3ResultScreen=function(){var n=(new Date).toISOString();this.sendPostMessage(3,{},n)},a.sendCode4PanelState=function(){var n=(new Date).toISOString();this.sendPostMessage(4,{},n)},a.sendPostMessage=function(n,e,o){try{if("undefined"!=typeof window&&window.parent&&window.parent!==window){var t={code:n};window.parent.postMessage(t,"*"),console.log("[WebCommunicationManager] ✅ Successfully sent Code #"+n+" to parent window"),console.log("[WebCommunicationManager] Code #"+n+" full payload:",t),console.log("[WebCommunicationManager] Code #"+n+" sent at: "+o)}else console.warn("[WebCommunicationManager] ⚠️ Not in iframe context - Code #"+n+" not sent"),console.log("[WebCommunicationManager] Code #"+n+" would have sent:",{code:n,dataKeys:Object.keys(e),timestamp:o,windowParent:"undefined"!=typeof window?!!window.parent:"no window",isIframe:!("undefined"==typeof window||!window.parent)&&window.parent!==window})}catch(t){console.error("[WebCommunicationManager] ❌ Failed to send Code #"+n+" to parent window:",t.message),console.error("[WebCommunicationManager] Code #"+n+" error details:",{errorType:t.constructor.name,errorMessage:t.message,code:n,timestamp:o,hasData:!!e}),console.log("[WebCommunicationManager] Code #"+n+" failed payload:",e)}},a.isInIframeContext=function(){try{return"undefined"!=typeof window&&window.parent&&window.parent!==window}catch(n){return console.warn("[WebCommunicationManager] Error checking iframe context:",n.message),!1}},a.getEnvironmentInfo=function(){try{return{hasWindow:"undefined"!=typeof window,hasParent:"undefined"!=typeof window&&!!window.parent,isIframe:this.isInIframeContext(),userAgent:"undefined"!=typeof navigator?navigator.userAgent:"unknown",timestamp:(new Date).toISOString()}}catch(n){return console.error("[WebCommunicationManager] Error getting environment info:",n.message),{error:n.message,timestamp:(new Date).toISOString()}}},a.onDestroy=function(){console.log("[WebCommunicationManager] Destroyed")},o(t,null,[{key:"instance",get:function(){return t.instance}}]),t}(i))||s)||s);t._RF.pop()}}}));

System.register("chunks:///_virtual/WorkerAnimationDebugger.ts",["./rollupPluginModLoBabelHelpers.js","cc","./TowerAnimationController.ts"],(function(o){var e,t,n,r,i,l,s,c;return{setters:[function(o){e=o.applyDecoratedDescriptor,t=o.inheritsLoose,n=o.initializerDefineProperty,r=o.assertThisInitialized},function(o){i=o.cclegacy,l=o._decorator,s=o.Component},function(o){c=o.TowerAnimationController}],execute:function(){var a,u,g,h,f;i._RF.push({},"3caf2MAIapKuaFMFuNsJANF","WorkerAnimationDebugger",void 0);var w=l.ccclass,p=l.property;o("WorkerAnimationDebugger",(a=w("WorkerAnimationDebugger"),u=p(c),a((f=e((h=function(o){function e(){for(var e,t=arguments.length,i=new Array(t),l=0;l<t;l++)i[l]=arguments[l];return e=o.call.apply(o,[this].concat(i))||this,n(e,"towerController",f,r(e)),e}t(e,o);var i=e.prototype;return i.onLoad=function(){this.towerController||(this.towerController=this.node.getComponentInChildren(c)||this.node.getComponentInParent(c))},i.start=function(){this.runDiagnostics()},i.runDiagnostics=function(){console.log("🔍 === WORKER ANIMATION DIAGNOSTICS ==="),this.towerController?(console.log("✅ TowerAnimationController found"),this.checkBasicSetup(),this.checkWorkerComponents(),this.testBasicFunctionality(),console.log("🔍 === END DIAGNOSTICS ===")):console.error("❌ TowerAnimationController not found! Please assign it in the inspector.")},i.checkBasicSetup=function(){console.log("\n📋 Checking basic setup...");var o=this.towerController.getRaceState();console.log("Race state:",o);var e=this.towerController.getAllTowerStates();console.log("Tower states:",e)},i.checkWorkerComponents=function(){console.log("\n🔧 Checking WorkersAnimation components..."),this.towerController.debugAllWorkers()},i.testBasicFunctionality=function(){var o=this;console.log("\n🧪 Testing basic functionality..."),console.log("Testing force show worker1..."),this.towerController.forceShowAllWorker1(),setTimeout((function(){console.log("Testing height-based reveals..."),o.towerController.testAllTowerHeights()}),1e3)},i.testWorkerVisibility=function(){console.log("🎮 Manual worker visibility test"),this.towerController.forceShowAllWorker1()},i.testHeightSystem=function(){console.log("🎮 Manual height system test"),this.towerController.testAllTowerHeights()},i.testRacing=function(){console.log("🎮 Manual racing test"),this.towerController.testRacing()},i.showDebugInfo=function(){console.log("🎮 Debug info"),this.towerController.debugAllWorkers()},e}(s)).prototype,"towerController",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),g=h))||g));i._RF.pop()}}}));

System.register("chunks:///_virtual/WorkersAnimation.ts",["./rollupPluginModLoBabelHelpers.js","cc","./DynamicRandomPhaseConfig.ts","./EventManager.ts","./GameEvents.ts"],(function(e){var i,t,n,o,r,a,s,l,h,m,d,c,p,u,f,k,v,w;return{setters:[function(e){i=e.applyDecoratedDescriptor,t=e.inheritsLoose,n=e.initializerDefineProperty,o=e.assertThisInitialized,r=e.createForOfIteratorHelperLoose},function(e){a=e.cclegacy,s=e._decorator,l=e.Animation,h=e.UIOpacity,m=e.Vec3,d=e.tween,c=e.AnimationClip,p=e.UITransform,u=e.Component},function(e){f=e.DYNAMIC_RANDOM_CONFIG},function(e){k=e.EventManager,v=e.GAME_EVENTS},function(e){w=e.TowerPlayer}],execute:function(){var A,g,W,M,_,S,y,E,T,I,R,b,C,x,P,F,O,N,D,B,L,z,V,H,G,U;a._RF.push({},"bfb31qgZFBKjL+fatEcSu/R","WorkersAnimation",void 0);var K=s.ccclass,q=s.property;e("WorkersAnimation",(A=K("WorkersAnimation"),g=q({tooltip:"Tower player identifier (A, P, T) - determines which tower these workers belong to"}),W=q({type:l,tooltip:"First worker animation (starts immediately)"}),M=q({type:l,tooltip:"Second worker animation (revealed at height 65)"}),_=q({type:l,tooltip:"Third worker animation (revealed at height 200)"}),S=q({type:l,tooltip:"Dirt animation (shown at height 200)"}),y=q(h),E=q(h),T=q({tooltip:"Enable back-and-forth worker movement"}),I=q({tooltip:"Base movement speed for workers",range:[.01,5,.001],slide:!0}),R=q({tooltip:"Enable random speed variations for workers"}),b=q({tooltip:"Use frame-based movement instead of tween-based (more stable, apartment pattern)"}),A(((U=function(e){function i(){for(var i,t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return i=e.call.apply(e,[this].concat(r))||this,n(i,"towerPlayer",P,o(i)),n(i,"worker1Animation",F,o(i)),n(i,"worker2Animation",O,o(i)),n(i,"worker3Animation",N,o(i)),n(i,"dirtAnimation",D,o(i)),n(i,"glassUIOpacity",B,o(i)),n(i,"auraUIOpacity",L,o(i)),n(i,"enableWorkerMovement",z,o(i)),n(i,"movementSpeed",V,o(i)),n(i,"enableSpeedVariation",H,o(i)),n(i,"useFrameBasedMovement",G,o(i)),i._isInitialized=!1,i._activeWorkers=new Set,i._currentMode="idle",i._lastWorkerStartTimes=new Map,i._workerStates=new Map,i._currentHeight=0,i._hasCompletedRace=!1,i._allRacesCompleteProcessed=!1,i._shouldStopWorkers=!1,i._lastGameStatus=-1,i._winLoseAnimationTriggered=!1,i._inWinLoseMode=!1,i._worker1Movement=null,i._worker2Movement=null,i._worker3Movement=null,i._movementInitialized=!1,i._originalWorkerPositions=new Map,i.glassTween=null,i.auraTween=null,i}t(i,e);var a=i.prototype;return a.onLoad=function(){this.initializeWorkers(),this.initializeWorkerStates(),this.setupEventListeners(),this._isInitialized=!0},a.onDestroy=function(){this.cleanup()},a.initializeWorkers=function(){try{this.captureOriginalWorkerPositions(),this.showAndStartWorker(this.worker1Animation,"worker1")||this.setWorkerToRestPose(this.worker1Animation,"worker1"),this.hideWorker(this.worker2Animation),this.hideWorker(this.worker3Animation),this.hideWorker(this.dirtAnimation),this.enableWorkerMovement&&this.initializeMovementState(),this.restoreAllWorkersToOriginalPositions(),this.forceAllAnimationsToPingPong()}catch(e){this._isInitialized=!1}},a.initializeWorkerStates=function(){var e,i,t=f.START_Y,n=f.WORKER_INTEGRATION.HEIGHT_THRESHOLDS.WORKER2_HEIGHT,o=f.WORKER_INTEGRATION.HEIGHT_THRESHOLDS.WORKER3_HEIGHT,r=f.WORKER_INTEGRATION.HEIGHT_THRESHOLDS.DIRT_HEIGHT,a=Number.isFinite(t)?t:0,s=Number.isFinite(n)?n:a,l=Number.isFinite(o)?o:s,h=Number.isFinite(r)?r:l,m=null!=(e=null==(i=this.worker1Animation)||null==(i=i.node)?void 0:i.active)&&e,d=this._activeWorkers.has(this.worker1Animation),c=m||this._currentHeight>=a;this._workerStates.set("worker1",{id:"worker1",animation:this.worker1Animation,movement:null,heightThreshold:a,isVisible:m,isActive:d,shouldBeVisible:c}),this._workerStates.set("worker2",{id:"worker2",animation:this.worker2Animation,movement:null,heightThreshold:s,isVisible:!1,isActive:!1,shouldBeVisible:!1}),this._workerStates.set("worker3",{id:"worker3",animation:this.worker3Animation,movement:null,heightThreshold:l,isVisible:!1,isActive:!1,shouldBeVisible:!1}),this._workerStates.set("dirt",{id:"dirt",animation:this.dirtAnimation,movement:null,heightThreshold:h,isVisible:!1,isActive:!1,shouldBeVisible:!1})},a.setupEventListeners=function(){k.instance.on(v.TOWER_ALL_RACES_COMPLETE,this.onAllRacesComplete,this),k.instance.on(v.TOWER_INDIVIDUAL_COMPLETE,this.onTowerIndividualComplete,this),k.instance.on(v.GAME_RESET_UI,this.onGameResetUI,this)},a.cleanup=function(){k.instance.off(v.TOWER_ALL_RACES_COMPLETE,this.onAllRacesComplete,this),k.instance.off(v.TOWER_INDIVIDUAL_COMPLETE,this.onTowerIndividualComplete,this),k.instance.off(v.GAME_RESULT,this.onRaceResult,this),k.instance.off(v.GAME_RESET_UI,this.onGameResetUI,this),this.stopAllWorkers(),this.stopAllMovement()},a.startRacingMode=function(){if(this._isInitialized){this._currentMode;this.stopAllWorkers(),this._currentMode="racing",this.restoreAllWorkersToOriginalPositions(["worker1"]),this.resetWorkerMovementState("worker1"),this._hasCompletedRace=!1,this._allRacesCompleteProcessed=!1,this._shouldStopWorkers=!1,this._lastGameStatus=-1,this._winLoseAnimationTriggered=!1,this._inWinLoseMode=!1,this.showAndStartWorker(this.worker1Animation,"worker1"),this.ensureMinimumWorkerVisible("racing mode"),this.startAllWorkerMovement()}},a.startJiggleMode=function(){if(this._isInitialized){this._currentMode;this.stopAllWorkers(),this._currentMode="jiggle",this.restoreAllWorkersToOriginalPositions(),this.showAndStartWorker(this.worker1Animation,"worker1"),this.ensureMinimumWorkerVisible("jiggle mode"),this.startAllWorkerMovement()}},a.startIdleMode=function(){this._isInitialized&&"idle"!==this._currentMode&&(this._currentMode="idle",this.stopAllWorkers(),this.restoreAllWorkersToOriginalPositions(),f.WORKER_INTEGRATION.USE_HEIGHT_BASED_REVEALS&&this.startIdleModeWithHeightLogic())},a.startIdleModeWithHeightLogic=function(){if(this._currentHeight<=0)this.startIdleModeShowAll();else{this.updateWorkerVisibilityStates(),this.applyWorkerStates();Array.from(this._workerStates.values()).filter((function(e){return e.isVisible})).length}},a.startIdleModeShowAll=function(){this.showAndStartWorker(this.worker1Animation,"worker1")||this.setWorkerToRestPose(this.worker1Animation,"worker1"),this.hideWorker(this.worker2Animation),this.hideWorker(this.worker3Animation)},a.updateWorkersByHeight=function(e){if(this._isInitialized&&f.WORKER_INTEGRATION.USE_HEIGHT_BASED_REVEALS&&!this._inWinLoseMode){this._currentHeight=e,this.updateWorkerVisibilityStates(),this.applyWorkerStates();Array.from(this._workerStates.values()).filter((function(e){return e.isVisible})).length}},a.updateWorkerVisibilityStates=function(){for(var e,i=r(this._workerStates);!(e=i()).done;){var t=e.value,n=t[0],o=t[1];if(o.shouldBeVisible=this._currentHeight>=o.heightThreshold,"dirt"===n){var a=this._workerStates.get("worker3");o.shouldBeVisible=o.shouldBeVisible&&!0===(null==a?void 0:a.shouldBeVisible)}}},a.applyWorkerStates=function(){if(!this._inWinLoseMode){for(var e,i=r(this._workerStates);!(e=i()).done;){var t=e.value,n=t[0],o=t[1];o.shouldBeVisible&&!o.isVisible&&o.animation?(this.showAndStartWorker(o.animation,n),o.isVisible=!0,o.isActive=!0):!o.shouldBeVisible&&o.isVisible&&o.animation&&(this.stopAndHideWorker(o.animation),o.isVisible=!1,o.isActive=!1)}this.startAllWorkerMovement()}},a.setAnimationSpeed=function(e){var i=Math.max(.8,Math.min(1.5,e));this._activeWorkers.forEach((function(e){if(e&&e.defaultClip)try{e.defaultClip.speed=i}catch(e){}}));var t=this.movementSpeed*i;this.updateMovementSpeed(t),this.forceFixActiveWorkersWrapMode()},a.forceFixActiveWorkersWrapMode=function(){this._activeWorkers.forEach((function(e){if(e&&e.defaultClip){var i=e.getState(e.defaultClip.name);i&&2!==i.wrapMode&&(i.wrapMode=2)}}))},a.stopTowerAndFreezeWorkers=function(){var e=this;this._activeWorkers.forEach((function(i){i&&e.freezeWorkerAtFirstFrame(i,"worker")}))},a.fixWorker1WrapMode=function(){if(this.worker1Animation){var e=this.worker1Animation;if(e.defaultClip){e.defaultClip.wrapMode=2;var i=e.getState(e.defaultClip.name);i&&(i.wrapMode=2,e.stop(),e.play(e.defaultClip.name))}}else console.error("[WorkersAnimation] ❌ worker1Animation is null")},a.testFreeze=function(){this.freezeAllWorkersAtFrame1()},a.testStopMovement=function(){this.stopAllWorkerMovement()},a.debugWorkerPositions=function(){var e=this.getActualContainerBounds(),i=[this._worker1Movement,this._worker2Movement,this._worker3Movement],t=[this.worker1Animation,this.worker2Animation,this.worker3Animation];i.forEach((function(i,n){var o;if(i&&null!=(o=t[n])&&o.node){var r=t[n].node.parent.position;i.position,r.x<e.left||(r.x,e.right)}}))},a.switchMovementSystem=function(e){this.stopAllMovement(),this.useFrameBasedMovement=e,this.startAllWorkerMovement()},a.updateMovementSpeed=function(e){var t=this,n=e||this.movementSpeed;void 0!==e&&(this.movementSpeed=e);var o=[this._worker1Movement,this._worker2Movement,this._worker3Movement],r=f.WORKER_INTEGRATION.MOVEMENT_CONFIG;o.forEach((function(e){if(e){var o,a,s,l,h=null!=(o=i.LANE_SPEED_MULTIPLIERS[e.laneIndex])?o:1,m=n*h,d=Math.max(.005,null!=(a=r.MIN_SPEED)?a:.85*m),c=Math.max(d+.001,null!=(s=r.MAX_SPEED)?s:1.25*m),p=(null==(l=r.SPEED_VARIATION)||l)&&t.enableSpeedVariation;e.baseSpeed=m,e.minSpeed=d,e.maxSpeed=c,e.speed=p?t.randomInRange(d,c):m,e.speedTimer=0,e.speedRefreshInterval=t.getSpeedRefreshIntervalSeconds()}}))},a.ensureDefaultClip=function(e,i){if(!e)return null;if(!e.defaultClip){if(!(e.clips&&e.clips.length>0))return null;e.defaultClip=e.clips[0]}return e.defaultClip&&(e.defaultClip.wrapMode=2),e.defaultClip||null},a.resetWorkerScale=function(e){e&&e.node&&e.node.setScale(new m(1,1,1))},a.captureOriginalWorkerPositions=function(){var e=this;[{id:"worker1",animation:this.worker1Animation},{id:"worker2",animation:this.worker2Animation},{id:"worker3",animation:this.worker3Animation}].forEach((function(i){var t=i.id,n=i.animation;if(n&&n.node&&n.node.parent&&!e._originalWorkerPositions.has(t)){var o=n.node.parent,r=new m(o.position.x,o.position.y,o.position.z);e._originalWorkerPositions.set(t,r)}}))},a.restoreAllWorkersToOriginalPositions=function(e){var i=this;void 0===e&&(e=[]);var t=new Set(e);[{id:"worker1",animation:this.worker1Animation},{id:"worker2",animation:this.worker2Animation},{id:"worker3",animation:this.worker3Animation}].forEach((function(e){var n=e.id,o=e.animation;t.has(n)||i.restoreWorkerToOriginalPosition(n,o)}))},a.resetWorkerMovementState=function(e,i){var t,n={worker1:{movement:this._worker1Movement,animation:this.worker1Animation},worker2:{movement:this._worker2Movement,animation:this.worker2Animation},worker3:{movement:this._worker3Movement,animation:this.worker3Animation}}[e];if(n){var o=n.movement,r=n.animation;if(o&&r&&r.node){var a=r.node.parent;if(a){o.position.x=a.position.x,o.position.y=a.position.y;var s=Math.min(Math.max(a.position.x,o.leftBoundary),o.rightBoundary);Math.abs(s-a.position.x)>.001&&(a.setPosition(new m(s,a.position.y,a.position.z)),o.position.x=s)}o.isMoving=!1;var l=i;if(!Number.isFinite(l)||0===l){var h=.5*(o.leftBoundary+o.rightBoundary),d=o.position.x;l=Number.isFinite(d)&&Number.isFinite(h)?d>=h?-1:1:o.direction>=0?1:-1}o.direction=l>=0?1:-1,o.pauseTimer=0,o.speedTimer=0;var c=Math.min(Math.max(o.baseSpeed,o.minSpeed),o.maxSpeed);o.speed=c,o.speedRefreshInterval=this.getSpeedRefreshIntervalSeconds(),o.lastDirectionChange=Date.now(),null==(t=o.activeTween)||t.stop(),o.activeTween=null,this.applyWorkerSpriteFlipping(r,o.direction)}}},a.restoreWorkerToOriginalPosition=function(e,i){if(i&&i.node&&i.node.parent){var t=this._originalWorkerPositions.get(e);if(t){i.node.parent.setPosition(t.x,t.y,t.z),this.resetWorkerScale(i);var n,o={worker1:this._worker1Movement,worker2:this._worker2Movement,worker3:this._worker3Movement}[e];if(o)o.position.x=t.x,o.position.y=t.y,o.isMoving=!1,null==(n=o.activeTween)||n.stop(),o.activeTween=null,o.direction=1}}},a.showAndStartWorker=function(e,i){var t=Date.now(),n=t-(this._lastWorkerStartTimes.get(i)||0);if(n<100)return console.warn("[WorkersAnimation] ⚠️ "+i+" called too rapidly ("+n+"ms ago) - skipping to prevent interference"),!1;if(this._lastWorkerStartTimes.set(i,t),!e||!e.node)return console.error("[WorkersAnimation] ❌ "+i+" animation component is missing node - not assigned in editor!"),!1;try{e.node.active=!0,this.resetWorkerScale(e);var o=this.ensureDefaultClip(e,i);if(!o)return!1;e.stop(),e.play(o.name);var r=e.getState(o.name);r&&(r.wrapMode=2),this._activeWorkers.add(e);var a=this._workerStates.get(i);return a&&(a.isVisible=!0,a.isActive=!0,a.shouldBeVisible=!0),!0}catch(e){return console.error("[WorkersAnimation] ❌ Failed to start "+i+" animation:"),!1}},a.setWorkerToRestPose=function(e,i){if(e&&e.node){var t=this.ensureDefaultClip(e,i);if(t){var n=t.name;e.node.active=!0,this.resetWorkerScale(e);var o=e.getState(n);o||(e.play(n),o=e.getState(n)),o?(o.time=0,o.sample(),o.stop()):e.stop();var r=this._workerStates.get(i);r&&(r.isVisible=!0,r.isActive=!1,r.shouldBeVisible=!0),this._activeWorkers.delete(e)}}},a.ensureMinimumWorkerVisible=function(e){if(this._activeWorkers.size>0)return!0;for(var i=0,t=[{animation:this.worker1Animation,name:"worker1"},{animation:this.worker2Animation,name:"worker2"},{animation:this.worker3Animation,name:"worker3"}];i<t.length;i++){var n=t[i];if(n.animation&&this.showAndStartWorker(n.animation,n.name))return!0}return console.warn("[WorkersAnimation] Unable to ensure minimum worker visibility when entering "+e),!1},a.stopAndHideWorker=function(e){if(e)try{e.stop(),this._activeWorkers.delete(e),e.node&&(e.node.active=!1)}catch(e){console.error("[WorkersAnimation] Failed to stop worker animation")}},a.hideWorker=function(e){e&&e.node&&(e.node.active=!1)},a.stopAllWorkers=function(){this._activeWorkers.forEach((function(e){e&&(e.stop(),e.node&&(e.node.active=!1))})),this._activeWorkers.clear(),this._lastWorkerStartTimes.clear(),this._hasCompletedRace=!1,this.setWorkerToRestPose(this.worker1Animation,"worker1"),this.hideWorker(this.worker2Animation),this.hideWorker(this.worker3Animation),this.hideWorker(this.dirtAnimation)},a.forceAllAnimationsToPingPong=function(){[{anim:this.worker1Animation,name:"worker1"},{anim:this.worker2Animation,name:"worker2"},{anim:this.worker3Animation,name:"worker3"},{anim:this.dirtAnimation,name:"dirt"}].forEach((function(e){var i=e.anim;e.name;if(i&&i.clips&&(i.clips.forEach((function(e,i){if(e){e.wrapMode;e.wrapMode=2}})),i.defaultClip)){i.defaultClip.wrapMode;i.defaultClip.wrapMode=2}}))},a.forceAllAnimationsToLoop=function(){[this.worker1Animation,this.worker2Animation,this.worker3Animation,this.dirtAnimation].forEach((function(e,i){e&&e.clips&&e.clips.forEach((function(e){e&&(e.wrapMode=1)}))}))},a.onAllRacesComplete=function(e){this._hasCompletedRace=!0,e&&e.results&&(this._shouldStopWorkers=!1,this.hideDirtAnimation(),this.stopIdleAnimationsForResults(e),this._allRacesCompleteProcessed=!0)},a.onTowerIndividualComplete=function(e){this.hideDirtAnimation(),e&&e.player==this.towerPlayer&&this.freezeAllWorkersAtFrame1(),this._hasCompletedRace=!0},a.onGameResetUI=function(){var e,i;this.stopAllWorkers(),this.stopAllMovement(),this._hasCompletedRace=!1,this._allRacesCompleteProcessed=!1,this._shouldStopWorkers=!1,this._lastGameStatus=-1,this._winLoseAnimationTriggered=!1,this._inWinLoseMode=!1,this._currentHeight=0,this.worker1Animation.node.setPosition(0,-5,0),this.worker2Animation.node.setPosition(0,-5,0),this.worker3Animation.node.setPosition(0,-5,0),null==(e=this.glassTween)||e.stop(),null==(i=this.auraTween)||i.stop(),this.glassUIOpacity.opacity=0,this.auraUIOpacity.opacity=0,this._workerStates.forEach((function(e){e.isVisible=!1,e.isActive=!1,e.shouldBeVisible=!1})),this._currentMode="idle",this.restoreAllWorkersToOriginalPositions(),this.showAndStartWorker(this.worker1Animation,"worker1")||this.setWorkerToRestPose(this.worker1Animation,"worker1")},a.onRaceResult=function(e){e&&e.winner?this.switchToWinLoseAnimations(e):console.warn("[WorkersAnimation] ❌ Invalid race result data for animation switching")},a.switchToWinLoseAnimations=function(e){var i=this,t=e.winner,n=this.towerPlayer===t,o=n?1:2,r=n?"win":"lose";n&&(this.glassTween=d(this.glassUIOpacity.getComponent(h)).to(.3,{opacity:255},{easing:"quadOut"}).to(.3,{opacity:0},{easing:"quadIn"}).union().repeatForever().start(),setTimeout((function(){k.instance.emit(v.SHOW_RESULT_POPUP)}),1500),this.auraTween=d(this.auraUIOpacity.getComponent(h)).to(.25,{opacity:255},{easing:"quadOut"}).to(.25,{opacity:0},{easing:"quadIn"}).union().repeatForever().start()),[{animation:this.worker1Animation,name:"worker1",position:{x:-40,y:0},scale:{x:1,y:1}},{animation:this.worker2Animation,name:"worker2",position:{x:0,y:0},scale:{x:1,y:1}},{animation:this.worker3Animation,name:"worker3",position:{x:40,y:0},scale:{x:1,y:1}}].forEach((function(e){var t=e.animation,n=e.name,a=e.position,s=e.scale;if(t)if(t.clips&&t.clips[o])if(a)if(s)try{t.node.active=!0;var l=t.clips[o];if(!l)return void console.warn("[WorkersAnimation] Skipping "+n+" - target clip missing at index "+o);l.wrapMode=c.WrapMode.Loop,t.node.scale=new m(s.x,s.y,1),t.node.parent.position=new m(a.x,a.y,0),t.play(l.name);var h=t.getState(l.name);h&&(h.wrapMode=c.WrapMode.Loop,h.repeatCount=1/0,h.play()),i._activeWorkers.add(t)}catch(e){console.error("[WorkersAnimation] Failed to switch "+n+" to "+r+" animation:")}else console.error("[WorkersAnimation] Skipping "+n+" - scale is undefined");else console.error("[WorkersAnimation] Skipping "+n+" - position is undefined");else console.warn("[WorkersAnimation] Skipping "+n+" - clips["+o+"] not available");else console.warn("[WorkersAnimation] Skipping "+n+" - animation is null")}))},a.determineWinnerFromResults=function(e){if(!e||!Array.isArray(e)||0===e.length)return console.warn("[WorkersAnimation] Invalid results data for winner determination"),null;var i=e.find((function(e){return 1===e.position}));if(i&&i.player)return i.player;var t=[].concat(e).sort((function(e,i){return e.position-i.position}));return t[0]&&t[0].player?t[0].player:(console.warn("[WorkersAnimation] Could not determine winner from results:",e),null)},a.stopIdleAnimationsForResults=function(e){var i=this;this.stopAllWorkerMovement();this._activeWorkers.forEach((function(e){if(e&&e.clips&&e.clips.length>0)try{var i=e.clips[0];if(i){e.stop();i.wrapMode;i.wrapMode=0,e.play(i.name),e.pause();e.getState(i.name);0}}catch(e){console.error("[WorkersAnimation] ❌ Failed to stop idle animation for result transition:")}})),e&&!this._winLoseAnimationTriggered?(this._winLoseAnimationTriggered=!0,setTimeout((function(){i.triggerWinLoseAnimations(e)}),100)):e&&this._winLoseAnimationTriggered},a.triggerWinLoseAnimations=function(e){var i=this.determineWinnerFromResults(e.results);i?this.switchToWinLoseAnimations({winner:i}):console.warn("[WorkersAnimation] ❌ Could not determine winner from local race results")},a.freezeAllWorkersAtFrame1=function(){this.stopAllWorkerMovement();this._activeWorkers.forEach((function(e){if(e&&e.clips&&e.clips.length>0)try{var i=e.clips[0];if(i){e.stop();i.wrapMode;i.wrapMode=0,e.play(i.name),e.pause();e.getState(i.name);0}}catch(e){console.error("[WorkersAnimation] ❌ Failed to freeze worker:")}}))},a.freezeAllWorkersAtFirstFrame=function(){this._activeWorkers.forEach((function(e){if(e&&e.clips&&e.clips.length>0)try{var i=e.clips[0];i&&(i.wrapMode=0,e.play(i.name),e.pause())}catch(e){console.error("[WorkersAnimation] Failed to freeze worker at first frame")}}))},a.freezeWorkerAtFirstFrame=function(e,i){if(e&&e.clips&&0!==e.clips.length)try{var t=e.clips[0];t&&(t.wrapMode=0,e.play(t.name),e.pause())}catch(e){console.error("[WorkersAnimation] Failed to freeze "+i+" at first frame")}else console.warn("[WorkersAnimation] Cannot freeze "+i+" - no clips available")},a.hideDirtAnimation=function(){if(this.dirtAnimation){this.stopAndHideWorker(this.dirtAnimation);var e=this._workerStates.get("dirt");e&&(e.isVisible=!1,e.isActive=!1)}},a.initializeMovementState=function(){var e=this;if(this.enableWorkerMovement&&!this._movementInitialized){var i=f.WORKER_INTEGRATION.MOVEMENT_CONFIG,t=new Map,n=[{id:"worker1",animation:this.worker1Animation,assign:function(i){e._worker1Movement=i}},{id:"worker2",animation:this.worker2Animation,assign:function(i){e._worker2Movement=i}},{id:"worker3",animation:this.worker3Animation,assign:function(i){e._worker3Movement=i}}];n.forEach((function(e){e.animation&&e.animation.node&&t.set(e.animation,t.size)}));var o=Math.max(1,t.size||1);n.forEach((function(n){if(n.animation&&n.animation.node){var r,a=null!=(r=t.get(n.animation))?r:0;n.assign(e.createWorkerMovement(n.id,n.animation,i,a,o))}else n.assign(null)})),this._movementInitialized=!0}},a.createWorkerMovement=function(e,t,n,o,r){var a,s,l,h,m;if(!t||!t.node||!t.node.parent)return null;var d=t.node.parent,c=d.getComponent(p);if(!c)return console.warn("[WorkersAnimation] Worker node missing UITransform component"),null;var u=Math.max(1,r||1),f=Math.min(Math.max(0,o||0),u-1),k=this.calculateMovementBoundaries(c,f,u),v=this._originalWorkerPositions.get(e),w=v?Math.min(Math.max(v.x,k.left),k.right):Math.min(Math.max(d.position.x,k.left),k.right),A=v?v.y:d.position.y;d.setPosition(w,A,d.position.z),this.resetWorkerScale(t);var g=(this.movementSpeed||n.DEFAULT_SPEED||.03)*(null!=(a=i.LANE_SPEED_MULTIPLIERS[f])?a:1),W=null!=(s=n.MIN_SPEED)?s:.85*g,M=null!=(l=n.MAX_SPEED)?l:1.25*g,_="worker1"===e,S=_?Math.max(.005,Math.min(W,.55*g)):Math.max(.01,W),y=_?Math.max(S+.015,Math.min(M,1.05*g)):Math.max(S+.1,M),E=(null==(h=n.SPEED_VARIATION)||h)&&this.enableSpeedVariation?this.randomInRange(S,y):g,T=this.resolveInitialWorkerSpeed(e,E,S,g),I=this.getSpeedRefreshIntervalSeconds();return this.applyWorkerSpriteFlipping(t,1),{laneIndex:f,laneCount:u,position:{x:d.position.x,y:d.position.y},direction:1,speed:T,baseSpeed:g,minSpeed:S,maxSpeed:y,isMoving:!1,leftBoundary:k.left,rightBoundary:k.right,lastDirectionChange:Date.now(),pauseTimer:_?.18:0,baseDirectionPause:Math.max(0,(null!=(m=n.DIRECTION_CHANGE_DELAY)?m:120)/1e3),speedTimer:Math.random()*I,speedRefreshInterval:I,activeTween:null}},a.resolveInitialWorkerSpeed=function(e,i,t,n){var o=Math.max(t,Math.min(i,n));if("worker1"!==e)return o;var r=Math.min(.7*n,.85*o),a=Math.max(t,.4*n);return Math.max(a,r)},a.randomInRange=function(e,i){return Number.isFinite(e)&&Number.isFinite(i)?i<=e?e:e+Math.random()*(i-e):e},a.getSpeedRefreshIntervalSeconds=function(){var e=i.SPEED_REFRESH_RANGE_SEC,t=e[0],n=e[1];return this.randomInRange(t,n)},a.calculateMovementBoundaries=function(e,i,t){var n,o=this.getActualContainerBounds(),r=e.width,a=f.WORKER_INTEGRATION.MOVEMENT_CONFIG,s=Math.max(.35*r,null!=(n=null==a?void 0:a.BOUNDARY_PADDING)?n:10),l=(o.right,o.left,o.left+s),h=o.right-s;if(l>=h){var m=.5*(o.left+o.right),d=Math.max(.25*r,10);l=m-d,h=m+d}return{left:l,right:h}},a.getActualContainerBounds=function(){try{if(this.node){var e=this.node.getComponent(p);if(e){var i=.5*e.width,t=this.node.position.x;return{left:t-i,right:t+i}}}if(this.node&&this.node.parent){var n=this.node.parent.getComponent(p);if(n){var o=.5*n.width,r=this.node.parent.position.x;return{left:r-o,right:r+o}}}return console.warn("[WorkersAnimation] Using fallback container bounds"),{left:-100,right:100}}catch(e){return console.error("[WorkersAnimation] Failed to get container bounds:"),{left:-100,right:100}}},a.startAllWorkerMovement=function(){var e=this;if(this.enableWorkerMovement&&"racing"===this._currentMode){var i=[this._worker1Movement,this._worker2Movement,this._worker3Movement],t=[this.worker1Animation,this.worker2Animation,this.worker3Animation];i.forEach((function(i,n){i&&t[n]&&e._activeWorkers.has(t[n])&&e.startWorkerMovement(i,t[n])}))}},a.stopAllWorkerMovement=function(){[this._worker1Movement,this._worker2Movement,this._worker3Movement].forEach((function(e,i){e&&(e.activeTween&&(e.activeTween.stop(),e.activeTween=null),e.isMoving=!1,e.pauseTimer=0,e.speedTimer=0)}))},a.startWorkerMovement=function(e,i){if(e)if(i)if(i.node)if(e.isMoving)console.warn("[WorkersAnimation] Worker movement already active - skipping duplicate start");else if(e.leftBoundary>=e.rightBoundary)console.error("[WorkersAnimation] Invalid movement boundaries: left="+e.leftBoundary+", right="+e.rightBoundary);else try{e.isMoving=!0,e.pauseTimer=0,e.activeTween=null,(!Number.isFinite(e.speedRefreshInterval)||e.speedRefreshInterval<=0)&&(e.speedRefreshInterval=this.getSpeedRefreshIntervalSeconds()),e.speedTimer=Math.random()*e.speedRefreshInterval*.5;var t=i.node.parent;if(t){var n=t.position,o=Math.min(Math.max(n.x,e.leftBoundary),e.rightBoundary);o!==n.x&&t.setPosition(new m(o,n.y,n.z)),e.position.x=t.position.x,e.position.y=t.position.y}this.applyWorkerSpriteFlipping(i,e.direction),this.useFrameBasedMovement||this.createMovementTween(e,i)}catch(i){console.error("[WorkersAnimation] Failed to start worker movement:"),e.isMoving=!1}else console.error("[WorkersAnimation] Cannot start worker movement - animation node is null");else console.error("[WorkersAnimation] Cannot start worker movement - animation component is null");else console.error("[WorkersAnimation] Cannot start worker movement - movement state is null")},a.applyWorkerSpriteFlipping=function(e,i){if(e&&e.node)try{i>0?e.node.setScale(new m(1,1,1)):e.node.setScale(new m(-1,1,1))}catch(e){console.error("[WorkersAnimation] Failed to apply sprite flipping:")}else console.warn("[WorkersAnimation] Cannot apply sprite flipping - missing animation or node")},a.createMovementTween=function(e,i){var t=this;if(i.node)try{var n=e.direction>0?e.rightBoundary:e.leftBoundary,o=i.node.parent.position.x,r=Math.abs(n-o);if(r<=0)return void console.warn("[WorkersAnimation] Invalid movement distance: "+r+" - skipping tween");var a=this.getActualContainerBounds();if(n<a.left-50||n>a.right+50){console.error("[WorkersAnimation] Target position "+n.toFixed(1)+" is outside safe container bounds ["+a.left.toFixed(1)+", "+a.right.toFixed(1)+"] - recalculating boundaries");var s=i.node.parent.getComponent(p);if(!s)return;var l=this.calculateMovementBoundaries(s,e.laneIndex,e.laneCount);e.leftBoundary=l.left,e.rightBoundary=l.right,n=e.direction>0?e.rightBoundary:e.leftBoundary;var h=Math.min(Math.max(o,e.leftBoundary),e.rightBoundary),c=i.node.parent.position;if(i.node.parent.setPosition(new m(h,c.y,c.z)),e.position.x=i.node.parent.position.x,(r=Math.abs(n-e.position.x))<=0)return}if(e.speed<=0)return void console.error("[WorkersAnimation] Invalid movement speed: "+e.speed+" - cannot create tween");this.applyWorkerSpriteFlipping(i,e.direction);var u=r/(1e3*e.speed),f=i.node.parent.position;e.activeTween=d(i.node.parent).to(u,{position:new m(n,f.y,f.z)}).call((function(){e.position.x=i.node.parent.position.x,e.position.y=i.node.parent.position.y,e.activeTween=null;var n=e.direction>0?-1:1;t.onDirectionFlip(e,i,n),e.isMoving&&!t.useFrameBasedMovement&&t.createMovementTween(e,i)})).start()}catch(i){console.error("[WorkersAnimation] Failed to create movement tween:"),e.isMoving=!1}else console.error("[WorkersAnimation] Cannot create movement tween - animation node is null")},a.stopAllMovement=function(){[this._worker1Movement,this._worker2Movement,this._worker3Movement].forEach((function(e){e&&(e.isMoving=!1,e.pauseTimer=0,e.speedTimer=0,e.activeTween&&(e.activeTween.stop(),e.activeTween=null))}))},a.update=function(e){var i=this;!this.enableWorkerMovement||!this.useFrameBasedMovement||e<=0||"racing"===this._currentMode&&[{movement:this._worker1Movement,animation:this.worker1Animation},{movement:this._worker2Movement,animation:this.worker2Animation},{movement:this._worker3Movement,animation:this.worker3Animation}].forEach((function(t){var n=t.movement,o=t.animation;n&&n.isMoving&&o&&o.node&&o.node.parent&&i._activeWorkers.has(o)&&i.advanceFrameMovement(n,o,e)}))},a.advanceFrameMovement=function(e,i,t){var n,o=null==(n=i.node)?void 0:n.parent;if(o)if(this.maybeRefreshWorkerSpeed(e,t),e.pauseTimer>0)e.pauseTimer=Math.max(0,e.pauseTimer-t);else{var r=1e3*t;if(Number.isFinite(r)&&!(r<=0)){var a=e.speed*r*e.direction;if(Number.isFinite(a)&&0!==a){var s=o.position,l=s.x+a;if(e.direction>0&&l>=e.rightBoundary)return l=e.rightBoundary,o.setPosition(new m(l,s.y,s.z)),e.position.x=l,e.position.y=s.y,void this.onDirectionFlip(e,i,-1);if(e.direction<0&&l<=e.leftBoundary)return l=e.leftBoundary,o.setPosition(new m(l,s.y,s.z)),e.position.x=l,e.position.y=s.y,void this.onDirectionFlip(e,i,1);o.setPosition(new m(l,s.y,s.z)),e.position.x=l,e.position.y=s.y}}}},a.onDirectionFlip=function(e,i,t){e.direction=t,e.lastDirectionChange=Date.now(),e.pauseTimer=this.computeDirectionPause(e),this.applyWorkerSpriteFlipping(i,t),this.refreshWorkerSpeed(e,!0)},a.computeDirectionPause=function(e){var t=Math.max(0,e.baseDirectionPause),n=i.DIRECTION_PAUSE_JITTER_SEC,o=n[0],r=n[1];return t+this.randomInRange(o,r)},a.maybeRefreshWorkerSpeed=function(e,i){(!Number.isFinite(e.speedRefreshInterval)||e.speedRefreshInterval<=0)&&(e.speedRefreshInterval=this.getSpeedRefreshIntervalSeconds()),e.speedTimer+=i,e.speedTimer>=e.speedRefreshInterval&&this.refreshWorkerSpeed(e,!0)},a.refreshWorkerSpeed=function(e,i){var t,n=(null==(t=f.WORKER_INTEGRATION.MOVEMENT_CONFIG.SPEED_VARIATION)||t)&&this.enableSpeedVariation;e.speed=n?this.randomInRange(e.minSpeed,e.maxSpeed):e.baseSpeed,e.speedTimer=0,i&&(e.speedRefreshInterval=this.getSpeedRefreshIntervalSeconds())},i}(u)).SPEED_REFRESH_RANGE_SEC=[2.2,3.8],U.DIRECTION_PAUSE_JITTER_SEC=[.04,.12],U.LANE_SPEED_MULTIPLIERS=[.94,1,1.08],P=i((x=U).prototype,"towerPlayer",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return w.A}}),F=i(x.prototype,"worker1Animation",[W],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=i(x.prototype,"worker2Animation",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=i(x.prototype,"worker3Animation",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=i(x.prototype,"dirtAnimation",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=i(x.prototype,"glassUIOpacity",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=i(x.prototype,"auraUIOpacity",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=i(x.prototype,"enableWorkerMovement",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),V=i(x.prototype,"movementSpeed",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.03}}),H=i(x.prototype,"enableSpeedVariation",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),G=i(x.prototype,"useFrameBasedMovement",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),C=x))||C));a._RF.pop()}}}));

System.register("chunks:///_virtual/WorkersAnimationTest.ts",["./rollupPluginModLoBabelHelpers.js","cc","./WorkersAnimation.ts"],(function(o){var t,e,n,i,s,r,a,l;return{setters:[function(o){t=o.applyDecoratedDescriptor,e=o.inheritsLoose,n=o.initializerDefineProperty,i=o.assertThisInitialized},function(o){s=o.cclegacy,r=o._decorator,a=o.Component},function(o){l=o.WorkersAnimation}],execute:function(){var c,m,u,A,d,g,h,p,k;s._RF.push({},"e987c1FrvBIuIVp8XPYaG2C","WorkersAnimationTest",void 0);var f=r.ccclass,w=r.property;o("WorkersAnimationTest",(c=f("WorkersAnimationTest"),m=w({type:l,tooltip:"Reference to WorkersAnimation component"}),u=w({tooltip:"Auto-run tests on start"}),A=w({tooltip:"Test delay between operations (seconds)"}),c((h=t((g=function(o){function t(){for(var t,e=arguments.length,s=new Array(e),r=0;r<e;r++)s[r]=arguments[r];return t=o.call.apply(o,[this].concat(s))||this,n(t,"workersAnimation",h,i(t)),n(t,"autoRunTests",p,i(t)),n(t,"testDelay",k,i(t)),t}e(t,o);var s=t.prototype;return s.start=function(){this.autoRunTests&&this.workersAnimation&&this.runAllTests()},s.runAllTests=function(){var o=this;console.log("[WorkersAnimationTest] Starting comprehensive tests..."),this.scheduleTest((function(){return o.testSystemValidation()}),1),this.scheduleTest((function(){return o.testAnimationClipAnalysis()}),2),this.scheduleTest((function(){return o.testLoopModeConfiguration()}),3),this.scheduleTest((function(){return o.testEnhancedIdleMode()}),4),this.scheduleTest((function(){return o.testDifferentWrapModes()}),5),this.scheduleTest((function(){return o.testAnimationSpeeds()}),6),this.scheduleTest((function(){return o.testFinalValidation()}),7)},s.scheduleTest=function(o,t){setTimeout(o,t*this.testDelay*1e3)},s.testSystemValidation=function(){if(console.log("\n=== Test 1: System Validation ==="),this.workersAnimation){var o=this.workersAnimation.getSystemStatus();console.log("System Status:",o);var t=this.workersAnimation.validateAndFixAnimations();console.log("System validation result:",t?"PASSED":"ISSUES FOUND AND FIXED")}else console.error("WorkersAnimation component not assigned!")},s.testAnimationClipAnalysis=function(){console.log("\n=== Test 2: Animation Clip Analysis ===");var o=this.workersAnimation.getSystemStatus();console.log("Animation system status:",o)},s.testLoopModeConfiguration=function(){console.log("\n=== Test 3: Loop Mode Configuration ==="),this.workersAnimation.forceLoopMode=!0,console.log("Enabled forceLoopMode"),this.workersAnimation.startIdleMode()},s.testEnhancedIdleMode=function(){var o=this;console.log("\n=== Test 4: Enhanced Idle Mode ==="),this.workersAnimation.startIdleMode(),setTimeout((function(){var t=o.workersAnimation.getSystemStatus();console.log("Worker state after idle mode:",t)}),1e3)},s.testDifferentWrapModes=function(){var o=this;console.log("\n=== Test 5: Different Wrap Modes ==="),this.workersAnimation.stopAllWorkers(),console.log("Testing jiggle mode..."),this.workersAnimation.startJiggleMode(),setTimeout((function(){console.log("Testing racing mode..."),o.workersAnimation.startRacingMode()}),2e3)},s.testAnimationSpeeds=function(){console.log("\n=== Test 6: Animation Speed Testing ==="),this.workersAnimation.testIdleAnimationSpeeds()},s.testFinalValidation=function(){console.log("\n=== Test 7: Final Validation ===");var o=this.workersAnimation.getSystemStatus();console.log("Final System Status:",o);var t=this.workersAnimation.validateAndFixAnimations();console.log("Final validation result:",t?"PASSED":"ISSUES FOUND"),console.log("\n=== All Tests Complete ==="),console.log("WorkersAnimation enhanced loop system is ready for use!")},s.testIdleMode=function(){this.workersAnimation&&this.workersAnimation.startIdleMode()},s.testDebugState=function(){if(this.workersAnimation){var o=this.workersAnimation.getSystemStatus();console.log("WorkersAnimation debug state:",o)}},s.testValidateAnimations=function(){if(this.workersAnimation){console.log("Animation validation - checking system status...");var o=this.workersAnimation.getSystemStatus();console.log("System status:",o)}},s.testForceLoopMode=function(){this.workersAnimation&&(this.workersAnimation.forceLoopMode=!0,this.workersAnimation.startIdleMode(),console.log("Force loop mode enabled and idle mode started"))},s.testMonitorLooping=function(){if(this.workersAnimation){var o=this.workersAnimation.getSystemStatus();console.log("Monitoring animation looping - current status:",o)}},s.testForceRestartAnimations=function(){this.workersAnimation&&this.workersAnimation.forceRestartAllAnimationsWithLoop()},s.quickFixLoopingIssue=function(){this.workersAnimation&&(console.log("[WorkersAnimationTest] Applying quick fix for looping issue..."),this.workersAnimation.quickFixLoopingIssue(),console.log("[WorkersAnimationTest] Quick fix applied using new comprehensive method"))},s.testNewLoopSystem=function(){if(this.workersAnimation){console.log("[WorkersAnimationTest] Testing new loop management system..."),console.log("1. Testing validation...");var o=this.workersAnimation.validateAndFixAnimations();console.log("Validation result:",o),console.log("2. Testing force loop..."),this.workersAnimation.forceAllAnimationsToLoop(),console.log("3. Testing debug methods..."),this.workersAnimation.debugWorkerState(),this.workersAnimation.debugAllAnimationClips(),console.log("4. Testing monitoring..."),this.workersAnimation.startLoopMonitoring(),console.log("5. Testing idle mode..."),this.workersAnimation.startIdleMode(),console.log("[WorkersAnimationTest] New loop system test complete")}},s.testComprehensiveLoopFix=function(){var o=this;this.workersAnimation&&(console.log("[WorkersAnimationTest] Testing comprehensive loop fix..."),this.workersAnimation.forceLoopMode=!0,this.workersAnimation.enableEnhancedDebugging=!0,this.workersAnimation.idleAnimationSpeed=1.2,this.workersAnimation.quickFixLoopingIssue(),setTimeout((function(){console.log("=== LOOP FIX RESULTS ==="),o.workersAnimation.debugWorkerState(),o.workersAnimation.monitorAnimationLooping()}),3e3),console.log("[WorkersAnimationTest] Comprehensive loop fix test initiated"))},t}(a)).prototype,"workersAnimation",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),p=t(g.prototype,"autoRunTests",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),k=t(g.prototype,"testDelay",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 3}}),d=g))||d));s._RF.pop()}}}));

(function(r) {
  r('virtual:///prerequisite-imports/main', 'chunks:///_virtual/main'); 
})(function(mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
    return {
        setters: [function(_m) {
            var _exportObj = {};

            for (var _key in _m) {
              if (_key !== "default" && _key !== "__esModule") _exportObj[_key] = _m[_key];
            }
      
            _export(_exportObj);
        }],
        execute: function () { }
    };
    });
});