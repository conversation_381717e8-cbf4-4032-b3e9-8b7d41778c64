{"importBase": "import", "nativeBase": "native", "name": "main", "deps": ["resources"], "uuids": ["d1zZ+9xgdCnZXIewTr3MQ5", "06G572h55H74Vi60RVAE8F@f9941", "0aPMjS9gZDJbItEkjK7hTG@f9941", "0a5b974a6", "13sfuiLHZK1Z4qLqzc7+c7", "17T/sN2nZCaLqSPLACx9mb", "20J0Rupi5CMaxgtkAxKp57", "2fb1URYR9M44VqJ3BCfhIv", "32YVqkMGRHUppkIdpS4Ech", "81KY+byk9NobTGcxPKTdMA", "8eFCjx4RZJUJ9HM7OXMLn2", "baIUdvKGZPgZxNbjWTFuRI", "c1M9JrsOpIfJkidKQR2wzK", "cfgNPShvJPBLPsU0Q0qXOx", "e0W3QohDRKSLN0B6vtd2Lv", "e1FYrKEPVOhbPvWQ+efrTP", "e8vEB2YT1FqL9N+6DlpfGp", "f9u1wzfFdOWomv8cf/RISt", "fdjsU2o1RKF5x0TziDw3jI", "133nQtjM1B+7odYGhUfZhl@f9941", "14eyxtP2lCUoN9XYpDxUcn@f9941", "1edkAObptMerbhrAyVbT0z@f9941", "23sdztjc5G7YsfO0Gnd5Co@f9941", "28nC+vBpFAcKcS5eaTBWuR@f9941", "2clAANJEtAUL+klhwGaB99@f9941", "2cuV0WCF5PNKx6/nval0cv@f9941", "2dIn1gBNZAC6cexDc226pp@f9941", "2elAWVMhFAzql7qG371xHg@f9941", "33HdNCbR1OUYdn1/kJuMtu@f9941", "35Zrk7MdZNUKOSYdiUPuCX@f9941", "3b+FMPlaZDIJURk9bA7+yB@f9941", "42dSPjDztMJa3LmXSvuP6o@f9941", "45MccfLu5FB4msRdc1jPJq@f9941", "48bOCNRwZMC69Hr2lkDB9/@f9941", "503e6MV7hH/oEfFPdubTMJ@f9941", "57kWKLYOFH1KgFfFLU03rq@f9941", "5aUeYA5iZFsZk/ZvzvT4mx@f9941", "5bJr3geQ5DSKOoO5lkboxr@f9941", "629qaKe/tE4LUqK5qXbwWm@f9941", "64sQo1hjxIiIjldl6DBxBu", "75FNWad9JL0r4FOltI+8bu@f9941", "769X0u6wBNto+JKW5Z2bo4@f9941", "78FfzOvUNDKba/p45LCo97@f9941", "7apXR+DFdAP6Itt+UxiaHd@f9941", "7dj5uJT9FMn6OrOOx83tfK@f9941", "7dpp+6b2NHx7iup0EECrcq@f9941", "7fWhnaamFLj7ybXgf9nt9Q@f9941", "7f0jE0vJpHzaryrX3qhHjh@f9941", "83GvKVP49PzLo6wjuMSsYf", "89AmBHG+REw7IJ9Gr8nIf9@f9941", "8aXIWoD0JOGK30bzaqJ/9O@f9941", "8eFJxHUmFNF5/e3l9EhZC3@f9941", "8eLU3ZSJRBNrQzLiAw/4rE@f9941", "90q9ROWolB7qRk3YWzhabO@f9941", "9526YDBWhKP5aFjcMtkHIu", "97kZ6xZdJBYI/O+GdSnnmS@f9941", "9cZCH8M7NK7rSYx1xcfDbB@f9941", "9ehZ65DERHN5mnN0rbvLsZ@f9941", "a0gi+uC1VPAqVf3cWXuk47", "a1LtOqyh5I55+KGfUqaML7@f9941", "a71ZATHUBC+4oAUg8uo7/S@f9941", "aaB0RiplVCrLlNl5GjsFHd@f9941", "ad3FLKEaZDe571AvHexs9r@f9941", "ad7YYbBkxPJ4oRpFmG6YNZ@f9941", "aeYtvTnkVAH5fMy78j5bvt@f9941", "b1dS2DCyxGiJMnZNHCdzR2@f9941", "b53Al7FZxLzoD0BHq6SgUk", "b7/PcamhtHW5aGjF2fOaoN@f9941", "baLOTM+xdLFZHkwAF6Bfrq@f9941", "bb2nzl+E5AhJlGXFMaADoL@f9941", "bdESuSSYZPE4ij1YtVbLdv@f9941", "c2cnIEimBH9JD/fDmprO5E@f9941", "c5XlHkZ31KAL5yBrGQQvtn@f9941", "c9QqhdRdtD0pnhqu+tQdA7@f9941", "c9maSeQyRBxZ9sGyByYREz@f9941", "d8j08fFGlE6ZtNm5EB4fig@f9941", "d8ocnFKc5GK7QibhAnyxnP@f9941", "d9be1Fs8JPy4YjbVjN+rj5@f9941", "daZRS7XaZPuY4XpLNLC1YA", "dbBb3F/uZN75zHNs6uL1c/@f9941", "dbmLv5YHhNGKtL9FgjKwOK@f9941", "ddWHJQf8xJBaMaccaXUgHb@f9941", "deEz14hx9E0pgk5nTcGMwx@f9941", "dfUZlDiphMEpDrLQe9NFjd@f9941", "e4q2pR/15NzYXjifI0GWvz@f9941", "e68ut9LsxLMIimraHOe2mz@f9941", "e9tg9hwLFMhoHtZjXndCSi@f9941", "e90W+aMchHF6GYjZdSeGBj@f9941", "eb5Y0QN+5Ms6g6MPE7ij3I@f9941", "f8/G50lRBIGYVdrmSJoaJE@f9941"], "paths": {"0": ["db:/assets/Tower", 0, 1], "11": ["db:/internal/physics/default-physics-material", 2, 1], "18": ["db:/internal/default_renderpipeline/builtin-forward", 1, 1]}, "scenes": {"db://assets/Tower.scene": 0}, "packs": {"0a5b974a6": [11, 0, 18]}, "versions": {"import": [], "native": []}, "redirect": [1, "0", 2, "0", 4, "0", 19, "0", 20, "0", 5, "0", 21, "0", 6, "0", 22, "0", 23, "0", 24, "0", 25, "0", 26, "0", 27, "0", 7, "0", 8, "0", 28, "0", 29, "0", 30, "0", 31, "0", 32, "0", 33, "0", 34, "0", 35, "0", 36, "0", 37, "0", 38, "0", 39, "0", 40, "0", 41, "0", 42, "0", 43, "0", 44, "0", 45, "0", 46, "0", 47, "0", 9, "0", 48, "0", 49, "0", 50, "0", 10, "0", 51, "0", 52, "0", 53, "0", 54, "0", 55, "0", 56, "0", 57, "0", 58, "0", 59, "0", 60, "0", 61, "0", 62, "0", 63, "0", 64, "0", 65, "0", 66, "0", 67, "0", 68, "0", 69, "0", 70, "0", 12, "0", 71, "0", 72, "0", 73, "0", 74, "0", 13, "0", 75, "0", 76, "0", 77, "0", 79, "0", 80, "0", 81, "0", 82, "0", 83, "0", 14, "0", 15, "0", 84, "0", 85, "0", 16, "0", 86, "0", 87, "0", 88, "0", 89, "0", 17, "0"], "debug": false, "extensionMap": {".cconb": [10, 12, 13, 14, 15, 16, 17, 4, 5, 6, 7, 78, 8, 9]}, "hasPreloadScript": true, "dependencyRelationships": {}, "types": ["cc.SceneAsset", "cc.RenderPipeline", "cc.PhysicsMaterial"]}