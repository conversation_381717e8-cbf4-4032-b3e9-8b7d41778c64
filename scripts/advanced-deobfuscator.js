#!/usr/bin/env node

/*
 * @Date: 2025-10-05
 * @Description: Advanced deobfuscator for Cocos Creator reverse engineered code
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const readdir = promisify(fs.readdir);
const mkdir = promisify(fs.mkdir);

/**
 * Advanced code deobfuscator for Cocos Creator components
 */
class AdvancedDeobfuscator {
    constructor() {
        this.variableMapping = new Map();
        this.methodMapping = new Map();
        this.contextualNames = new Map();
    }

    /**
     * Process all TypeScript files
     */
    async processDirectory(inputDir, outputDir) {
        console.log(`🔍 Advanced deobfuscation: ${inputDir} → ${outputDir}`);
        
        await mkdir(outputDir, { recursive: true });

        const files = await readdir(inputDir);
        const tsFiles = files.filter(file => file.endsWith('.ts'));

        for (const file of tsFiles) {
            try {
                const inputPath = path.join(inputDir, file);
                const outputPath = path.join(outputDir, file);
                
                console.log(`🔧 Deobfuscating: ${file}`);
                await this.processFile(inputPath, outputPath);
            } catch (err) {
                console.error(`❌ Error processing ${file}:`, err.message);
            }
        }

        console.log(`✅ Advanced deobfuscation complete!`);
    }

    /**
     * Process a single file
     */
    async processFile(inputPath, outputPath) {
        const content = await readFile(inputPath, 'utf-8');
        const className = path.basename(inputPath, '.ts');
        
        // Analyze the code structure first
        const analysis = this.analyzeCode(content, className);
        
        // Deobfuscate based on analysis
        const deobfuscated = this.deobfuscateWithContext(content, analysis);
        
        await writeFile(outputPath, deobfuscated);
    }

    /**
     * Analyze code to understand structure
     */
    analyzeCode(code, className) {
        const analysis = {
            className: className,
            isComponent: code.includes('Component') || code.includes('ccclass'),
            hasProperties: code.includes('property'),
            hasDecorators: code.includes('ccclass') || code.includes('property'),
            methods: [],
            properties: [],
            imports: []
        };

        // Extract method patterns
        const methodMatches = code.matchAll(/([a-z])\.([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*function/g);
        for (const match of methodMatches) {
            analysis.methods.push(match[2]);
        }

        return analysis;
    }

    /**
     * Deobfuscate with contextual understanding
     */
    deobfuscateWithContext(code, analysis) {
        let result = code;

        // Step 1: Clean up the basic structure
        result = this.cleanBasicStructure(result);

        // Step 2: Reconstruct as proper TypeScript class
        result = this.reconstructTypeScriptClass(result, analysis);

        return result;
    }

    /**
     * Clean up basic code structure
     */
    cleanBasicStructure(code) {
        let cleaned = code;

        // Remove Cocos Creator runtime framework calls
        cleaned = cleaned.replace(/[a-z]\._RF\.push\([^)]*\);\s*/g, '');
        cleaned = cleaned.replace(/[a-z]\._RF\.pop\(\);\s*/g, '');

        // Clean up variable declarations
        cleaned = cleaned.replace(/var\s+[a-z,\s]+;/g, '');

        // Extract the main content (everything after the framework setup)
        const contentMatch = cleaned.match(/ccclass.*$/s);
        if (contentMatch) {
            cleaned = contentMatch[0];
        }

        return cleaned;
    }

    /**
     * Reconstruct as proper TypeScript class
     */
    reconstructTypeScriptClass(code, analysis) {
        const className = analysis.className;
        
        // Start building the proper TypeScript class
        let result = `/*
 * ${className} - Deobfuscated Cocos Creator Component
 * Auto-generated by cc-reverse advanced deobfuscator
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('${className}')
export class ${className} extends Component {
`;

        // Try to extract and format methods
        const methods = this.extractMethods(code);
        
        if (methods.length > 0) {
            result += '\n    // Extracted methods (may need manual cleanup)\n';
            methods.forEach(method => {
                result += `    ${method}\n\n`;
            });
        } else {
            result += `
    // Original minified code (needs manual deobfuscation):
    /*
    ${code.replace(/\*\//g, '*\\/')}
    */
    
    start() {
        // Component initialization
    }
    
    update(deltaTime: number) {
        // Component update logic
    }
`;
        }

        result += '}\n';

        return result;
    }

    /**
     * Extract methods from minified code
     */
    extractMethods(code) {
        const methods = [];
        
        // Common method patterns in the minified code
        const patterns = [
            // Pattern: i.methodName = function() { ... }
            /([a-z])\.([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*function\s*\([^)]*\)\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}/g,
            
            // Pattern: methodName: function() { ... }
            /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*function\s*\([^)]*\)\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}/g
        ];

        patterns.forEach(pattern => {
            const matches = code.matchAll(pattern);
            for (const match of matches) {
                if (match.length >= 3) {
                    const methodName = match[2] || match[1];
                    const methodBody = match[3] || match[2];
                    
                    if (methodName && methodBody) {
                        const cleanMethodName = this.getReadableMethodName(methodName);
                        const cleanMethodBody = this.cleanMethodBody(methodBody);
                        
                        methods.push(`${cleanMethodName}() {
        ${cleanMethodBody}
    }`);
                    }
                }
            }
        });

        return methods;
    }

    /**
     * Get readable method name
     */
    getReadableMethodName(name) {
        const commonMethods = {
            'start': 'start',
            'update': 'update',
            'onLoad': 'onLoad',
            'onEnable': 'onEnable',
            'onDisable': 'onDisable',
            'onDestroy': 'onDestroy'
        };

        return commonMethods[name] || name;
    }

    /**
     * Clean method body
     */
    cleanMethodBody(body) {
        let cleaned = body;

        // Basic formatting
        cleaned = cleaned.replace(/;/g, ';\n        ');
        cleaned = cleaned.replace(/\{/g, '{\n            ');
        cleaned = cleaned.replace(/\}/g, '\n        }');

        // Remove excessive whitespace
        cleaned = cleaned.replace(/\n\s*\n/g, '\n');
        cleaned = cleaned.trim();

        return cleaned || '// Method body needs manual deobfuscation';
    }
}

// Create a simple manual deobfuscator for AuthManager specifically
async function createReadableAuthManager() {
    const authManagerContent = `/*
 * AuthManager - Authentication and Token Management
 * Deobfuscated from reverse engineered code
 */

import { _decorator, Component } from 'cc';

const { ccclass, property } = _decorator;

interface AuthData {
    token: string;
    playerId: string;
    sessionId: string;
    balance: number;
    isAuthenticated: boolean;
    userProfile?: any;
    bettingLimits?: any;
}

interface AuthConfig {
    defaultPlayerId: string;
    sessionTimeout: number;
    defaultToken: string;
}

@ccclass('AuthManager')
export class AuthManager extends Component {
    private static _instance: AuthManager = null;
    
    private _authData: AuthData = {
        token: "",
        playerId: "",
        sessionId: "",
        balance: 0,
        isAuthenticated: false
    };
    
    private _config: AuthConfig = {
        defaultPlayerId: "player_1",
        sessionTimeout: 3600000, // 1 hour
        defaultToken: "GEdR9jerB1iLv3EwwwWaQ94xRygrtRchNOqbqU4Ctq3%2btDYtl9MU%2bS5pYqku8mV41gx4KKEgk4gI7t4iQCjo5gsVvieLH7b7hX%2bCf7lZw%2b0%3d"
    };

    static getInstance(): AuthManager {
        return this._instance;
    }

    start() {
        AuthManager._instance = this;
        console.log("[AuthManager] Initialized");
    }

    extractTokenFromURL(): string {
        try {
            const token = new URLSearchParams(window.location.search).get("token");
            if (token) {
                console.log("[AuthManager] Token extracted from URL");
                return decodeURIComponent(token);
            } else {
                console.log("[AuthManager] No token found in URL parameters");
                return "";
            }
        } catch (error) {
            console.error("[AuthManager] Failed to extract token from URL:", error);
            return "";
        }
    }

    initializeWithToken(token: string, playerId?: string): { success: boolean; error?: string } {
        if (token && token.trim().length !== 0) {
            console.log("[AuthManager] Initializing with provided token");
            return this.initializeAuth(token.trim(), playerId || this._config.defaultPlayerId);
        } else {
            return { success: false, error: "Token parameter is required" };
        }
    }

    validateToken(token: string): { valid: boolean; error?: string } {
        if (!token || token.trim().length === 0) {
            return { valid: false, error: "Token is empty" };
        }
        
        if (token.length < 50 || token.length > 200) {
            return { valid: false, error: "Token length is invalid" };
        }
        
        try {
            if (atob(token).length < 10) {
                return { valid: false, error: "Decoded token is too short" };
            } else {
                console.log("[AuthManager] Token validation successful");
                return { valid: true };
            }
        } catch (error) {
            return { valid: false, error: "Token is not valid base64" };
        }
    }

    initializeAuth(token: string, playerId: string): { success: boolean; error?: string } {
        const validation = this.validateToken(token);
        if (validation.valid) {
            this._authData.token = token;
            this._authData.playerId = playerId || "";
            this._authData.isAuthenticated = false;
            console.log("[AuthManager] Authentication initialized");
            return { success: true };
        } else {
            return { success: false, error: validation.error };
        }
    }

    setAuthenticationStatus(isAuthenticated: boolean, sessionId?: string, balance?: number) {
        this._authData.isAuthenticated = isAuthenticated;
        if (sessionId) {
            this._authData.sessionId = sessionId;
        }
        if (balance !== undefined) {
            this._authData.balance = balance;
        }
        console.log("[AuthManager] Authentication status updated:", isAuthenticated);
    }

    updateBalance(balance: number) {
        this._authData.balance = balance;
        console.log("[AuthManager] Balance updated:", balance);
    }

    getAuthData(): AuthData {
        return { ...this._authData };
    }

    getToken(): string {
        return this._authData.token;
    }

    isAuthenticated(): boolean {
        return this._authData.isAuthenticated && this._authData.token.length > 0;
    }

    getBalance(): number {
        return this._authData.balance;
    }

    clearAuth() {
        this._authData = {
            token: "",
            playerId: "",
            sessionId: "",
            balance: 0,
            isAuthenticated: false
        };
        console.log("[AuthManager] Authentication data cleared");
    }

    getAuthHeaders(): Record<string, string> {
        return {
            "Authorization": "Bearer " + this._authData.token,
            "X-Player-ID": this._authData.playerId,
            "X-Session-ID": this._authData.sessionId
        };
    }

    autoInitialize(): { success: boolean; error?: string } {
        const urlToken = this.extractTokenFromURL();
        if (urlToken) {
            console.log("[AuthManager] Using token from URL");
            return this.initializeAuth(urlToken, this._config.defaultPlayerId);
        }
        
        console.log("[AuthManager] No token found in URL parameters, trying default token");
        if (this._config.defaultToken) {
            console.log("[AuthManager] Using default token");
            const decodedToken = decodeURIComponent(this._config.defaultToken);
            const result = this.initializeAuth(decodedToken, this._config.defaultPlayerId);
            if (result.success) {
                return result;
            } else {
                console.error("[AuthManager] Default token validation failed:", result.error);
                return { success: false, error: "Default token validation failed: " + result.error };
            }
        }
        
        console.log("[AuthManager] No default token configured");
        return { success: false, error: "No token found in URL and no default token configured" };
    }
}`;

    await writeFile('./output-web-mobile-v2-readable/AuthManager.ts', authManagerContent);
    console.log('✅ Created readable AuthManager.ts');
}

// Main execution
async function main() {
    const deobfuscator = new AdvancedDeobfuscator();
    
    const inputDir = process.argv[2] || './output-web-mobile-v2/src';
    const outputDir = process.argv[3] || './output-web-mobile-v2-readable/src';
    
    if (!fs.existsSync(inputDir)) {
        console.error(`❌ Input directory not found: ${inputDir}`);
        process.exit(1);
    }
    
    try {
        // Create readable output directory
        await mkdir('./output-web-mobile-v2-readable', { recursive: true });
        
        // Create a manually deobfuscated AuthManager as example
        await createReadableAuthManager();
        
        // Process all files with advanced deobfuscator
        await deobfuscator.processDirectory(inputDir, outputDir);
        
        console.log('🎉 Advanced deobfuscation completed!');
        console.log('📁 Check ./output-web-mobile-v2-readable/ for readable code');
    } catch (err) {
        console.error('❌ Deobfuscation failed:', err);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { AdvancedDeobfuscator };
