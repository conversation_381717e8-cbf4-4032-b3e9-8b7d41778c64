#!/usr/bin/env node

/*
 * @Date: 2025-10-05
 * @Description: Validation script for Cocos Creator 3.8.x support
 */

const path = require('path');
const fs = require('fs');
const { codeAnalyzer } = require('../src/core/codeAnalyzer');
const { resourceProcessor } = require('../src/core/resourceProcessor');
const { projectGenerator } = require('../src/core/projectGenerator');
const { logger } = require('../src/utils/logger');

/**
 * Validation script for 3.8.x support
 */
class ValidationScript {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.passed = 0;
        this.total = 0;
    }

    /**
     * Run all validation tests
     */
    async run() {
        logger.info('开始验证 Cocos Creator 3.8.x 支持...');
        
        // Set up global test environment
        this.setupTestEnvironment();
        
        // Run validation tests
        this.validateCodeAnalyzer();
        this.validateResourceProcessor();
        this.validateProjectGenerator();
        this.validateVersionDetection();
        
        // Report results
        this.reportResults();
        
        return this.errors.length === 0;
    }

    /**
     * Set up test environment
     */
    setupTestEnvironment() {
        global.cocosVersion = '3.8.x';
        global.config = {
            output: { createMeta: true, prettify: true },
            codeGen: { language: "typescript", moduleType: "commonjs" },
            assets: { extractTextures: true, extractAudio: true }
        };
        global.paths = {
            output: './validation-output',
            ast: './validation-output/ast',
            res: './validation-resources'
        };
    }

    /**
     * Validate code analyzer functionality
     */
    validateCodeAnalyzer() {
        logger.info('验证代码分析器...');
        
        this.test('Code Analyzer - Parser Options', () => {
            const options = codeAnalyzer.getParserOptions();
            this.assert(options.plugins.includes('typescript'), 'Should include TypeScript plugin');
            this.assert(options.plugins.includes('decorators'), 'Should include decorators plugin');
            this.assert(options.sourceType === 'module', 'Should use module source type');
        });

        this.test('Code Analyzer - File Extension', () => {
            const extension = codeAnalyzer.getFileExtension();
            this.assert(extension === '.ts', 'Should generate .ts files for 3.8.x');
        });

        this.test('Code Analyzer - Metadata Template', () => {
            const meta = codeAnalyzer.getMetaTemplate('test-file');
            this.assert(meta.ver === '4.0.23', 'Should use 3.8.x metadata version');
            this.assert(meta.importer === 'typescript', 'Should use TypeScript importer');
            this.assert(meta.imported === true, 'Should mark as imported');
        });

        this.test('Code Analyzer - Generator Options', () => {
            const options = codeAnalyzer.getGeneratorOptions();
            this.assert(options.decoratorsBeforeExport === true, 'Should support decorators before export');
        });
    }

    /**
     * Validate resource processor functionality
     */
    validateResourceProcessor() {
        logger.info('验证资源处理器...');
        
        this.test('Resource Processor - Metadata Generation', () => {
            const audioMeta = resourceProcessor.generateMetaData('test-uuid', 'AudioClip');
            this.assert(audioMeta.ver === '4.0.23', 'Should use 3.8.x version for audio');
            this.assert(audioMeta.importer === 'audio-clip', 'Should use correct importer');
            this.assert(audioMeta.imported === true, 'Should mark as imported');
        });

        this.test('Resource Processor - Importer Types', () => {
            const importerTests = [
                ['Material', 'material'],
                ['EffectAsset', 'effect'],
                ['Texture2D', 'texture'],
                ['ImageAsset', 'image'],
                ['Mesh', 'mesh'],
                ['Skeleton', 'skeleton'],
                ['Prefab', 'prefab'],
                ['Scene', 'scene'],
                ['AnimationClip', 'animation-clip']
            ];

            importerTests.forEach(([assetType, expectedImporter]) => {
                const importer = resourceProcessor.getImporterType(assetType);
                this.assert(importer === expectedImporter, 
                    `${assetType} should map to ${expectedImporter}, got ${importer}`);
            });
        });
    }

    /**
     * Validate project generator functionality
     */
    validateProjectGenerator() {
        logger.info('验证项目生成器...');
        
        this.test('Project Generator - Version', () => {
            const version = projectGenerator.getProjectVersion();
            this.assert(version === '3.8.8', 'Should return 3.8.8 for 3.8.x projects');
        });

        this.test('Project Generator - Project Config', () => {
            const config = projectGenerator.createProjectConfig('TestProject');
            this.assert(config.engine === 'cocos-creator', 'Should use cocos-creator engine');
            this.assert(config.type === '3d', 'Should support 3D');
            this.assert(config.renderPipeline === 'builtin-forward', 'Should use forward pipeline');
            this.assert(config.name === 'TestProject', 'Should use provided name');
        });

        this.test('Project Generator - TypeScript Config', () => {
            const tsconfig = projectGenerator.createTSConfig();
            this.assert(tsconfig.compilerOptions.target === 'es6', 'Should target ES6');
            this.assert(tsconfig.compilerOptions.strict === true, 'Should enable strict mode');
            this.assert(tsconfig.compilerOptions.lib.includes('es2020'), 'Should include ES2020 lib');
            this.assert(tsconfig.compilerOptions.paths.cc !== undefined, 'Should include cc path mapping');
        });

        this.test('Project Generator - Project Settings', () => {
            const settings = projectGenerator.createProjectSettings();
            this.assert(Array.isArray(settings['group-list']), 'Group list should be array in 3.8.x');
            this.assert(settings.physics !== undefined, 'Should include physics settings');
            this.assert(settings.rendering !== undefined, 'Should include rendering settings');
            this.assert(settings.animation !== undefined, 'Should include animation settings');
        });

        this.test('Project Generator - Package.json', () => {
            const packageJson = projectGenerator.createPackageJson();
            this.assert(packageJson.name === 'cocos-creator-project', 'Should have correct name');
            this.assert(packageJson.scripts.build !== undefined, 'Should include build script');
            this.assert(packageJson.engines.node !== undefined, 'Should specify Node.js version');
        });
    }

    /**
     * Validate version detection functionality
     */
    validateVersionDetection() {
        logger.info('验证版本检测...');
        
        this.test('Version Detection - Global Variable', () => {
            this.assert(global.cocosVersion === '3.8.x', 'Should detect 3.8.x version');
        });
    }

    /**
     * Run a test with error handling
     */
    test(name, testFn) {
        this.total++;
        try {
            testFn();
            this.passed++;
            logger.debug(`✓ ${name}`);
        } catch (error) {
            this.errors.push(`✗ ${name}: ${error.message}`);
            logger.error(`✗ ${name}: ${error.message}`);
        }
    }

    /**
     * Assert a condition
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(message);
        }
    }

    /**
     * Report validation results
     */
    reportResults() {
        logger.info('\n=== 验证结果 ===');
        logger.info(`总测试数: ${this.total}`);
        logger.info(`通过: ${this.passed}`);
        logger.info(`失败: ${this.errors.length}`);
        logger.info(`警告: ${this.warnings.length}`);

        if (this.errors.length > 0) {
            logger.error('\n失败的测试:');
            this.errors.forEach(error => logger.error(error));
        }

        if (this.warnings.length > 0) {
            logger.warn('\n警告:');
            this.warnings.forEach(warning => logger.warn(warning));
        }

        if (this.errors.length === 0) {
            logger.success('\n🎉 所有验证测试通过！Cocos Creator 3.8.x 支持已成功实现。');
        } else {
            logger.error('\n❌ 验证失败，请检查上述错误。');
        }
    }
}

// Run validation if called directly
if (require.main === module) {
    const validator = new ValidationScript();
    validator.run().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        logger.error('验证过程中出现错误:', error);
        process.exit(1);
    });
}

module.exports = ValidationScript;
