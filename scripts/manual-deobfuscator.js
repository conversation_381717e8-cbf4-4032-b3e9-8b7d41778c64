#!/usr/bin/env node

/*
 * @Date: 2025-10-05
 * @Description: Manual deobfuscator for specific game components
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

/**
 * Manual deobfuscator for key game components
 */
class ManualDeobfuscator {
    
    /**
     * Create readable GameManager
     */
    async createReadableGameManager() {
        const gameManagerContent = `/*
 * GameManager - Main Game Controller
 * Manually deobfuscated from reverse engineered code
 */

import { _decorator, Component, Node } from 'cc';
import { SocketManager } from './SocketManager';
import { AuthManager } from './AuthManager';
import { SoundManager } from './SoundManager';
import { LocalizationManager } from './LocalizationManager';
import { EventManager } from './EventManager';
import { GameEvents } from './GameEvents';

const { ccclass, property } = _decorator;

enum GameStatus {
    WAITING = 'waiting',
    BETTING = 'betting',
    RACING = 'racing',
    FINISHED = 'finished'
}

enum PlayerType {
    A = 'A',
    P = 'P', 
    T = 'T'
}

interface CurrentBet {
    totalAmount: number;
    [key: string]: any;
}

@ccclass('GameManager')
export class GameManager extends Component {
    @property(SoundManager)
    soundManager: SoundManager = null;

    private static _instance: GameManager = null;
    
    // Core managers
    private socketManager: SocketManager = null;
    private authManager: AuthManager = null;
    private localizationManager: LocalizationManager = null;
    private eventTarget: any = null;
    
    // Game state
    private currentRound: number = 0;
    private currentRoundId: string = "";
    private gameStatus: GameStatus = GameStatus.WAITING;
    private timeRemains: number = 0;
    private balance: number = 0;
    private isConnected: boolean = false;
    private currentBet: CurrentBet = { totalAmount: 0 };
    private canBet: boolean = false;
    private bettingHistory: any[] = [];

    /**
     * Map symbol to player type
     */
    mapSymbolToPlayer(symbol: string): PlayerType | null {
        if (!symbol) return null;
        
        const trimmed = symbol.trim();
        if (!trimmed) return null;
        
        switch (trimmed.toUpperCase()) {
            case "아":
            case "A":
            case "PLAYER_A":
                return PlayerType.A;
            case "파":
            case "P":
            case "PLAYER_P":
                return PlayerType.P;
            case "트":
            case "T":
            case "PLAYER_T":
                return PlayerType.T;
            default:
                return null;
        }
    }

    static getInstance(): GameManager {
        return this._instance;
    }

    start() {
        GameManager._instance = this;
        this.initializeManagers();
        
        if (!this.setupEventListeners()) {
            console.log("[GameManager] Managers not ready, scheduling retry...");
            this.retryInitialization(1);
            return;
        }
        
        this.startGame();
    }

    /**
     * Start game with provided token
     */
    startWithToken(token: string, playerId?: string) {
        console.log("[GameManager] Starting game with provided token...");
        
        if (!token || token.trim().length === 0) {
            console.error("[GameManager] Token parameter is required");
            this.eventTarget.emit("token-required", {
                message: "Token parameter is required to start the game",
                suggestion: "Please provide a valid authentication token"
            });
            return;
        }
        
        this.startGameWithToken(token.trim(), playerId);
    }

    /**
     * Start game (async)
     */
    async startGame() {
        try {
            console.log("[GameManager] Starting game...");
            
            const authResult = this.authManager.autoInitialize();
            if (!authResult.success) {
                console.log("[GameManager] No URL token found");
                this.eventTarget.emit("token-required", {
                    message: "Please provide authentication token",
                    suggestion: "Use startWithToken(token) method or add token to URL"
                });
                return;
            }
            
            console.log("[GameManager] Authentication successful");
            await this.connectToGame();
            
        } catch (error) {
            console.error("[GameManager] Failed to start game:", error);
            this.eventTarget.emit("startup-failed", { error: error.message });
        }
    }

    /**
     * Connect to game server
     */
    async connectToGame() {
        // Implementation would connect to WebSocket server
        console.log("[GameManager] Connecting to game server...");
        // this.socketManager.connect();
    }

    /**
     * Initialize all managers
     */
    private initializeManagers() {
        // Initialize core managers
        this.authManager = AuthManager.getInstance();
        this.socketManager = SocketManager.getInstance();
        // ... other managers
    }

    /**
     * Setup event listeners
     */
    private setupEventListeners(): boolean {
        // Setup event listeners for game events
        return true;
    }

    /**
     * Retry initialization
     */
    private retryInitialization(attempt: number) {
        console.log(\`[GameManager] Retry initialization attempt \${attempt}\`);
        // Retry logic
    }

    /**
     * Start game with token
     */
    private startGameWithToken(token: string, playerId?: string) {
        const authResult = this.authManager.initializeWithToken(token, playerId);
        if (authResult.success) {
            this.connectToGame();
        } else {
            console.error("[GameManager] Token initialization failed:", authResult.error);
        }
    }

    // Getters and setters
    getCurrentRound(): number { return this.currentRound; }
    getGameStatus(): GameStatus { return this.gameStatus; }
    getBalance(): number { return this.balance; }
    isGameConnected(): boolean { return this.isConnected; }
    canPlaceBet(): boolean { return this.canBet; }
}`;

        await writeFile('./output-web-mobile-v2-readable/GameManager-clean.ts', gameManagerContent);
        console.log('✅ Created readable GameManager-clean.ts');
    }

    /**
     * Create readable TowerAnimation
     */
    async createReadableTowerAnimation() {
        const towerAnimationContent = `/*
 * TowerAnimation - Tower Racing Animation Controller
 * Manually deobfuscated from reverse engineered code
 */

import { _decorator, Component, Node, Vec3, Tween, tween } from 'cc';
import { GameEvents } from './GameEvents';
import { EventManager } from './EventManager';
import { WorkersAnimation } from './WorkersAnimation';
import { DynamicRandomPhaseConfig } from './DynamicRandomPhaseConfig';

const { ccclass, property } = _decorator;

enum TowerPlayer {
    A = 'A',
    P = 'P',
    T = 'T'
}

enum RacePhase {
    PHASE1 = 'phase1',
    PHASE2 = 'phase2',
    PHASE3 = 'phase3'
}

@ccclass('TowerAnimation')
export class TowerAnimation extends Component {
    @property(Node)
    towerNode: Node = null;

    @property(WorkersAnimation)
    workersAnimation: WorkersAnimation = null;

    @property({ tooltip: "Tower player identifier (A, P, T)" })
    towerPlayer: TowerPlayer = TowerPlayer.A;

    @property({ tooltip: "Horizontal jiggle amplitude", range: [0, 10, 0.1], slide: true })
    wobbleAmplitude: number = 2.0;

    @property({ tooltip: "Seconds between jiggle flips", range: [0.01, 0.5, 0.01], slide: true })
    wobbleInterval: number = 0.02;

    @property({ tooltip: "Optional smoothing window", range: [0, 0.5, 0.01], slide: true })
    wobbleSmoothing: number = 0.1;

    @property({ tooltip: "Strength of speed-follow behaviour", range: [0, 3, 0.1], slide: true })
    wobbleSpeedFollowStrength: number = 1.0;

    @property({ tooltip: "Reference vertical speed", range: [5, 80, 1], slide: true })
    wobbleSpeedReference: number = 20.0;

    @property({ tooltip: "Smoothing for tower speed sampling", range: [0, 1, 0.05], slide: true })
    wobbleSpeedSmoothing: number = 0.3;

    // Animation state
    private isRacing: boolean = false;
    private activeTween: Tween<Node> = null;
    private finalPosition: number = 1;
    private raceStartTime: number = 0;
    private currentPhase: RacePhase = RacePhase.PHASE1;
    private currentY: number = 0;

    // Wobble animation state
    private wobbleActive: boolean = false;
    private wobbleBaseX: number = 0;
    private wobbleOffset: number = 0;
    private wobbleTargetOffset: number = 0;
    private wobbleDirection: number = 1;
    private wobbleTimer: number = 0;
    private wobbleCurrentAmplitude: number = 0;
    private wobbleCurrentInterval: number = 0.02;
    private wobbleSmoothedSpeed: number = 0;
    private wobbleSampledY: number = 0;

    // Dynamic race configuration
    private dynamicRaceConfig: DynamicRandomPhaseConfig = null;
    private currentActionIndex: number = 0;
    private raceDurationMultiplier: number = 1;
    private plannedDurationMs: number = null;
    private countdownTween: Tween<Node> = null;

    // Constants (would be imported from a constants file)
    private static readonly RESET_Y = 0;
    private static readonly START_Y = 100;

    debugLog(...args: any[]) {
        // Only log if debug logging is enabled
        if (this.isDebugLoggingEnabled()) {
            console.log(...args);
        }
    }

    onLoad() {
        this.debugLog(\`[TowerAnimation] \${this.towerPlayer} onLoad() called - FORCING timing configuration\`);
        
        // Force timing configuration
        this.setTimingConfiguration(8, 10);
        
        if (this.towerNode) {
            this.resetPosition();
        }
    }

    /**
     * Reset tower to initial position
     */
    resetPosition() {
        if (!this.towerNode) return;

        this.cancelCountdownTween();
        
        this.currentY = TowerAnimation.RESET_Y;
        this.towerNode.setPosition(
            this.towerNode.position.x,
            this.currentY,
            this.towerNode.position.z
        );
        
        this.currentPhase = RacePhase.PHASE1;
        this.isRacing = false;
        
        // Reset wobble state
        this.wobbleBaseX = this.towerNode.position.x;
        this.wobbleOffset = 0;
        this.wobbleTargetOffset = 0;
        this.wobbleTimer = 0;
        this.wobbleDirection = -1;
        this.wobbleCurrentAmplitude = this.wobbleAmplitude;
        this.wobbleCurrentInterval = Math.max(0.005, this.wobbleInterval);
        this.wobbleSmoothedSpeed = 0;
        this.wobbleSampledY = this.currentY;
        
        this.applyTowerWobble(true);
        
        this.debugLog(\`[TowerAnimation] \${this.towerPlayer} reset to Y=\${this.currentY}\`);
    }

    /**
     * Cancel countdown tween
     */
    cancelCountdownTween() {
        if (this.countdownTween) {
            this.countdownTween.stop();
            this.countdownTween = null;
        }
    }

    /**
     * Animate tower to start position
     */
    animateToStartPosition(duration: number, delay?: number) {
        if (!this.towerNode || this.isRacing) return;

        const tweenDuration = this.resolveCountdownTweenDuration(duration, delay);
        if (tweenDuration === 0) return;

        const targetY = TowerAnimation.START_Y;
        const currentPos = this.towerNode.getPosition();

        if (Math.abs(currentPos.y - targetY) <= 0.01) {
            this.currentY = targetY;
            return;
        }

        this.cancelCountdownTween();
        
        this.countdownTween = tween(this.towerNode)
            .to(tweenDuration, {
                position: new Vec3(currentPos.x, targetY, currentPos.z)
            }, {
                easing: 'sineOut',
                onUpdate: () => {
                    this.currentY = this.towerNode.position.y;
                    this.wobbleSampledY = this.currentY;
                }
            })
            .call(() => {
                this.currentY = targetY;
                this.wobbleSampledY = this.currentY;
                this.countdownTween = null;
            })
            .start();
    }

    /**
     * Apply tower wobble effect
     */
    private applyTowerWobble(force: boolean = false) {
        // Wobble animation implementation
        if (this.towerNode) {
            const newX = this.wobbleBaseX + this.wobbleOffset;
            this.towerNode.setPosition(newX, this.towerNode.position.y, this.towerNode.position.z);
        }
    }

    private resolveCountdownTweenDuration(duration: number, delay?: number): number {
        // Calculate actual tween duration based on parameters
        return duration || 1.0;
    }

    private setTimingConfiguration(param1: number, param2: number) {
        // Set timing configuration for animations
        this.debugLog(\`[TowerAnimation] Setting timing configuration: \${param1}, \${param2}\`);
    }

    private isDebugLoggingEnabled(): boolean {
        // Check if debug logging is enabled (would check global performance settings)
        return true; // For now, always enable
    }
}`;

        await writeFile('./output-web-mobile-v2-readable/TowerAnimation-clean.ts', towerAnimationContent);
        console.log('✅ Created readable TowerAnimation-clean.ts');
    }

    /**
     * Create readable SocketManager
     */
    async createReadableSocketManager() {
        const socketManagerContent = `/*
 * SocketManager - WebSocket Communication Manager
 * Manually deobfuscated from reverse engineered code
 */

import { _decorator, Component } from 'cc';
import { EventManager } from './EventManager';
import { AuthManager } from './AuthManager';

const { ccclass, property } = _decorator;

interface SocketConfig {
    url: string;
    reconnectInterval: number;
    maxReconnectAttempts: number;
    heartbeatInterval: number;
}

@ccclass('SocketManager')
export class SocketManager extends Component {
    private static _instance: SocketManager = null;
    
    private socket: WebSocket = null;
    private isConnected: boolean = false;
    private isConnecting: boolean = false;
    private reconnectAttempts: number = 0;
    private heartbeatTimer: number = null;
    private reconnectTimer: number = null;
    
    private config: SocketConfig = {
        url: 'wss://game-server.example.com/ws',
        reconnectInterval: 5000,
        maxReconnectAttempts: 10,
        heartbeatInterval: 30000
    };

    static getInstance(): SocketManager {
        return this._instance;
    }

    start() {
        SocketManager._instance = this;
        console.log("[SocketManager] Initialized");
    }

    /**
     * Connect to WebSocket server
     */
    connect(url?: string): Promise<boolean> {
        return new Promise((resolve, reject) => {
            if (this.isConnected || this.isConnecting) {
                resolve(this.isConnected);
                return;
            }

            const socketUrl = url || this.config.url;
            console.log(\`[SocketManager] Connecting to \${socketUrl}\`);
            
            this.isConnecting = true;
            this.socket = new WebSocket(socketUrl);
            
            this.socket.onopen = () => {
                console.log("[SocketManager] Connected successfully");
                this.isConnected = true;
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.startHeartbeat();
                resolve(true);
            };
            
            this.socket.onmessage = (event) => {
                this.handleMessage(event.data);
            };
            
            this.socket.onclose = () => {
                console.log("[SocketManager] Connection closed");
                this.handleDisconnection();
                resolve(false);
            };
            
            this.socket.onerror = (error) => {
                console.error("[SocketManager] Connection error:", error);
                this.isConnecting = false;
                reject(error);
            };
        });
    }

    /**
     * Disconnect from server
     */
    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        this.isConnected = false;
        this.isConnecting = false;
        this.stopHeartbeat();
        this.stopReconnect();
    }

    /**
     * Send message to server
     */
    send(message: any): boolean {
        if (!this.isConnected || !this.socket) {
            console.warn("[SocketManager] Cannot send message - not connected");
            return false;
        }

        try {
            const data = typeof message === 'string' ? message : JSON.stringify(message);
            this.socket.send(data);
            return true;
        } catch (error) {
            console.error("[SocketManager] Failed to send message:", error);
            return false;
        }
    }

    /**
     * Handle incoming messages
     */
    private handleMessage(data: string) {
        try {
            const message = JSON.parse(data);
            console.log("[SocketManager] Received message:", message);
            
            // Route message to appropriate handler
            EventManager.getInstance().emit('socket-message', message);
            
        } catch (error) {
            console.error("[SocketManager] Failed to parse message:", error);
        }
    }

    /**
     * Handle disconnection
     */
    private handleDisconnection() {
        this.isConnected = false;
        this.isConnecting = false;
        this.stopHeartbeat();
        
        EventManager.getInstance().emit('socket-disconnected');
        
        // Attempt reconnection
        if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }

    /**
     * Schedule reconnection attempt
     */
    private scheduleReconnect() {
        this.reconnectAttempts++;
        console.log(\`[SocketManager] Scheduling reconnect attempt \${this.reconnectAttempts}\`);
        
        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, this.config.reconnectInterval);
    }

    /**
     * Start heartbeat
     */
    private startHeartbeat() {
        this.heartbeatTimer = setInterval(() => {
            this.send({ type: 'ping', timestamp: Date.now() });
        }, this.config.heartbeatInterval);
    }

    /**
     * Stop heartbeat
     */
    private stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    /**
     * Stop reconnection timer
     */
    private stopReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }

    // Getters
    getConnectionStatus(): boolean { return this.isConnected; }
    getReconnectAttempts(): number { return this.reconnectAttempts; }
}`;

        await writeFile('./output-web-mobile-v2-readable/SocketManager-clean.ts', socketManagerContent);
        console.log('✅ Created readable SocketManager-clean.ts');
    }
}

// Main execution
async function main() {
    const deobfuscator = new ManualDeobfuscator();
    
    try {
        await mkdir('./output-web-mobile-v2-readable', { recursive: true });
        
        console.log('🔧 Creating manually deobfuscated components...');
        
        await deobfuscator.createReadableGameManager();
        await deobfuscator.createReadableTowerAnimation();
        await deobfuscator.createReadableSocketManager();
        
        console.log('🎉 Manual deobfuscation completed!');
        console.log('📁 Check ./output-web-mobile-v2-readable/ for clean, readable code');
        console.log('');
        console.log('📋 Created files:');
        console.log('  - AuthManager.ts (authentication system)');
        console.log('  - GameManager-clean.ts (main game controller)');
        console.log('  - TowerAnimation-clean.ts (tower racing animations)');
        console.log('  - SocketManager-clean.ts (WebSocket communication)');
        
    } catch (err) {
        console.error('❌ Manual deobfuscation failed:', err);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { ManualDeobfuscator };
