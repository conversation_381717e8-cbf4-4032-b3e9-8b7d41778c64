#!/usr/bin/env node

/*
 * @Date: 2025-10-05
 * @Description: Filter and organize actual game components from build artifacts
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const readdir = promisify(fs.readdir);
const mkdir = promisify(fs.mkdir);
const copyFile = promisify(fs.copyFile);

/**
 * Game component filter and organizer
 */
class GameComponentFilter {
    constructor() {
        // Files that are build artifacts and should be excluded
        this.buildArtifacts = [
            'rollupPluginModLoBabelHelpers_js.ts',
            'builtin_pipeline.ts',
            'builtin_pipeline_settings.ts', 
            'builtin_pipeline_types.ts',
            'internal.ts',
            'index.ts',
            'main.ts',
            'resources.ts',
            'module_0.ts',
            'module_1.ts'
        ];

        // Core game components that are essential
        this.coreGameComponents = [
            'AuthManager.ts',
            'GameManager.ts',
            'SocketManager.ts',
            'EventManager.ts',
            'SoundManager.ts',
            'LocalizationManager.ts',
            'ErrorHandler.ts'
        ];

        // Game logic components
        this.gameLogicComponents = [
            'TowerAnimation.ts',
            'TowerAnimationController.ts',
            'WorkersAnimation.ts',
            'WorkersAnimationTest.ts',
            'DynamicPhaseTest.ts',
            'DynamicRandomPhaseConfig.ts',
            'TimingOptimizationTest.ts',
            'CardSkin.ts',
            'GameEvents.ts'
        ];

        // UI components
        this.uiComponents = [
            'PopupManager.ts',
            'StartPopupView.ts',
            'RankPopupView.ts',
            'RoundInfoPanel.ts',
            'FakeLoadingView.ts',
            'LoadingScene.ts',
            'HistoryItemView.ts',
            'SoundToggleButton.ts'
        ];

        // Spine animation components
        this.spineComponents = [
            'SpineInitializer.ts',
            'SpineIntegrationExample.ts',
            'SpineMemoryManager.ts',
            'SpineStartupManager.ts',
            'OptimizedSpineComponent.ts',
            'SpiteSkin.ts'
        ];

        // Utility and debug components
        this.utilityComponents = [
            'Singleton.ts',
            'WebCommunicationManager.ts',
            'WorkerAnimationDebugger.ts'
        ];
    }

    /**
     * Filter and organize game components
     */
    async filterAndOrganize(inputDir, outputDir) {
        console.log('🎯 Filtering game components from build artifacts...');
        
        await mkdir(outputDir, { recursive: true });
        
        const files = await readdir(inputDir);
        const tsFiles = files.filter(file => file.endsWith('.ts'));
        
        // Create organized directory structure
        await this.createDirectoryStructure(outputDir);
        
        // Process each file
        for (const file of tsFiles) {
            const category = this.categorizeFile(file);
            
            if (category === 'build-artifact') {
                console.log(`🗑️  Skipping build artifact: ${file}`);
                continue;
            }
            
            const inputPath = path.join(inputDir, file);
            const outputPath = this.getOutputPath(outputDir, file, category);
            
            console.log(`📁 ${category}: ${file}`);
            await copyFile(inputPath, outputPath);
        }
        
        // Create summary
        await this.createComponentSummary(outputDir);
        
        console.log('✅ Game component filtering complete!');
    }

    /**
     * Categorize file by type
     */
    categorizeFile(filename) {
        if (this.buildArtifacts.includes(filename)) {
            return 'build-artifact';
        }
        if (this.coreGameComponents.includes(filename)) {
            return 'core';
        }
        if (this.gameLogicComponents.includes(filename)) {
            return 'game-logic';
        }
        if (this.uiComponents.includes(filename)) {
            return 'ui';
        }
        if (this.spineComponents.includes(filename)) {
            return 'spine';
        }
        if (this.utilityComponents.includes(filename)) {
            return 'utility';
        }
        return 'other';
    }

    /**
     * Create organized directory structure
     */
    async createDirectoryStructure(outputDir) {
        const dirs = [
            'core',
            'game-logic', 
            'ui',
            'spine',
            'utility',
            'other'
        ];
        
        for (const dir of dirs) {
            await mkdir(path.join(outputDir, dir), { recursive: true });
        }
    }

    /**
     * Get output path for file based on category
     */
    getOutputPath(outputDir, filename, category) {
        return path.join(outputDir, category, filename);
    }

    /**
     * Create component summary
     */
    async createComponentSummary(outputDir) {
        const summary = `# 🎮 Game Components Summary

## 📁 Organized Structure

### 🔧 Core System Components (${this.coreGameComponents.length} files)
Essential game systems and managers:
${this.coreGameComponents.map(f => `- **${f}** - ${this.getComponentDescription(f)}`).join('\n')}

### 🎯 Game Logic Components (${this.gameLogicComponents.length} files)
Core gameplay mechanics:
${this.gameLogicComponents.map(f => `- **${f}** - ${this.getComponentDescription(f)}`).join('\n')}

### 🖼️ UI Components (${this.uiComponents.length} files)
User interface and views:
${this.uiComponents.map(f => `- **${f}** - ${this.getComponentDescription(f)}`).join('\n')}

### 🎭 Spine Animation Components (${this.spineComponents.length} files)
Advanced character animations:
${this.spineComponents.map(f => `- **${f}** - ${this.getComponentDescription(f)}`).join('\n')}

### 🛠️ Utility Components (${this.utilityComponents.length} files)
Helper classes and utilities:
${this.utilityComponents.map(f => `- **${f}** - ${this.getComponentDescription(f)}`).join('\n')}

## 🗑️ Excluded Build Artifacts (${this.buildArtifacts.length} files)
These files are build system artifacts and not needed for game development:
${this.buildArtifacts.map(f => `- ~~${f}~~ - Build artifact`).join('\n')}

## 🎯 Development Focus

**Start with these core components:**
1. **AuthManager.ts** - Authentication and user management
2. **GameManager.ts** - Main game controller
3. **TowerAnimation.ts** - Core racing mechanics
4. **SocketManager.ts** - Real-time communication

**Then expand to:**
- Game logic components for racing mechanics
- UI components for user interface
- Spine components for advanced animations

## 📊 Component Statistics

| Category | Count | Purpose |
|----------|-------|---------|
| Core Systems | ${this.coreGameComponents.length} | Essential game infrastructure |
| Game Logic | ${this.gameLogicComponents.length} | Racing and gameplay mechanics |
| UI Components | ${this.uiComponents.length} | User interface and views |
| Spine Animation | ${this.spineComponents.length} | Advanced character animations |
| Utilities | ${this.utilityComponents.length} | Helper classes and tools |
| **Total Game Code** | **${this.coreGameComponents.length + this.gameLogicComponents.length + this.uiComponents.length + this.spineComponents.length + this.utilityComponents.length}** | **Actual game components** |
| ~~Build Artifacts~~ | ~~${this.buildArtifacts.length}~~ | ~~Excluded from development~~ |

## 🚀 Next Steps

1. **Focus on Core** - Start with the core/ directory components
2. **Understand Game Logic** - Review game-logic/ components for racing mechanics  
3. **Customize UI** - Modify ui/ components for your needs
4. **Enhance Animations** - Use spine/ components for character animations
5. **Ignore Build Artifacts** - Don't worry about the excluded files

The filtered components represent the actual game code you need for development!`;

        await writeFile(path.join(outputDir, 'COMPONENT_SUMMARY.md'), summary);
    }

    /**
     * Get component description
     */
    getComponentDescription(filename) {
        const descriptions = {
            'AuthManager.ts': 'Authentication and token management',
            'GameManager.ts': 'Main game controller and state management',
            'SocketManager.ts': 'WebSocket communication with server',
            'EventManager.ts': 'Event system and messaging',
            'SoundManager.ts': 'Audio system management',
            'LocalizationManager.ts': 'Multi-language support',
            'ErrorHandler.ts': 'Error handling and recovery',
            'TowerAnimation.ts': 'Tower racing animation system',
            'TowerAnimationController.ts': 'Animation coordination',
            'WorkersAnimation.ts': 'Worker character animations',
            'DynamicPhaseTest.ts': 'Animation testing framework',
            'DynamicRandomPhaseConfig.ts': 'Dynamic race configuration',
            'TimingOptimizationTest.ts': 'Performance testing',
            'CardSkin.ts': 'Card rendering and animation',
            'GameEvents.ts': 'Game event definitions',
            'PopupManager.ts': 'Popup window management',
            'StartPopupView.ts': 'Game start interface',
            'RankPopupView.ts': 'Ranking display',
            'RoundInfoPanel.ts': 'Round information display',
            'FakeLoadingView.ts': 'Loading screen',
            'LoadingScene.ts': 'Scene loading management',
            'HistoryItemView.ts': 'Game history display',
            'SoundToggleButton.ts': 'Audio controls',
            'SpineInitializer.ts': 'Spine animation system setup',
            'SpineIntegrationExample.ts': 'Spine integration examples',
            'SpineMemoryManager.ts': 'Memory optimization for animations',
            'SpineStartupManager.ts': 'Spine system initialization',
            'OptimizedSpineComponent.ts': 'Performance-optimized Spine rendering',
            'SpiteSkin.ts': 'Additional skin system',
            'Singleton.ts': 'Singleton pattern implementation',
            'WebCommunicationManager.ts': 'Web communication layer',
            'WorkerAnimationDebugger.ts': 'Animation debugging tools'
        };
        
        return descriptions[filename] || 'Game component';
    }
}

// Main execution
async function main() {
    const filter = new GameComponentFilter();
    
    const inputDir = process.argv[2] || './output-web-mobile-v2-readable/src';
    const outputDir = process.argv[3] || './game-components-clean';
    
    if (!fs.existsSync(inputDir)) {
        console.error(`❌ Input directory not found: ${inputDir}`);
        process.exit(1);
    }
    
    try {
        await filter.filterAndOrganize(inputDir, outputDir);
        
        console.log('');
        console.log('🎉 Game component filtering completed!');
        console.log(`📁 Clean game components saved to: ${outputDir}`);
        console.log('📋 Check COMPONENT_SUMMARY.md for detailed breakdown');
        
    } catch (err) {
        console.error('❌ Filtering failed:', err);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { GameComponentFilter };
