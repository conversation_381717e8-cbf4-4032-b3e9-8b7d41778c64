#!/usr/bin/env node

/*
 * @Date: 2025-10-05
 * @Description: Code beautifier and deobfuscator for reverse engineered TypeScript files
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const readdir = promisify(fs.readdir);

/**
 * Code beautifier and deobfuscator
 */
class CodeBeautifier {
    constructor() {
        this.variableMap = new Map();
        this.functionMap = new Map();
        this.classMap = new Map();
        this.counter = 0;
    }

    /**
     * Beautify all TypeScript files in a directory
     */
    async beautifyDirectory(inputDir, outputDir) {
        console.log(`🎨 Beautifying TypeScript files from ${inputDir} to ${outputDir}`);
        
        // Ensure output directory exists
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const files = await readdir(inputDir);
        const tsFiles = files.filter(file => file.endsWith('.ts'));

        console.log(`Found ${tsFiles.length} TypeScript files to beautify`);

        for (const file of tsFiles) {
            try {
                const inputPath = path.join(inputDir, file);
                const outputPath = path.join(outputDir, file);
                
                console.log(`📝 Processing: ${file}`);
                await this.beautifyFile(inputPath, outputPath);
            } catch (err) {
                console.error(`❌ Error processing ${file}:`, err.message);
            }
        }

        console.log(`✅ Beautification complete! Files saved to ${outputDir}`);
    }

    /**
     * Beautify a single TypeScript file
     */
    async beautifyFile(inputPath, outputPath) {
        const content = await readFile(inputPath, 'utf-8');
        const beautified = this.beautifyCode(content, path.basename(inputPath));
        await writeFile(outputPath, beautified);
    }

    /**
     * Beautify and deobfuscate code
     */
    beautifyCode(code, filename) {
        let beautified = code;

        // Step 1: Basic formatting
        beautified = this.formatBasicStructure(beautified);

        // Step 2: Deobfuscate variable names
        beautified = this.deobfuscateVariables(beautified, filename);

        // Step 3: Format functions and classes
        beautified = this.formatFunctionsAndClasses(beautified);

        // Step 4: Clean up and format
        beautified = this.finalCleanup(beautified);

        return beautified;
    }

    /**
     * Format basic code structure
     */
    formatBasicStructure(code) {
        let formatted = code;

        // Add line breaks after semicolons
        formatted = formatted.replace(/;(?!\s*$)/g, ';\n');

        // Add line breaks after opening braces
        formatted = formatted.replace(/\{(?!\s*$)/g, '{\n');

        // Add line breaks before closing braces
        formatted = formatted.replace(/(?<!^\s*)\}/g, '\n}');

        // Fix comma spacing
        formatted = formatted.replace(/,(?!\s)/g, ', ');

        // Fix operator spacing
        formatted = formatted.replace(/([=!<>+\-*\/])(?!=)/g, ' $1 ');
        formatted = formatted.replace(/\s+([=!<>+\-*\/])\s+/g, ' $1 ');

        return formatted;
    }

    /**
     * Deobfuscate variable names based on context
     */
    deobfuscateVariables(code, filename) {
        let deobfuscated = code;

        // Extract class name from filename
        const className = path.basename(filename, '.ts');

        // Common patterns and their replacements
        const patterns = [
            // Cocos Creator specific
            { pattern: /n\._RF\.push/g, replacement: 'cc._RF.push' },
            { pattern: /n\._RF\.pop/g, replacement: 'cc._RF.pop' },
            { pattern: /cclegacy\._RF/g, replacement: 'cc._RF' },
            
            // Class and decorator patterns
            { pattern: /u\.ccclass/g, replacement: '_decorator.ccclass' },
            { pattern: /u\.property/g, replacement: '_decorator.property' },
            { pattern: /i\.ccclass/g, replacement: '_decorator.ccclass' },
            { pattern: /i\.property/g, replacement: '_decorator.property' },
            
            // Common variable names
            { pattern: /\bt\(/g, replacement: `${className}(` },
            { pattern: /function\s+([a-z])\(/g, replacement: 'function $1Method(' },
            
            // Method patterns
            { pattern: /\.prototype\s*=\s*/g, replacement: '.prototype = ' },
            { pattern: /return\s+([a-z])\s*\}/g, replacement: 'return $1;\n}' },
        ];

        patterns.forEach(({ pattern, replacement }) => {
            deobfuscated = deobfuscated.replace(pattern, replacement);
        });

        return deobfuscated;
    }

    /**
     * Format functions and classes
     */
    formatFunctionsAndClasses(code) {
        let formatted = code;

        // Format function declarations
        formatted = formatted.replace(/function\s*\(/g, 'function(');
        formatted = formatted.replace(/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g, 'function $1(');

        // Format class methods
        formatted = formatted.replace(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*function\s*\(/g, '$1: function(');

        // Format arrow functions
        formatted = formatted.replace(/=>\s*\{/g, ' => {');

        return formatted;
    }

    /**
     * Final cleanup and formatting
     */
    finalCleanup(code) {
        let cleaned = code;

        // Remove excessive whitespace
        cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');
        cleaned = cleaned.replace(/^\s+$/gm, '');

        // Fix indentation (basic)
        const lines = cleaned.split('\n');
        let indentLevel = 0;
        const indentSize = 4;

        const formattedLines = lines.map(line => {
            const trimmed = line.trim();
            if (!trimmed) return '';

            // Decrease indent for closing braces
            if (trimmed.startsWith('}')) {
                indentLevel = Math.max(0, indentLevel - 1);
            }

            const indented = ' '.repeat(indentLevel * indentSize) + trimmed;

            // Increase indent for opening braces
            if (trimmed.endsWith('{')) {
                indentLevel++;
            }

            return indented;
        });

        cleaned = formattedLines.join('\n');

        // Add proper TypeScript structure
        cleaned = this.addTypeScriptStructure(cleaned);

        return cleaned;
    }

    /**
     * Add proper TypeScript class structure
     */
    addTypeScriptStructure(code) {
        let structured = code;

        // Try to identify and format class structure
        if (structured.includes('ccclass') || structured.includes('Component')) {
            // This looks like a Cocos Creator component
            structured = this.formatCocosComponent(structured);
        }

        return structured;
    }

    /**
     * Format Cocos Creator component structure
     */
    formatCocosComponent(code) {
        let formatted = code;

        // Add proper imports at the top
        const imports = [
            "import { _decorator, Component } from 'cc';",
            "",
            "const { ccclass, property } = _decorator;",
            ""
        ].join('\n');

        // Try to extract the main class
        const classMatch = code.match(/ccclass\s*\(\s*["']([^"']+)["']\s*\)/);
        if (classMatch) {
            const className = classMatch[1];
            
            // Basic class structure
            const classStructure = `
@ccclass('${className}')
export class ${className} extends Component {
    // Properties and methods will be here
    // Note: This is a deobfuscated version - manual cleanup needed
    
    ${formatted}
}`;

            formatted = imports + classStructure;
        }

        return formatted;
    }
}

// Main execution
async function main() {
    const beautifier = new CodeBeautifier();
    
    const inputDir = process.argv[2] || './output-web-mobile-v2/src';
    const outputDir = process.argv[3] || './output-web-mobile-v2-beautified/src';
    
    if (!fs.existsSync(inputDir)) {
        console.error(`❌ Input directory not found: ${inputDir}`);
        process.exit(1);
    }
    
    try {
        await beautifier.beautifyDirectory(inputDir, outputDir);
        console.log('🎉 Code beautification completed successfully!');
    } catch (err) {
        console.error('❌ Beautification failed:', err);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { CodeBeautifier };
